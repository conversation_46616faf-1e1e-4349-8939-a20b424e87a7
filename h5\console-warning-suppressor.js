/**
 * 控制台警告抑制器 - 立即执行版本
 * 专门处理第三方库（如 TinyMCE）产生的性能警告
 * 这些警告通常无法直接修复，但会影响开发体验
 * 必须在所有其他脚本之前执行
 */

(function() {
    'use strict';

    // 立即保存原始控制台方法，防止被其他脚本覆盖
    const originalConsole = {
        warn: console.warn.bind(console),
        error: console.error.bind(console),
        log: console.log.bind(console)
    };

    // 配置：需要抑制的警告类型
    const SUPPRESSED_WARNINGS = [
        '[Violation]Avoid using document.write()',
        '[Violation]Added non-passive event listener',
        'touchstart',
        'touchmove',
        'scroll-blocking',
        'fontawesome-webfont.woff2',
        'net::ERR_ABORTED 404',
        'HEAD http://localhost:3002/admin/font-awesome/fonts/fontawesome-webfont.woff2',
        '404 (Not Found)'
    ];

    // 配置：需要抑制的错误类型
    const SUPPRESSED_ERRORS = [
        'fontawesome-webfont.woff2',
        '404 (Not Found)',
        'HEAD http://localhost:3002/admin/font-awesome/fonts/fontawesome-webfont.woff2'
    ];
    
    // 立即重写控制台方法（在页面加载前就生效）
    
    // 检查消息是否应该被抑制
    function shouldSuppressMessage(message, suppressList) {
        if (typeof message !== 'string') {
            message = String(message);
        }
        
        return suppressList.some(pattern => {
            return message.includes(pattern);
        });
    }
    
    // 立即重写 console.warn
    console.warn = function(...args) {
        const message = args.join(' ');

        if (shouldSuppressMessage(message, SUPPRESSED_WARNINGS)) {
            // 静默处理，不显示在控制台
            return;
        }

        // 其他警告正常显示
        return originalConsole.warn.apply(console, arguments);
    };

    // 立即重写 console.error
    console.error = function(...args) {
        const message = args.join(' ');

        if (shouldSuppressMessage(message, SUPPRESSED_ERRORS)) {
            // 静默处理，不显示在控制台
            return;
        }

        // 其他错误正常显示
        return originalConsole.error.apply(console, arguments);
    };

    // 拦截 XMLHttpRequest 错误（立即生效）
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        const originalOnError = this.onerror;
        const originalOnLoad = this.onload;

        // 重写错误处理
        this.onerror = function(e) {
            if (typeof url === 'string' && url.includes('fontawesome-webfont.woff2')) {
                // 静默处理字体文件错误
                return;
            }
            if (originalOnError) {
                return originalOnError.call(this, e);
            }
        };

        this.onload = function(e) {
            if (this.status === 404 && typeof url === 'string' && url.includes('fontawesome-webfont.woff2')) {
                // 静默处理字体文件 404
                return;
            }
            if (originalOnLoad) {
                return originalOnLoad.call(this, e);
            }
        };

        return originalXHROpen.call(this, method, url, ...args);
    };
    
    // 拦截网络错误（针对字体文件 404）
    function interceptNetworkErrors() {
        // 拦截 fetch 错误
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            return originalFetch.apply(this, args).catch(error => {
                const url = args[0];
                if (typeof url === 'string' && url.includes('fontawesome-webfont.woff2')) {
                    // 静默处理字体文件 404 错误
                    return Promise.reject(new Error('Font file not found (suppressed)'));
                }
                return Promise.reject(error);
            });
        };
        
        // 拦截 XMLHttpRequest 错误
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this.addEventListener('error', function(e) {
                if (typeof url === 'string' && url.includes('fontawesome-webfont.woff2')) {
                    // 静默处理字体文件错误
                    e.stopPropagation();
                    e.preventDefault();
                }
            });
            
            return originalXHROpen.call(this, method, url, ...args);
        };
    }
    
    // 优化 TinyMCE 相关的性能问题
    function optimizeTinyMCEPerformance() {
        // 等待 TinyMCE 加载
        const checkTinyMCE = setInterval(function() {
            if (typeof tinymce !== 'undefined') {
                clearInterval(checkTinyMCE);
                
                // 添加全局配置优化
                if (tinymce.util && tinymce.util.Delay) {
                    // 优化延迟处理
                    const originalDelay = tinymce.util.Delay;
                    tinymce.util.Delay = function(callback, time) {
                        // 减少不必要的延迟
                        return originalDelay(callback, Math.min(time || 0, 100));
                    };
                }
                
                // 监听编辑器初始化
                tinymce.on('AddEditor', function(e) {
                    const editor = e.editor;
                    
                    editor.on('init', function() {
                        // 编辑器初始化完成后的优化
                        const container = editor.getContainer();
                        if (container) {
                            // 添加性能优化类
                            container.classList.add('tinymce-optimized');
                            
                            // 优化触摸事件
                            optimizeTouchEvents(container);
                        }
                    });
                });
            }
        }, 100);
        
        // 10秒后停止检查
        setTimeout(() => clearInterval(checkTinyMCE), 10000);
    }
    
    // 优化触摸事件
    function optimizeTouchEvents(container) {
        // 检测被动事件监听器支持
        let supportsPassive = false;
        try {
            const opts = Object.defineProperty({}, 'passive', {
                get: function() { supportsPassive = true; }
            });
            window.addEventListener('test', null, opts);
            window.removeEventListener('test', null, opts);
        } catch (e) {}
        
        if (supportsPassive && container) {
            // 为容器添加被动触摸事件监听器
            ['touchstart', 'touchmove', 'touchend'].forEach(eventType => {
                container.addEventListener(eventType, function() {
                    // 被动监听器，不阻止默认行为
                }, { passive: true });
            });
        }
    }
    
    // 添加 CSS 优化
    function addPerformanceCSS() {
        const style = document.createElement('style');
        style.textContent = `
            /* TinyMCE 性能优化样式 */
            .tinymce-optimized {
                transform: translateZ(0); /* 启用硬件加速 */
                will-change: transform; /* 提示浏览器优化 */
            }
            
            .tinymce-optimized * {
                touch-action: manipulation; /* 优化触摸响应 */
            }
            
            /* 隐藏 TinyMCE 通知 */
            .tox-notifications-container {
                display: none !important;
            }
            
            /* 优化字体渲染 */
            .fa, [class^="fa-"], [class*=" fa-"] {
                font-display: swap;
                text-rendering: optimizeSpeed;
            }
        `;
        document.head.appendChild(style);
    }
    
    // 初始化所有优化
    function initializeOptimizations() {
        interceptNetworkErrors();
        optimizeTinyMCEPerformance();
        addPerformanceCSS();
        
        console.log('🚀 性能优化已启用：控制台警告抑制器');
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeOptimizations);
    } else {
        initializeOptimizations();
    }
    
    // 导出恢复方法（用于调试）
    window.restoreConsole = function() {
        console.warn = originalConsole.warn;
        console.error = originalConsole.error;
        console.log = originalConsole.log;
        console.log('✅ 控制台已恢复正常显示');
    };
    
})();
