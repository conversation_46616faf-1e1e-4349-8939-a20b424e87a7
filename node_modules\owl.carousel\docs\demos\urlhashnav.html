<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Url Hash Navigation usage demo">
    <meta name="author" content="<PERSON>">
    <title>
      Url Hash Navigation Demo | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/demos/demos.html">Demos</a> 
              </li>
              <li> <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a>  </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>Url Hash Navigation</h1>
        </div>
      </div>
    </section>

    <!--  Demos -->
    <section id="demos">
      <div class="row">
        <div class="large-12 columns">
          <div class="owl-carousel owl-theme">
            <div class="item" data-hash="zero">
              <h4>0</h4>
            </div>
            <div class="item" data-hash="one">
              <h4>1</h4>
            </div>
            <div class="item" data-hash="two">
              <h4>2</h4>
            </div>
            <div class="item" data-hash="three">
              <h4>3</h4>
            </div>
            <div class="item" data-hash="four">
              <h4>4</h4>
            </div>
            <div class="item" data-hash="five">
              <h4>5</h4>
            </div>
            <div class="item" data-hash="six">
              <h4>6</h4>
            </div>
            <div class="item" data-hash="seven">
              <h4>7</h4>
            </div>
            <div class="item" data-hash="eight">
              <h4>8</h4>
            </div>
            <div class="item" data-hash="nine">
              <h4>9</h4>
            </div>
            <div class="item" data-hash="ten">
              <h4>10</h4>
            </div>
            <div class="item" data-hash="eleven">
              <h4>11</h4>
            </div>
            <div class="item" data-hash="tweleve">
              <h4>12</h4>
            </div>
            <div class="item" data-hash="thirteen">
              <h4>13</h4>
            </div>
            <div class="item" data-hash="fourteen">
              <h4>14</h4>
            </div>
            <div class="item" data-hash="fifteen">
              <h4>15</h4>
            </div>
          </div>
          <hr>
          <a class="button secondary url" href="#zero">zero</a> 
          <a class="button secondary url" href="#three">three</a> 
          <a class="button secondary url" href="#five">five</a> 
          <a class="button secondary url" href="#seven">seven</a> 
          <a class="button secondary url" href="#ten">ten</a> 
          <h3 id="overview">Overview</h3>
          <blockquote>
            <p>URLhashListener option is listening for url hash change and is looking for slide with the same data name e.g. <code>data-hash=&quot;zero&quot;</code></p>
          </blockquote>
          <p>Also <code>startPosition</code> option accept string: <code>&#39;URLHash&#39;</code>. This will load corresponding items on startup. Browser history back button is also affected.</p>
          <h3 id="setup">Setup</h3>
          <pre><code>    $(&#39;.owl-carousel&#39;).owlCarousel({
        items:4,
        loop:false,
        center:true,
        margin:10,
        URLhashListener:true,
        autoplayHoverPause:true,
        startPosition: &#39;URLHash&#39;
    });</code></pre>
          <script>
            $(document).ready(function() {
              $('.owl-carousel').owlCarousel({
                items: 4,
                loop: false,
                center: true,
                margin: 10,
                callbacks: true,
                URLhashListener: true,
                autoplayHoverPause: true,
                startPosition: 'URLHash'
              });
            })
          </script>
        </div>
      </div>
    </section>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>