/**
 * 新闻数据加载和显示管理
 */

// 当前语言环境（默认中文）
let currentLang = 'zh';

// 分页相关变量
let currentPage = 1;
let pageSize = 6; // 每页显示6条新闻
let totalPages = 1;
let allNewsData = []; // 存储所有新闻数据

// 初始化函数
function initNewsData() {
    // 检测当前页面语言
    detectPageLanguage();
    
    // 获取URL中的页码参数
    const urlParams = new URLSearchParams(window.location.search);
    const pageParam = urlParams.get('page');
    if (pageParam && !isNaN(parseInt(pageParam))) {
        currentPage = parseInt(pageParam);
    }
    
    // 加载新闻数据
    loadNewsData();
}

// 检测页面语言
function detectPageLanguage() {
    // 首先检查是否有window.currentLanguage设置
    if (window.currentLanguage) {
        currentLang = window.currentLanguage;
        return;
    }

    // 获取URL中的语言参数
    const urlParams = new URLSearchParams(window.location.search);
    const langParam = urlParams.get('lang');
    
    // 如果URL中有语言参数，使用该参数
    if (langParam) {
        currentLang = langParam.toLowerCase();
    } else {
        // 否则检查HTML标签的lang属性
        const htmlLang = document.documentElement.lang;
        if (htmlLang) {
            currentLang = htmlLang.toLowerCase().includes('zh') ? 'zh' : 'en';
        }
    }
}

// 加载新闻数据
function loadNewsData() {
    console.log('当前语言:', currentLang); // 调试日志

    try {
        // 构建API请求参数
        const params = {
            info_type: 'company_news_detail', // 恢复原来的info_type
            lang: currentLang === 'zh' ? 0 : 1,
            show: true
        };
        
        console.log('请求参数:', params); // 调试日志
        console.log('请求URL:', '/apis/company_info/list');
        
        // 先获取总数
        $.ajax({
            url: '/apis/company_info/list',
            method: 'GET',
            data: {
                filters: JSON.stringify(params),
                page: 1,
                page_size: 1 // 只获取一条记录，主要是为了获取total
            },
            success: function(initialResponse) {
                if (initialResponse.status === 'ok' && initialResponse.total) {
                    const totalNews = initialResponse.total;
                    console.log('后台新闻总数:', totalNews);
                    
                    // 计算需要请求的页数
                    const apiPageSize = 10; // API服务器每页返回10条
                    const totalPages = Math.ceil(totalNews / apiPageSize);
                    console.log('需要请求的页数:', totalPages);
                    
                    // 存储所有新闻数据
                    let allData = [];
                    let loadedPages = 0;
                    
                    // 请求每一页数据
                    for (let page = 1; page <= totalPages; page++) {
                        $.ajax({
                            url: '/apis/company_info/list',
                            method: 'GET',
                            async: false, // 使用同步请求确保按顺序获取
                            data: {
                                filters: JSON.stringify(params),
                                page: page,
                                page_size: apiPageSize
                            },
                            success: function(pageResponse) {
                                if (pageResponse.status === 'ok' && pageResponse.data) {
                                    console.log(`获取第${page}页数据:`, pageResponse.data.length);
                                    allData = allData.concat(pageResponse.data);
                                    loadedPages++;
                                    
                                    // 所有页面都加载完成
                                    if (loadedPages === totalPages) {
                                        processAllNewsData(allData, totalNews);
                                    }
                                } else {
                                    console.error(`获取第${page}页数据失败:`, pageResponse.msg);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error(`请求第${page}页数据失败:`, error);
                                console.error('状态码:', xhr.status);
                            }
                        });
                    }
                } else {
                    console.error('获取新闻总数失败:', initialResponse.msg);
                    showErrorMessage();
                }
            },
            error: function(xhr, status, error) {
                console.error('请求新闻总数失败:', error);
                console.error('状态码:', xhr.status);
                console.error('响应文本:', xhr.responseText);
                showErrorMessage();
            }
        });
    } catch (e) {
        console.error('加载新闻数据时发生异常:', e);
        showErrorMessage();
    }
}

// 处理获取到的所有新闻数据
function processAllNewsData(newsData, totalCount) {
    console.log('获取到新闻数据条数:', newsData.length);
    // 检查是否获取了所有数据
    if (newsData.length < totalCount) {
        console.warn('⚠️ 警告：API返回的数据不完整！');
        console.warn(`实际总数: ${totalCount}, 获取到: ${newsData.length}`);
    } else {
        console.log('✓ 成功获取所有新闻数据');
    }
    
    // 添加后台数据导入统计信息
    console.log('===== 后台数据导入统计 =====');
    console.log('API返回新闻总数:', newsData.length);
    console.log('后台实际新闻总数:', totalCount);
    console.log('每页显示数量:', pageSize);
    console.log('计算总页数:', Math.ceil(newsData.length / pageSize));
    console.log('===========================');
    
    // 按显示顺序排序
    allNewsData = newsData.sort((a, b) => 
        (a.display_order || 100) - (b.display_order || 100)
    );
    
    // 计算总页数
    totalPages = Math.ceil(allNewsData.length / pageSize);
    
    // 确保当前页码有效
    if (currentPage < 1) currentPage = 1;
    if (currentPage > totalPages) currentPage = totalPages;
    
    // 获取当前页的数据
    const currentPageData = allNewsData.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );
    
    // 添加当前页数据统计信息
    console.log('===== 当前页数据统计 =====');
    console.log('当前页码:', currentPage);
    console.log('当前页数据起始索引:', (currentPage - 1) * pageSize);
    console.log('当前页数据结束索引:', Math.min(currentPage * pageSize, allNewsData.length));
    console.log('当前页实际显示条数:', currentPageData.length);
    console.log('===========================');
    
    // 渲染新闻列表
    renderNewsList(currentPageData);
    
    // 渲染分页控件
    renderPagination();
}

// 渲染新闻列表
function renderNewsList(newsData) {
    console.log('渲染新闻数据:', newsData);
    const newsListContainer = $('#newsList');
    
    if (!newsListContainer.length) {
        console.error('找不到新闻列表容器元素 #newsList');
        return;
    }
    
    newsListContainer.empty();
    
    if (!newsData || newsData.length === 0) {
        showNoDataMessage(newsListContainer);
        return;
    }
    
    // 遍历新闻数据创建新闻项
    newsData.forEach((news, index) => {
        const newsItem = createNewsItem(news, index === 0);
        newsListContainer.append(newsItem);
    });
}

// 创建单个新闻项
function createNewsItem(news, isFirstItem) {
    console.log('创建新闻项:', news); // 调试日志

    try {
        // 处理图片路径
        let imagePath = news.image_path || './images/news/placeholder.svg';
        if (imagePath.includes('C:')) {
            // 处理Windows路径
            imagePath = '/uploads/' + imagePath.split('\\').pop();
        } else if (!imagePath.startsWith('http') && !imagePath.startsWith('/')) {
            imagePath = '/' + imagePath;
        }
        console.log('处理后的图片路径:', imagePath);

        // 处理内容中的换行符，分离时间和详情
        let timeContent = '';
        let detailContent = '';
        
        if (news.content) {
            console.log('原始内容:', news.content);
            const contentParts = news.content.split('\n');
            console.log('内容分割后的部分数量:', contentParts.length);
            
            if (contentParts.length > 1) {
                // 使用第一行作为时间内容
                timeContent = contentParts[0].trim();
                // 使用剩余部分作为详情内容
                detailContent = contentParts.slice(1).join('\n').trim();
            } else {
                // 如果没有换行符，使用全部内容作为时间
                timeContent = news.content.trim();
            }
        } else {
            console.log('新闻项没有内容，使用创建时间');
            // 如果没有内容，使用创建时间
            const date = new Date(news.created_at || news.updated_at);
            timeContent = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        }
        
        console.log('处理后的时间内容:', timeContent);
        console.log('处理后的详情内容:', detailContent);

        // 使用新的HTML结构
        const newsItemHtml = `
            <div class="news-item${isFirstItem ? ' first-item' : ''}">
                <div class="news-date">${timeContent}</div>
                <div class="news-content-wrapper">
                    <div class="news-image-container">
                        <img src="${imagePath}" alt="${news.title || ''}" class="news-image" onerror="this.src='./images/news/placeholder.svg'">
                    </div>
                    <div class="news-info">
                        <h2 class="news-title">
                            <a href="${news.url || `news-detail.html?id=${news.id}`}">${news.title || (currentLang === 'en' ? 'Untitled' : '无标题')}</a>
                        </h2>
                        <div class="news-desc">${detailContent}</div>
                        <a href="${news.url || `news-detail.html?id=${news.id}`}" class="news-more">${currentLang === 'zh' ? '了解详情' : 'Learn More'}</a>
                    </div>
                </div>
            </div>
        `;
        
        return newsItemHtml;
    } catch (e) {
        console.error('创建新闻项时发生异常:', e);
        console.error('问题新闻项数据:', news);
        
        // 返回一个错误提示的新闻项
        return `            <div class="news-item">
                <div class="news-info" style="text-align: center; color: #ff0000;">
                    <p>新闻项加载失败</p>
                </div>
            </div>
        `;
    }
}

// 显示无数据消息
function showNoDataMessage(container) {
    const message = currentLang === 'zh' ? 
        '暂无新闻动态' : 
        'No news available';
    
    container.html(`
        <div class="news-item">
            <div class="news-info" style="text-align: center;">
                <p>${message}</p>
            </div>
        </div>
    `);
}

// 显示错误消息
function showErrorMessage() {
    const message = currentLang === 'zh' ? 
        '加载新闻数据失败，请稍后重试' : 
        'Failed to load news data. Please try again later.';
    
    const newsListContainer = $('#newsList');
    if (newsListContainer.length) {
        newsListContainer.html(`
            <div class="news-item">
                <div class="news-info" style="text-align: center; color: #ff0000;">
                    <p>${message}</p>
                </div>
            </div>
        `);
    } else {
        console.error('找不到新闻列表容器元素 #newsList');
    }
}

// 添加分页控件渲染函数
function renderPagination() {
    let paginationContainer = $('.pagination-container');
    if (paginationContainer.length === 0) {
        $('#newsList').after('<div class="pagination-container"></div>');
        paginationContainer = $('.pagination-container');
    }
    
    let paginationHtml = '<div class="pagination">';
    
    // 上一页箭头（旋转180度）
    paginationHtml += `
        <a class="pagination-arrow ${currentPage === 1 ? 'disabled' : ''}" 
           href="javascript:void(0)" 
           onclick="${currentPage > 1 ? `changePage(${currentPage - 1})` : ''}"
        ><img src="/images/home/<USER>" alt="上一页" style="transform: rotate(180deg);"></a>
    `;
    
    // 页码
    const displayTotalPages = Math.max(1, totalPages);
    for (let i = 1; i <= displayTotalPages; i++) {
        paginationHtml += `
            <a class="pagination-number${i === currentPage ? ' active' : ''}" 
               href="javascript:void(0)" 
               onclick="changePage(${i})"
            >${i}</a>
        `;
    }
    
    // 下一页箭头（原始方向）
    paginationHtml += `
        <a class="pagination-arrow ${currentPage >= displayTotalPages ? 'disabled' : ''}" 
           href="javascript:void(0)" 
           onclick="${currentPage < displayTotalPages ? `changePage(${currentPage + 1})` : ''}"
        ><img src="/images/home/<USER>" alt="下一页"></a>
    `;
    
    // 跳转到指定页
    paginationHtml += `
        <div class="pagination-goto">
            <input type="text" class="goto-input" id="pageNumberInput" 
                   placeholder="${currentLang === 'zh' ? '跳转到' : 'Jump'}" 
                   min="1" 
                   max="${displayTotalPages}">
            <button class="goto-button" onclick="gotoPage()">GO</button>
        </div>
    `;
    
    paginationHtml += '</div>';
    
    paginationContainer.html(paginationHtml);
    
    // 添加输入框事件处理
    $('#pageNumberInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter键
            gotoPage();
        }
    }).on('focus', function() {
        // 当输入框获得焦点时，清空输入框的值
        $(this).val('');
    }).on('blur', function() {
        // 当输入框失去焦点且为空时，恢复当前页码
        if (!$(this).val()) {
            $(this).val(currentPage);
        }
    });
}

// 修改gotoPage函数
function gotoPage() {
    const pageInput = document.getElementById('pageNumberInput');
    if (!pageInput) return;
    
    let page = parseInt(pageInput.value);
    if (isNaN(page)) {
        pageInput.value = currentPage;
        return;
    }
    
    // 确保页码在有效范围内
    page = Math.max(1, Math.min(page, totalPages));
    pageInput.value = page;
    
    if (page !== currentPage) {
        changePage(page);
    }
}

// 添加页面切换函数
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;
    
    currentPage = page;
    
    // 更新URL，不刷新页面
    const url = new URL(window.location.href);
    url.searchParams.set('page', currentPage);
    window.history.pushState({page: currentPage}, '', url);
    
    // 获取当前页的数据
    const currentPageData = allNewsData.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );
    
    // 渲染新闻列表
    renderNewsList(currentPageData);
    
    // 更新分页控件
    renderPagination();
    
    // 滚动到页面顶部
    window.scrollTo(0, 0);
}

// 将changePage函数暴露给全局作用域
window.changePage = changePage;

// 页面加载完成后初始化
$(document).ready(function() {
    console.log('页面已加载，准备初始化新闻数据...');
    
    // 检查jQuery是否正确加载
    if (typeof $ === 'undefined') {
        console.error('jQuery未加载，无法初始化新闻数据');
        return;
    }
    
    // 检查新闻列表容器是否存在
    if ($('#newsList').length === 0) {
        console.error('找不到新闻列表容器元素 #newsList');
    } else {
        console.log('找到新闻列表容器元素 #newsList');
    }
    
    // 初始化新闻数据
    initNewsData();
}); 
