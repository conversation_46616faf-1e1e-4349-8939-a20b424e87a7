---
title: <PERSON> Width Demo
subTitle: Auto Width
nav: demos
description: Auto Width 
sort: 6

tags: 
- demo
- core
---

<div class="owl-carousel owl-theme">
	<div class="item" style="width:250px"><h4>1</h4></div>
	<div class="item" style="width:100px"><h4>2</h4></div>
	<div class="item" style="width:500px"><h4>3</h4></div>
	<div class="item" style="width:100px"><h4>4</h4></div>
	<div class="item" style="width:50px"><h4>6</h4></div>
	<div class="item" style="width:250px"><h4>7</h4></div>
	<div class="item" style="width:120px"><h4>8</h4></div>
	<div class="item" style="width:420px"><h4>9</h4></div>
	<div class="item" style="width:120px"><h4>10</h4></div>
	<div class="item" style="width:300px"><h4>11</h4></div>
	<div class="item" style="width:450px"><h4>12</h4></div>
	<div class="item" style="width:220px"><h4>13</h4></div>
	<div class="item" style="width:150px"><h4>14</h4></div>
	<div class="item" style="width:600px"><h4>15</h4></div>
</div>

{{#markdown }}
### Overview

Use width style on elements to get the result you want. If using with infinity loop add option 'items' more than 1. It all depends on the width of your content.

### Setup
```
$('.owl-carousel').owlCarousel({
	margin:10,
	loop:true,
	autoWidth:true,
	items:4
})
```
### html
```
<div class="owl-carousel owl-theme">
	<div class="item" style="width:250px"><h4>1</h4></div>
	<div class="item" style="width:100px"><h4>2</h4></div>
	<div class="item" style="width:500px"><h4>3</h4></div>
	<div class="item" style="width:100px"><h4>4</h4></div>
	<div class="item" style="width:50px"><h4>6</h4></div>
	<div class="item" style="width:250px"><h4>7</h4></div>
	<div class="item" style="width:120px"><h4>8</h4></div>
	<div class="item" style="width:420px"><h4>9</h4></div>
	<div class="item" style="width:120px"><h4>10</h4></div>
	<div class="item" style="width:300px"><h4>11</h4></div>
	<div class="item" style="width:450px"><h4>12</h4></div>
	<div class="item" style="width:220px"><h4>13</h4></div>
	<div class="item" style="width:150px"><h4>14</h4></div>
	<div class="item" style="width:600px"><h4>15</h4></div>
</div>
```

{{/markdown }} 

<script>
$(document).ready(function(){
	$('.owl-carousel').owlCarousel({
		margin:10,
		loop:true,
		autoWidth:true,
		items:4
	})
})
</script>