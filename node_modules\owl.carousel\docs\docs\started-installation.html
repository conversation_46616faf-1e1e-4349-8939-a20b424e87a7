<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Owl Carousel Documentation">
    <meta name="author" content="<PERSON>">
    <title>
      Installation | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li> <a href="/OwlCarousel2/demos/demos.html">Demos</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a> 
              </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>Getting Started</h1>
        </div>
      </div>
    </section>
    <div id="docs">
      <div class="row">
        <div class="small-12 medium-3 large-3 columns">
          <ul class="side-nav">
            <li class="side-nav-head">Getting Started</li>
            <li> <a href="started-welcome.html">Welcome</a>  </li>
            <li> <a href="started-installation.html">Installation</a>  </li>
            <li> <a href="started-faq.html">FAQ</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">API</li>
            <li> <a href="api-options.html">Options</a>  </li>
            <li> <a href="api-classes.html">Classes</a>  </li>
            <li> <a href="api-events.html">Events</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Development</li>
            <li> <a href="dev-buildin-plugins.html">Built-in Plugins</a>  </li>
            <li> <a href="dev-plugin-api.html">Plugin API</a>  </li>
            <li> <a href="dev-styles.html">Sass Styles</a>  </li>
            <li> <a href="dev-external.html">External Libs</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Support</li>
            <li> <a href="support-contributing.html">Contributing</a>  </li>
            <li> <a href="support-changelog.html">Changelog</a>  </li>
            <li> <a href="support-contact.html">Contact</a>  </li>
          </ul>
        </div>
        <div class="small-12 medium-9 large-9 columns">
          <article class="docs-content">
            <h2 id="installation">Installation</h2>
            <hr>
            <h3 id="include-css">Include CSS</h3>
            <p>First, include two CSS files into your HTML head:</p>
            <pre><code>&lt;link rel=&quot;stylesheet&quot; href=&quot;owlcarousel/owl.carousel.min.css&quot;&gt;
&lt;link rel=&quot;stylesheet&quot; href=&quot;owlcarousel/owl.theme.default.min.css&quot;&gt;</code></pre>
            <blockquote>
              <p><code>owl.carousel.css</code> file is required and should be included before any *.js files.</p>
            </blockquote>
            <p>Second <code>owl.theme.default.css</code> file is optional and feel free to edit it. However, it is required if you&#39;d like the default nav controls like dots or next buttons. Inside the source package you can also find
              <a href="http://sass-lang.com/">SCSS</a>  files for easy generation of your own themes.</p>
            <h3 id="include-js">Include JS</h3>
            <p>Yep, include jQuery and <code>owl.carousel.min.js</code> into the footer.</p>
            <pre><code>&lt;script src=&quot;jquery.min.js&quot;&gt;&lt;/script&gt;
&lt;script src=&quot;owlcarousel/owl.carousel.min.js&quot;&gt;&lt;/script&gt;</code></pre>
            <h3 id="set-html">Set HTML</h3>
            <p>You don&#39;t need any special markup. All you need is to wrap your divs(owl works with any type element a/img/span..) inside the container element <code>&lt;div class=&quot;owl-carousel&quot;&gt;</code>. Class &quot;owl-carousel&quot; is
              mandatory to apply proper styles that come from owl.carousel.css file. If you want the default nav controls like dots or buttons, you must also include the &quot;owl-theme&quot; class on that same div.</p>
            <pre><code>&lt;!-- Set up your HTML --&gt;
&lt;div class=&quot;owl-carousel&quot;&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
  &lt;div&gt; Your Content &lt;/div&gt;
&lt;/div&gt;</code></pre>
            <h3 id="call-the-plugin">Call the plugin</h3>
            <p>Now call the Owl initializer function and your carousel is ready.</p>
            <pre><code>$(document).ready(function(){
  $(&quot;.owl-carousel&quot;).owlCarousel();
});</code></pre>
            <blockquote>
              <p>See
                <a href="/OwlCarousel2/demos/demos.html">demos</a>  for customisation and options usage.</p>
            </blockquote>
            <h3 id="next-step">Next Step</h3>
            <h4 id="-faq-started-faq-html-">
              <a href="started-faq.html">FAQ</a> 
            </h4>
          </article>
        </div>
      </div>
    </div>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>