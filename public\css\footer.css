/* Footer CSS
==================================== */

footer{
    background: #00509F;
    height: 540px;
    padding: 72px 360px 80px;
    position: relative;
}

footer a {
    color:#fff;
}

footer a:hover {
    color:#eee;
    text-decoration:none;
}

footer h1, footer h2, footer h3, footer h4, footer h5, footer h6{
    color: #FFFFFF;
}

footer address {
    line-height:1.6em;
}

footer h5 a:hover, footer a:hover {
    text-decoration:none;
}

/* 链接列表样式 */
ul.link-list{
    margin: 0;
    padding: 0;
    list-style: none;
}

ul.link-list li{
    margin: 0;
    padding: 2px 0 2px 0;
    list-style: none;
}

footer ul.link-list li a{
    color: #FFFFFF;
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 14px;
    line-height: 28px;
    text-align: left;
    text-decoration: none;
}

footer ul.link-list li a:hover {
    color: #eee;
    text-decoration: underline;
}

/* 页脚内容链接列表样式 */
footer ul.link-list {
    width: auto;
    height: auto;
    margin-bottom: 20px;
}

/* 页脚导入文字内容样式 - 按用户要求设置 */
footer ul.link-list li a,
footer ul.link-list li,
.office-address div,
#contact_info_container li {
    width: 56px;
    height: auto; /* 自适应高度 */
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 地址行超出宽度设置 */
.office-address div {
    width: auto;
    max-width: 100%;
}

/* 联系方式和产品项目宽度调整 */
#contact_info_container li,
#product_solution_container li,
#service_support_container li {
    width: auto;
    max-width: 100%;
}

/* 链接列表宽度 */
#product_solution_container,
#service_support_container,
#contact_info_container {
    width: 100%;
}

/* 二维码下面的文字不应用上述样式 */
.qrcode-labels span {
    font-family: "Source Han Sans CN";
    font-size: 12px;
    color: #FFFFFF;
    text-align: center;
}

/* 联系方式列表样式 */
#contact_info_container {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* 地址行特殊样式 */
li.address-line {
    display: flex !important;
    flex-wrap: wrap;
}

/* 地址前缀的字间距控制 */
.address-prefix-char {
    display: inline-block;
    width: 14px; /* 与文字大小相同 */
}

.address-prefix-char.spacer {
    width: 14px; /* 占位符宽度与文字相同 */
    visibility: hidden; /* 隐藏但保持占位 */
}

.address-prefix {
    display: inline-flex;
    align-items: center;
    width: 60px;
}

/* 移除旧的margin设置 */
.address-prefix-char.first-char {
    margin-right: 0;
}

/* 地址内容样式 - 确保与冒号有适当间距 */
.address-content {
    display: inline-block;
    white-space: nowrap;
    position: relative;
    left: 5px;
}

/* 联系部分整体容器 */
.contact-section {
    margin-bottom: 10px;
}

/* 办公地址样式 */
.office-address {
    max-width: none;  /* 移除最大宽度限制 */
    width: 100%;      /* 使用完整可用宽度 */
    overflow: visible; /* 允许内容溢出 */
    position: relative; /* 支持绝对定位 */
    z-index: 1;        /* 确保内容在上层 */
}

.office-address div {
    max-width: none;  /* 移除最大宽度限制 */
    white-space: nowrap; /* 强制在一行显示 */
    overflow: visible;  /* 允许内容超出容器 */
    line-height: 28px;
    margin-bottom: 0;
    position: relative; /* 相对定位 */
}

/* 地址行样式 - 使用突破容器的方式 */
.address-line {
    max-width: none;
    width: auto;      /* 自动宽度 */
    min-width: 100%;  /* 至少占满容器 */
    display: block;   /* 块级显示 */
    position: relative; /* 相对定位 */
    padding-left: 60px; /* 为地址前缀预留空间 */
    white-space: nowrap; /* 强制不换行 */
    overflow: visible;   /* 允许溢出 */
}

/* 调整地址前缀位置 */
.address-prefix {
    position: absolute;
    left: 0;
    width: 60px;
}

/* 页脚标题样式 */
.widgetheading {
    width: 100%;
    padding: 0;
    margin-bottom: 20px;
}

footer .widgetheading {
    position: relative;
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 28px;
    color: #FFFFFF;
    line-height: 34px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 15px;
    display: block;
}

/* 版权区域样式 */
.copyright {
    display: none;
}

.footer-divider {
    width: 100%;
    height: 1px;
    background-color: #ccc;
    margin: 15px 0;
    position: absolute;
    bottom: 70px;
    left: 0;
}

.footer-copyright {
    position: absolute;
    bottom: 35px;
    width: 1200px;         /* 和.container一样宽 */
    left: 50%;             /* 水平居中 */
    transform: translateX(-50%);
    text-align: center;
    color: #949494;
}

.footer-copyright a {
    color: #949494;
}

/* 二维码容器样式 */
#follow_us_container {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 20px;
    margin: 20px 0;
    width: 100%;
    flex-wrap: wrap; /* 允许在需要时换行 */
}

/* 单个二维码项目容器 */
.qrcode-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 68px;
}

/* 二维码图片样式 */
.qrcode-item img {
    width: 68px;
    height: 68px;
    object-fit: contain;
    margin-bottom: 8px;
}

/* 二维码标题样式 */
.qrcode-item .qrcode-title {
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 28px;
    text-align: center; /* 居中显示 */
    font-style: normal;
    text-transform: none;
    white-space: nowrap;
    width: 100%; /* 确保宽度与图片一致 */
    display: block; /* 确保是块级元素 */
}

/* 特定屏幕范围(1057px-992px)的调整 */
@media screen and (max-width: 1057px) and (min-width: 992px) {
    #follow_us_container {
        gap: 10px; /* 减小间距 */
    }
    
    .qrcode-item {
        width: 62px; /* 略微减小宽度 */
    }
    
    .qrcode-item img {
        width: 62px;
        height: 62px;
    }
    
    .qrcode-item .qrcode-title {
        font-size: 11px; /* 减小字体 */
        line-height: 24px; /* 减小行高 */
    }
}

/* 响应式调整 */
@media screen and (max-width: 991px) {
    #follow_us_container {
        gap: 15px;
    }
    
    .qrcode-item {
        width: 60px;
    }
    
    .qrcode-item img {
        width: 60px;
        height: 60px;
    }
}

@media screen and (max-width: 767px) {
    #follow_us_container {
        gap: 10px;
    }
    
    .qrcode-item {
        width: 55px;
    }
    
    .qrcode-item img {
        width: 55px;
        height: 55px;
    }
    
    .qrcode-item .qrcode-title {
        font-size: 11px;
        line-height: 24px;
    }
}

/* 极小屏幕进一步优化 */
@media screen and (max-width: 576px) {
    #follow_us_container {
        gap: 8px;
        justify-content: space-around; /* 均匀分布 */
    }
    
    .qrcode-item {
        width: 50px;
    }
    
    .qrcode-item img {
        width: 50px;
        height: 50px;
    }
    
    .qrcode-item .qrcode-title {
        font-size: 10px;
        line-height: 20px;
    }
}

/* 移除旧的二维码标签样式 */
.qrcode-labels {
    display: none;
}

/* 服务支持列表项 */
#service_support_container li {
    margin-bottom: 5px;
}

/* 响应式页脚布局 */
@media (max-width: 991px) {
    .footer-divider {
        position: relative;
        bottom: auto;
        margin-top: 30px;
    }
    
    .footer-copyright {
        position: relative;
        bottom: auto;
        margin-top: 15px;
    }
}

/* 调整移动端样式 */
@media (max-width: 767px) {
    footer {
        padding: 30px 0 60px;
    }
    
    footer .col-lg-1, footer .col-lg-2, footer .col-md-4, footer .col-lg-4, 
    footer .col-lg-5, footer .col-lg-6, footer .col-lg-7, footer .col-lg-8, 
    footer .col-lg-9, footer .col-lg-10, footer .col-lg-11, footer .col-lg-12{
        margin-bottom: 20px;
    }
    
    /* 移动端标题字体大小调整 */
    footer .widgetheading {
        font-size: 24px;
        line-height: 30px;
    }
    
    /* 移动端二维码调整 */
    #follow_us_container {
        justify-content: flex-start;
        gap: 10px;
    }
    
    .qrcode-item {
        width: 60px;
    }
    
    .qrcode-item img {
        width: 60px;
        height: 60px;
    }
    
    .qrcode-item .qrcode-title {
        font-size: 11px;
        line-height: 24px;
    }
    
    /* 进一步减小行高 */
    .office-address,
    .office-address div,
    .address-line,
    .address-prefix,
    .address-content {
        line-height: 22px !important; /* 极小屏幕上进一步减小行高 */
    }
    
    /* 极小屏幕上地址内容调整 */
    .address-content {
        margin-left: 10px !important; /* 极小屏幕上进一步减小左边距 */
    }
    
    /* 极小屏幕上地址行样式 */
    .address-line {
        margin: 0 !important;
        padding: 0 !important;
        display: block !important;
    }
    
    /* 极小屏幕上地址前缀样式 */
    .address-prefix {
        display: block !important;
        float: none !important; /* 移除浮动 */
        margin: 0 !important;
        width: auto !important;
    }
    
    /* 调整联系方式框架中所有内容的显示方式 */
    #contact_info_container li {
        line-height: 22px !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* 联系我们栏调整 */
footer .col-md-4:last-child {
    overflow: visible; /* 允许内容溢出 */
    position: relative; /* 支持绝对定位 */
}

footer .col-md-4:last-child .widget {
    position: relative;
    overflow: visible;
}

/* 添加响应式处理 */
@media screen and (max-width: 1437px) {
    /* 保持基本行高 */
    .office-address,
    .office-address div,
    .address-line,
    .address-prefix,
    .address-content {
        line-height: 28px !important; /* 行高为28px */
    }
    
    /* 地址内容强制换行 */
    .address-content {
        display: block !important; /* 块级显示 */
        white-space: normal !important; /* 允许自动换行 */
        word-wrap: break-word !important;
        word-break: break-word !important;
        margin-left: 30px !important; /* 左侧缩进 */
    }
    
    /* 控制地址前缀不换行 */
    .address-prefix {
        display: block !important;
        float: left !important;
        margin-right: 0 !important;
    }
}

/* 特别小的屏幕上进一步优化 */
@media screen and (max-width: 991px) {
    /* 减小行高 */
    .office-address,
    .office-address div,
    .address-line,
    .address-prefix,
    .address-content {
        line-height: 24px !important;
    }

    /* 地址行样式 */
    .address-line {
        position: relative !important;
        padding-left: 10px !important; /* 减小左内边距 */
        min-height: 24px !important;
    }

    /* 地址前缀固定位置 */
    .address-prefix {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 10px !important; /* 减小标签宽度 */
    }

    /* 地址内容样式 */
    .address-content {
        display: block !important;
        white-space: normal !important;
        word-wrap: break-word !important;
        word-break: break-word !important;
    }
}

/* 极小屏幕样式保持相同的间距结构 */
@media screen and (max-width: 767px) {
    .office-address,
    .office-address div,
    .address-line,
    .address-prefix,
    .address-content {
        line-height: 22px !important;
    }
}

/* 地址前缀的字间距控制 */
.address-prefix-char {
    display: inline-block;
}
.address-prefix-char.first-char {
    margin-right: 28px; /* 控制"地"和"址"之间的间距 */
}

/* 调整不同屏幕尺寸下"地"和"址"之间的间距 */
@media screen and (max-width: 991px) {
    .address-prefix-char.first-char {
        margin-right: 15px; /* 小屏幕上减小间距 */
    }

}

@media screen and (max-width: 767px) {
    .address-prefix-char.first-char {
        margin-right: 5px; /* 极小屏幕上进一步减小间距 */
    }
}

/* 控制产品中心和服务与支持链接的行高 */
#product_solution_container li,
#service_support_container li {
    line-height: 28px; /* 行高28px，与字体14px形成上下各7px的间距 */
    margin: 0; /* 移除外边距 */
    padding: 0; /* 移除内边距 */
}

/* 确保链接本身也遵循相同的行高 */
#product_solution_container li a,
#service_support_container li a {
    line-height: 28px;
    margin: 0;
    padding: 0;
}

/* 针对989px以下屏幕的地址显示特殊处理 */
@media screen and (max-width: 989px) {
    /* 地址行调整为垂直布局 */
    li.address-line {
        display: flex !important;
        flex-direction: column !important;
        flex-wrap: nowrap !important;
        padding-left: 0 !important;
        margin-bottom: 10px !important;
    }
    
    /* 地址前缀独立一行显示 */
    .address-prefix {
        position: relative !important;
        left: 0 !important;
        width: auto !important;
        margin-bottom: 5px !important;
        display: block !important;
    }
    
    /* 地址内容另起一行显示 */
    .address-content {
        display: block !important;
        white-space: normal !important;
        word-wrap: break-word !important;
        word-break: break-all !important;
        left: 0 !important;
        padding-left: 0 !important;
        margin-left: 0 !important;
        width: 100% !important;
        line-height: 1.5 !important;
    }
    
    /* 办公地址容器调整 */
    .office-address div {
        white-space: normal !important;
    }
    
    /* 确保地址内容可以正确换行 */
    #contact_info_container li {
        white-space: normal !important;
        width: 100% !important;
    }
}

/* 英文界面地址行特殊处理 */
html[lang="en"] .address-line,
body.english-version .address-line,
.index_en .address-line,
[data-lang="en"] .address-line {
    white-space: normal; /* 允许英文地址自动换行 */
    line-height: 1.5; /* 增加行距 */
    margin-bottom: 5px; /* 地址行之间的间距 */
}

/* 小屏幕(491px以下)英文界面地址显示特殊处理 */
@media screen and (max-width: 491px) {
    /* 英文界面地址行在小屏幕下强制换行显示 */
    html[lang="en"] .address-line,
    body.english-version .address-line,
    .index_en .address-line,
    [data-lang="en"] .address-line {
        display: flex !important;
        flex-direction: column !important;
        margin-bottom: 5px !important;
    }
    
    /* 英文界面地址前缀(Address:)单独一行显示，字母间不要有间距 */
    html[lang="en"] .address-prefix,
    body.english-version .address-prefix,
    .index_en .address-prefix,
    [data-lang="en"] .address-prefix {
        display: block !important;
        width: 100% !important;
        margin-bottom: 5px !important; /* 减小与内容的间距 */
        position: static !important;
        letter-spacing: normal !important; /* 恢复正常字母间距 */
    }
    
    /* 移除Address字母之间的间距 */
    html[lang="en"] .address-prefix-char,
    body.english-version .address-prefix-char,
    .index_en .address-prefix-char,
    [data-lang="en"] .address-prefix-char {
        width: auto !important; /* 移除固定宽度 */
        display: inline !important; /* 使用行内显示而非行内块 */
        letter-spacing: normal !important; /* 使用正常字母间距 */
        margin: 0 !important; /* 移除所有边距 */
        padding: 0 !important; /* 移除所有内边距 */
    }
    
    /* 英文界面地址内容从新行开始显示，行间距减小，确保文本紧凑 */
    html[lang="en"] .address-content,
    body.english-version .address-content,
    .index_en .address-content,
    [data-lang="en"] .address-content {
        display: block !important;
        padding-left: 5px !important; /* 增加一点左侧缩进 */
        margin-left: 0 !important;
        width: 100% !important;
        line-height: 1.3 !important; /* 减小行高使文本更紧凑 */
        margin-bottom: 2px !important; /* 减小底部间距 */
        word-wrap: break-word !important; /* 允许单词内部断行 */
        word-break: break-all !important; /* 在任何字符间断行 */
        text-align: justify !important; /* 两端对齐，减少空白 */
        hyphens: auto !important; /* 自动添加连字符 */
    }
    
    /* 修复地址各行之间的间距问题 */
    html[lang="en"] .address-line + .address-line,
    body.english-version .address-line + .address-line,
    .index_en .address-line + .address-line,
    [data-lang="en"] .address-line + .address-line {
        margin-top: 0 !important; /* 减少地址行之间的间距 */
    }
    
    /* 小屏幕下隐藏Address字母间距控制的特殊标签 */
    html[lang="en"] .address-prefix-char[style*="visibility:hidden"],
    body.english-version .address-prefix-char[style*="visibility:hidden"],
    .index_en .address-prefix-char[style*="visibility:hidden"],
    [data-lang="en"] .address-prefix-char[style*="visibility:hidden"] {
        display: none !important; /* 完全隐藏而不是仅改变可见性 */
    }
}

/* 确保移动端英文地址也能正常显示 */
@media screen and (max-width: 767px) {
    html[lang="en"] .address-line,
    body.english-version .address-line,
    .index_en .address-line,
    [data-lang="en"] .address-line {
        position: relative;
    }
} 

footer .container {
    width: 1200px;
    height: 470px;
    margin: 0 auto;
    display: flex;
    align-items: flex-start; /* 顶部对齐 */
    justify-content: center;
    position: relative;
    z-index: 2;
    background: none;
    padding: 0;
}
footer .container > .row {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
}
footer .container > .row > .col-md-4:nth-child(1) {
    width: 153px;
    text-align: left;
}
footer .container > .row > .col-md-4:nth-child(2) {
    width: 140px;
    margin-left: 179px;
    text-align: left;
}
footer .container > .row > .col-md-4:nth-child(3) {
    width: 508px;
    margin-left: 220px;
    text-align: left;
}
footer .container > .row > .col-md-4 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

html[lang="en"] footer .container > .row > .col-md-4:nth-child(1),
body.english-version footer .container > .row > .col-md-4:nth-child(1),
.index_en footer .container > .row > .col-md-4:nth-child(1),
[data-lang="en"] footer .container > .row > .col-md-4:nth-child(1) {
    width: 212px !important;
}

html[lang="en"] footer .container > .row > .col-md-4:nth-child(2),
body.english-version footer .container > .row > .col-md-4:nth-child(2),
.index_en footer .container > .row > .col-md-4:nth-child(2),
[data-lang="en"] footer .container > .row > .col-md-4:nth-child(2) {
    width: 250px !important;
    margin-left: 112px;
}

html[lang="en"] footer .container > .row > .col-md-4:nth-child(3),
body.english-version footer .container > .row > .col-md-4:nth-child(3),
.index_en footer .container > .row > .col-md-4:nth-child(3),
[data-lang="en"] footer .container > .row > .col-md-4:nth-child(3) {
    margin-left: 110px !important;
}
