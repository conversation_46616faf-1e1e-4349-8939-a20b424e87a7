// 当文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化新闻列表
    loadNewsList();
    // 加载热门新闻
    loadHotNews();
});

// 加载新闻列表
function loadNewsList() {
    // 从API获取新闻列表数据
    fetch('/apis/company_info/list?page=1&page_size=10&filters=' + JSON.stringify({
        info_type: 'news',
        show: true
    }))
    .then(response => response.json())
    .then(data => {
        if (data.status === 'ok' && data.data) {
            // 渲染新闻列表
            renderNewsList(data.data);
        } else {
            console.error('获取新闻列表失败:', data.msg);
        }
    })
    .catch(error => {
        console.error('加载新闻列表出错:', error);
    });
}

// 加载热门新闻
function loadHotNews() {
    // 从API获取热门新闻数据
    fetch('/apis/company_info/list?page=1&page_size=5&filters=' + JSON.stringify({
        info_type: 'news',
        show: true,
        is_hot: true
    }))
    .then(response => response.json())
    .then(data => {
        if (data.status === 'ok' && data.data) {
            // 渲染热门新闻
            renderHotNews(data.data);
        } else {
            console.error('获取热门新闻失败:', data.msg);
        }
    })
    .catch(error => {
        console.error('加载热门新闻出错:', error);
    });
}

// 渲染新闻列表
function renderNewsList(newsData) {
    const newsListContainer = document.getElementById('newsList');
    if (!newsListContainer) return;

    // 按显示顺序排序
    const sortedNews = [...newsData].sort((a, b) => 
        (a.display_order || 100) - (b.display_order || 100)
    );

    // 生成新闻列表HTML
    const newsHtml = sortedNews.map(news => {
        // 处理图片路径
        let imgSrc = news.image_path || '';
        if (imgSrc.includes('C:')) {
            const fileName = imgSrc.split('\\').pop().split('/').pop();
            imgSrc = '/uploads/' + fileName;
        } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
            imgSrc = '/' + imgSrc;
        }

        // 格式化日期
        const date = new Date(news.created_at || news.updated_at);
        const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

        // 获取图片尺寸信息（如果有）
        const imageSizeClass = news.image_size || '362px';

        return `
            <div class="news-item">
                <div class="news-image-container" style="width: ${imageSizeClass}">
                    <img src="${imgSrc}" alt="${news.title}" class="news-image">
                </div>
                <div class="news-info">
                    <h2 class="news-title">
                        <a href="/news-detail.html?id=${news.id}">${news.title}</a>
                    </h2>
                    <div class="news-date">${formattedDate}</div>
                    <a href="/news-detail.html?id=${news.id}" class="news-more">了解详情</a>
                </div>
            </div>
        `;
    }).join('');

    newsListContainer.innerHTML = newsHtml;
}

// 渲染热门新闻
function renderHotNews(hotNewsData) {
    const hotNewsContainer = document.getElementById('hotNewsList');
    if (!hotNewsContainer) return;

    // 按显示顺序排序
    const sortedHotNews = [...hotNewsData].sort((a, b) => 
        (a.display_order || 100) - (b.display_order || 100)
    );

    // 生成热门新闻HTML
    const hotNewsHtml = sortedHotNews.map(news => {
        // 处理图片路径
        let imgSrc = news.image_path || '';
        if (imgSrc.includes('C:')) {
            const fileName = imgSrc.split('\\').pop().split('/').pop();
            imgSrc = '/uploads/' + fileName;
        } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
            imgSrc = '/' + imgSrc;
        }

        // 格式化日期
        const date = new Date(news.created_at || news.updated_at);
        const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

        return `
            <div class="hot-news-item">
                ${imgSrc ? `<img src="${imgSrc}" alt="${news.title}" class="hot-news-image">` : ''}
                <div class="hot-news-info">
                    <h4 class="hot-news-title">
                        <a href="/news-detail.html?id=${news.id}">${news.title}</a>
                    </h4>
                    <div class="hot-news-date">${formattedDate}</div>
                </div>
            </div>
        `;
    }).join('');

    hotNewsContainer.innerHTML = hotNewsHtml;
}

// 渲染分页
function renderPagination(totalPages, currentPage) {
    const paginationContainer = document.getElementById('pagination');
    if (!paginationContainer) return;

    let paginationHtml = '<div class="pagination">';

    // 上一页
    if (currentPage > 1) {
        paginationHtml += `
            <span class="pagination-item" onclick="loadNewsList(${currentPage - 1})">
                <i class="fa fa-angle-left"></i>
            </span>
        `;
    }

    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || // 第一页
            i === totalPages || // 最后一页
            (i >= currentPage - 2 && i <= currentPage + 2) // 当前页附近的页码
        ) {
            paginationHtml += `
                <span class="pagination-item ${i === currentPage ? 'active' : ''}" 
                      onclick="loadNewsList(${i})">
                    ${i}
                </span>
            `;
        } else if (
            (i === currentPage - 3 && currentPage > 4) || 
            (i === currentPage + 3 && currentPage < totalPages - 3)
        ) {
            paginationHtml += '<span class="pagination-item">...</span>';
        }
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `
            <span class="pagination-item" onclick="loadNewsList(${currentPage + 1})">
                <i class="fa fa-angle-right"></i>
            </span>
        `;
    }

    paginationHtml += '</div>';
    paginationContainer.innerHTML = paginationHtml;
} 