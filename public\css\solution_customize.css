.solution-customize {
    width: 100%;
    height: 747px; /* 877px */
    background-color: #FFFFFF;
    position: relative;
    overflow: hidden;
}

.solution-customize-title {
    font-family: "Source Han Sans CN", sans-serif;
    font-weight: bold;
    font-size: 2.5vw; /* 48px */
    color: #00509F;
    line-height: 3.02vw; /* 58px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding-top: 66px; /* 60px */
    margin-bottom: 53px; /* 50px */
}

.solution-customize-content {
    position: relative;
    width: 62.5vw; /* 1200px */
    height: 480px; /* 560px */
    margin: 0 auto;
    padding: 0;
}

.solution-customize-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
}

.solution-customize-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 1.458vw; /* 28px */
    display: block; /* 消除图片底部间隙 */
}

.solution-customize-info {
    position: absolute;
    right: 48px; /* 38px */
    top: 186px; /* 242px */
    transform: none;
    width: 512px;
    height: 246px;
    background: #FFFFFF;
    padding: 0;
    border-radius: 20px; /* 18px */
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    padding: 37px 28px 42px 42px; 
}

.solution-customize-text {
    font-family: "Source Han Sans CN", sans-serif;
    font-weight: 400;
    font-size: 1.094vw; /* 21px */
    color: #333333;
    line-height: 36px; /* 43px */
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 36px; /* 29px */
}

.customize-button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 200px;
    height: 64px;
    background: #00509F;
    border-radius: 0.625vw; /* 12px */
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.customize-button span {
    font-family: "Source Han Sans CN", sans-serif;
    font-weight: 500;
    font-size: 24px; /* 20px */
    color: #FFFFFF;
    line-height: 2.76vw; /* 53px */
    text-align: center;
    font-style: normal;
    text-transform: none;
}

.customize-button:hover {
    background-color: #007DDB;
}

/* 英文界面样式调整 */
html[lang="en"] .solution-customize-text,
body.en-site .solution-customize-text,
.index_en .solution-customize-text,
[data-lang="en"] .solution-customize-text,
.english-version .solution-customize-text {
    font-size: 0.9vw; /* 减小英文界面字号 */
    line-height: 1.8vw; /* 减小英文界面行高 */
}

/* 响应式设计 - 492px及以下屏幕 */
@media screen and (max-width: 492px) {
    .solution-customize-text {
        font-size: 0.1vw; /* 比原来的1.094vw小 */
        line-height: 2vw;
        padding: 0;
        margin-bottom: 0;
    }
    
    .solution-customize-info {
        padding-bottom: 2vw; /* 减小底部内边距 */
    }
    
    /* 491px以下英文界面特殊处理 */
    html[lang="en"] .solution-customize-text,
    body.en-site .solution-customize-text,
    .index_en .solution-customize-text,
    [data-lang="en"] .solution-customize-text,
    .english-version .solution-customize-text {
        font-size: 8px !important;
        transform: scale(0.6);
        transform-origin: center center; /* 修改为居中缩放 */
        line-height: 1.2; /* 略微增加行高 */
        width: 166%; /* 增加宽度补偿缩放效果 (1/0.6 ≈ 1.66) */
        display: flex; /* 使用flex布局 */
        justify-content: flex-start; /* 改为左对齐 */
        align-items: center; /* 垂直居中 */
        position: relative; /* 添加相对定位 */
        left: -33%; /* 向左偏移以补偿宽度增加 */
        padding: 5px 0 0 5px ; /* 添加左内边距补偿左偏移 */
        box-sizing: border-box;
        white-space: normal; /* 允许换行 */
        text-align: left; /* 文本左对齐 */
        max-height: 70%; /* 限制最大高度 */
        margin: 0; /* 清除外边距 */
        letter-spacing: -0.2px; /* 稍微减小字母间距 */
    }
    
    /* 调整按钮在小屏幕上的位置 */
    .customize-button {
        margin-top: 5px; /* 增加与文本的间距 */
        margin-left: auto; /* 居中显示 */
        margin-right: auto;
        width: 80%; /* 调整宽度 */
    }
}