<!DOCTYPE html>
<html lang="en">
<head>
    <title>微信文章管理</title>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="/admin/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="/admin/css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-style.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-media.css"/>
    <link href="/admin/font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/admin/css/jquery.gritter.css"/>
    <script src="/admin/js/jquery.min.js"></script>
    <script src="/admin/js/jquery.cookie.js"></script>
</head>
<body>

<script src="/admin/js/head.js"></script>

<!--main-container-part-->
<div id="content">
    <!--breadcrumbs-->
    <div id="content-header">
        <div id="breadcrumb">
            <a href="#" class="current">微信文章管理</a>
        </div>
    </div>
    <!--End-breadcrumbs-->

    <!--Action boxes-->
    <div class="container-fluid">
        
        <!-- 统计信息 -->
        <div class="row-fluid">
            <div class="span3">
                <div class="widget-box">
                    <div class="widget-content">
                        <div class="stat-box">
                            <h4 id="total-articles">0</h4>
                            <p>总文章数</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="span3">
                <div class="widget-box">
                    <div class="widget-content">
                        <div class="stat-box">
                            <h4 id="total-sections">0</h4>
                            <p>总段落数</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="span3">
                <div class="widget-box">
                    <div class="widget-content">
                        <div class="stat-box">
                            <h4 id="sections-with-images">0</h4>
                            <p>包含图片段落</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="span3">
                <div class="widget-box">
                    <div class="widget-content">
                        <div class="stat-box">
                            <h4 id="avg-content-length">0</h4>
                            <p>平均内容长度</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="widget-box">
            <div class="widget-content">
                <div class="row-fluid">
                    <div class="span3">
                        <input type="text" id="search-title" placeholder="搜索标题..." class="input-medium">
                    </div>
                    <div class="span3">
                        <input type="text" id="search-content" placeholder="搜索内容..." class="input-medium">
                    </div>
                    <div class="span3">
                        <input type="text" id="search-parent-id" placeholder="父文章ID..." class="input-medium">
                    </div>
                    <div class="span3">
                        <button class="btn btn-primary" onclick="searchArticles()">搜索</button>
                        <button class="btn" onclick="resetSearch()">重置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文章列表 -->
        <div class="widget-box">
            <div class="widget-content nopadding">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>文章ID</th>
                            <th>标题</th>
                            <th>摘要</th>
                            <th>内容预览</th>
                            <th>图片数量</th>
                            <th>段落顺序</th>
                            <th>父文章ID</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="articles-list">
                        <!-- 动态加载内容 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页 -->
        <div class="fg-toolbar ui-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix">
            <div id="pagination" class="dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_full_numbers">
                <!-- 动态生成分页 -->
            </div>
        </div>
    </div>

    <!-- 文章详情模态框 -->
    <div id="articleDetailModal" class="modal hide fade">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">×</button>
            <h3>文章详情</h3>
        </div>
        <div class="modal-body" id="article-detail-content">
            <!-- 动态加载文章详情 -->
        </div>
        <div class="modal-footer">
            <button class="btn btn-success" onclick="generateProduct()">生成产品页面</button>
            <button class="btn" data-dismiss="modal">关闭</button>
        </div>
    </div>

    <!-- 生成产品模态框 -->
    <div id="generateProductModal" class="modal hide fade">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">×</button>
            <h3>生成产品页面</h3>
        </div>
        <div class="modal-body">
            <form>
                <div class="control-group">
                    <label class="control-label">产品标题</label>
                    <div class="controls">
                        <input type="text" id="product-title" class="input-xlarge" placeholder="输入产品标题">
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">产品类型</label>
                    <div class="controls">
                        <input type="text" id="product-type" class="input-xlarge" value="微信文章" placeholder="输入产品类型">
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">语言</label>
                    <div class="controls">
                        <select id="product-lang" class="input-medium">
                            <option value="0">中文</option>
                            <option value="1">英文</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="confirmGenerateProduct()">确认生成</button>
            <button class="btn" data-dismiss="modal">取消</button>
        </div>
    </div>
</div>

<!--end-main-container-part-->
<script src="/admin/js/footer.js"></script>
<script src="/admin/js/jquery.ui.custom.js"></script>
<script src="/admin/js/bootstrap.min.js"></script>
<script src="/admin/js/jquery.gritter.min.js"></script>
<script src="/admin/js/matrix.js"></script>

<script>
// 全局变量
let currentPage = 1;
let pageSize = 10;
let currentArticleId = null;
let searchParams = {};

// 页面加载完成后初始化
$(document).ready(function() {
    loadStats();
    loadArticles();
});

// 加载统计信息
function loadStats() {
    $.ajax({
        url: '/apis/wechat_articles_stats/',
        type: 'GET',
        success: function(response) {
            if (response.status === 'ok') {
                const data = response.data;
                $('#total-articles').text(data.total_articles || 0);
                $('#total-sections').text(data.total_sections || 0);
                $('#sections-with-images').text(data.sections_with_images || 0);
                $('#avg-content-length').text(Math.round(data.avg_content_length || 0));
            }
        },
        error: function() {
            console.error('加载统计信息失败');
        }
    });
}

// 加载文章列表
function loadArticles(page = 1) {
    currentPage = page;
    
    const params = {
        page: page,
        size: pageSize,
        ...searchParams
    };
    
    $.ajax({
        url: '/apis/wechat_articles/',
        type: 'GET',
        data: params,
        success: function(response) {
            if (response.status === 'ok') {
                renderArticlesList(response.data);
                renderPagination(response.page, response.total_pages, response.total);
            } else {
                $.gritter.add({
                    title: '错误',
                    text: response.msg || '加载文章列表失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function() {
            $.gritter.add({
                title: '错误',
                text: '网络错误，加载文章列表失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

// 渲染文章列表
function renderArticlesList(articles) {
    const tbody = $('#articles-list');
    tbody.empty();
    
    if (articles.length === 0) {
        tbody.append('<tr><td colspan="10" class="text-center">暂无数据</td></tr>');
        return;
    }
    
    articles.forEach(article => {
        const row = `
            <tr>
                <td>${article.id}</td>
                <td>${article.article_id}</td>
                <td>${article.title || '无标题'}</td>
                <td>${article.digest || '无摘要'}</td>
                <td>${article.content_preview || '无内容'}</td>
                <td>${article.image_urls.length}</td>
                <td>${article.section_order}</td>
                <td>${article.parent_article_id || '无'}</td>
                <td>${new Date(article.created_at).toLocaleString()}</td>
                <td>
                    <button class="btn btn-mini btn-info" onclick="viewArticleDetail('${article.article_id}')">查看详情</button>
                    <button class="btn btn-mini btn-primary" onclick="openDetailPage('${article.article_id}')">详情页编辑</button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 渲染分页
function renderPagination(currentPage, totalPages, total) {
    const pagination = $('#pagination');
    pagination.empty();
    
    if (totalPages <= 1) return;
    
    let paginationHtml = `<span>共 ${total} 条记录，第 ${currentPage}/${totalPages} 页</span> `;
    
    // 上一页
    if (currentPage > 1) {
        paginationHtml += `<button class="btn btn-mini" onclick="loadArticles(${currentPage - 1})">上一页</button> `;
    }
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            paginationHtml += `<button class="btn btn-mini btn-primary">${i}</button> `;
        } else {
            paginationHtml += `<button class="btn btn-mini" onclick="loadArticles(${i})">${i}</button> `;
        }
    }
    
    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `<button class="btn btn-mini" onclick="loadArticles(${currentPage + 1})">下一页</button>`;
    }
    
    pagination.html(paginationHtml);
}

// 搜索文章
function searchArticles() {
    searchParams = {
        title: $('#search-title').val(),
        content: $('#search-content').val(),
        parent_article_id: $('#search-parent-id').val()
    };

    // 移除空值
    Object.keys(searchParams).forEach(key => {
        if (!searchParams[key]) {
            delete searchParams[key];
        }
    });

    loadArticles(1);
}

// 重置搜索
function resetSearch() {
    $('#search-title').val('');
    searchParams = {};
    loadArticles(1);
}

// 打开详情页面编辑
function openDetailPage(articleId) {
    // 保存文章ID到Cookie
    $.cookie('wechat_article_id', articleId, { expires: 1 });
    // 在新窗口打开详情编辑页面
    window.open(`wechat_article_detail.html?article_id=${articleId}`, '_blank');
}

// 查看文章详情
function viewArticleDetail(articleId) {
    currentArticleId = articleId;

    $.ajax({
        url: '/apis/wechat_article_detail/',
        type: 'GET',
        data: { article_id: articleId },
        success: function(response) {
            if (response.status === 'ok') {
                renderArticleDetail(response.data);
                $('#articleDetailModal').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: response.msg || '加载文章详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function() {
            $.gritter.add({
                title: '错误',
                text: '网络错误，加载文章详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

// 渲染文章详情
function renderArticleDetail(sections) {
    const container = $('#article-detail-content');
    container.empty();

    if (sections.length === 0) {
        container.html('<p>暂无内容</p>');
        return;
    }

    const firstSection = sections[0];
    container.append(`
        <div class="article-info">
            <h4>${firstSection.title || '无标题'}</h4>
            <p><strong>文章ID:</strong> ${firstSection.article_id}</p>
            <p><strong>摘要:</strong> ${firstSection.digest || '无摘要'}</p>
            <p><strong>总段落数:</strong> ${sections.length}</p>
        </div>
        <hr>
    `);

    sections.forEach((section, index) => {
        let sectionHtml = `
            <div class="section-item" style="margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                <div class="section-header">
                    <strong>段落 ${index + 1} (顺序: ${section.section_order})</strong>
                </div>
        `;

        if (section.content_text && section.content_text.trim()) {
            sectionHtml += `<div class="section-text" style="margin: 10px 0;">${section.content_text}</div>`;
        }

        if (section.image_urls && section.image_urls.length > 0) {
            sectionHtml += '<div class="section-images" style="margin: 10px 0;">';
            section.image_urls.forEach(imageUrl => {
                sectionHtml += `<img src="${imageUrl}" style="max-width: 200px; margin: 5px;" class="img-thumbnail" />`;
            });
            sectionHtml += '</div>';
        }

        sectionHtml += '</div>';
        container.append(sectionHtml);
    });
}

// 生成产品页面
function generateProduct() {
    if (!currentArticleId) {
        $.gritter.add({
            title: '错误',
            text: '请先选择一篇文章',
            sticky: false,
            time: 3000
        });
        return;
    }

    $('#generateProductModal').modal('show');
}

// 确认生成产品
function confirmGenerateProduct() {
    const productTitle = $('#product-title').val();
    const productType = $('#product-type').val();
    const productLang = $('#product-lang').val();

    if (!productTitle) {
        $.gritter.add({
            title: '错误',
            text: '请输入产品标题',
            sticky: false,
            time: 3000
        });
        return;
    }

    $.ajax({
        url: '/apis/generate_product_from_wechat/',
        type: 'POST',
        data: {
            article_id: currentArticleId,
            product_title: productTitle,
            product_type: productType,
            lang: productLang
        },
        success: function(response) {
            if (response.status === 'ok') {
                $.gritter.add({
                    title: '成功',
                    text: `产品页面生成成功！产品ID: ${response.product_id}`,
                    sticky: false,
                    time: 5000
                });
                $('#generateProductModal').modal('hide');
                $('#articleDetailModal').modal('hide');

                // 清空表单
                $('#product-title').val('');
                $('#product-type').val('微信文章');
                $('#product-lang').val('0');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: response.msg || '生成产品页面失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function() {
            $.gritter.add({
                title: '错误',
                text: '网络错误，生成产品页面失败',
                sticky: false,
                time: 3000
            });
        }
    });
}
</script>
</body>
</html>
