---
title: Changelog
subTitle: Support
nav: docs
description: Owl Carousel Documentation

sort: 5

tags:
- Support
---

{{#markdown }}
## Changelog
------

##### 2.0.0.beta.2.4

* Refactors Core
* Makes `slideBy` option independet of `nav`
* Corrects update of `navRewind`
* Corrects `change` event for position in carousel
* Replaces `indexOf` with `$.inArray` in navigaiton plugin
* Corrects `to` override in navigation plugin
* Adds core overrides for navigation plugin
* Corrects naming of plugins as object members
* Corrects `navText` option of navigation plugin
* Corrects `slideBy` option and adds events for navigation plugin
* Corrects adaptive behaviour of the navigation plugin
* fixed autoHeight plugin
* fixed animate plugin
* added autoHeight demo
* fixed download link in subpages


##### 2.0.0.beta.2.3

Thanks to <PERSON><PERSON> for this amazing update
* Separates navigation from core (nav,dots and hash)
* Corrests and completes JSDocs comments
* Replaces `bind` with `$.proxy`


##### 2.0.0.beta.2.2
* fixed some events trigger two times on initilize
* fixed lazyload bug
* fixed stage active class calculation when using stagePadding
* fixed translate callback
* fixed fixed update function and update/updated events
* included stagePadding demo

------

{{/markdown }} 
