.product-recommend {
    width: 100%;
    height: 1031px; /* 1126/1920 * 100 = 58.65vw */
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

.section-title {
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: bold;
    font-size: 2.5vw; /* 48/1920 * 100 = 2.5vw */
    color: #00509F;
    line-height: 3.02vw; /* 58/1920 * 100 = 3.02vw */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-top: 64px;      /* 83/1920 * 100 = 4.32vw */
    margin-bottom: 56px;  /* 83/1920 * 100 = 4.32vw */
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 360px); /* 378/1920 * 100 = 19.69vw */
    gap: 2.08vw; /* 40/1920 * 100 = 2.08vw */
    max-width: 62.5vw; /* 1200/1920 * 100 = 62.5vw */
    width: 100%;
    margin: 0 auto;
    margin-bottom: 81px;  /* 100/1920 * 100 = 5.21vw */
}

.product-item {
    width: 360px; /* 378/1920 * 100 = 19.69vw */
    height: 360px; /* 378/1920 * 100 = 19.69vw */
    background: #F8F8F8;
    border-radius: 1.46vw; /* 28/1920 * 100 = 1.46vw */
    border: 1px solid #B2B2B2; /* 2/1920 * 100 = 0.1vw */
    padding: 0; /* 30/1920 * 100 = 1.56vw */
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 auto;
}

.product-item:hover {
    border-color: #0050A2;
    transform: translateY(-0.26vw); /* 5/1920 * 100 = 0.26vw */
}

.product-image-placeholder {
    width: 100%;
    height: auto;
    margin-bottom: 27px;
    padding:29px 20px 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image-placeholder img {
    width: 100%;
    height: 100%;
    max-width: 320px;
    max-height: 238px;
    object-fit: contain;
    object-position: center center;
    transition: transform 0.3s ease;
}

.product-item:hover .product-image-placeholder img {
    transform: scale(1.05);
}

.product-title {
    font-family: "Source Han Sans CN", sans-serif;
    font-weight: 400;
    font-size: 1.46vw; /* 28/1920 * 100 = 1.46vw */
    color: #333333;
    line-height: 2.08vw; /* 40/1920 * 100 = 2.08vw */
    text-align: center;
    font-style: normal;
    text-transform: none;
    width: auto;
    display: inline-block;
    margin-bottom: 31px;
    transition: color 0.3s ease;
}

.product-title a {
    color: #333333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-title a:hover,
.product-title a:focus {
    color: #0050A2;
    text-decoration: none;
}

.product-item:hover .product-title a {
    color: #0050A2;
}

