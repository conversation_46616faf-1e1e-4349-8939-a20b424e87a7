/* 防止横向滚动条 */
body {
    overflow-x: hidden;
}

/* 页面标题背景板 */
.page-title-banner {
    width: 100%;
    height: 6.25vw; /* 120px */
    background: #F8F8F8;
    border-radius: 0;
    display: flex;
    align-items: center;
    margin-bottom: 2.5vw; /* 48px */
}

.page-title {
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 500;
    font-size: 2.29vw; /* 44px */
    color: #00509E;
    line-height: 4.43vw; /* 85px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0;
}

/* 面包屑导航样式 */
.breadcrumb {
    margin: 0 0; /* 20px */
    padding: 0;
    background: none;
    display: flex;
    align-items: center;
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 0.83vw; /* 16px */
    line-height: 1.51vw; /* 29px */
    font-style: normal;
    text-transform: none;
}

.breadcrumb a {
    color: #333333;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #055999;
}

.breadcrumb .separator {
    margin: 0 0.42vw; /* 8px */
    color: #333333;
}

.breadcrumb .current {
    color: #333333;
}

/* 新闻内容区域样式 */
.news-content {
    padding: 20px 0;
}

/* 新闻列表样式 */
.news-list {
    display: flex;
    flex-direction: column;
    padding-bottom: 0;
}

/* 每页第一个新闻项的特殊样式 */
.first-item {
    position: relative;
    margin-top: 49px; /* 与面包屑导航的间距 */
}

.first-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0px;
    border-top: 1px solid #B2B2B2;
}

/* 新闻项基本样式 */
.news-item {
    display: flex;
    flex-direction: column;
    position: relative;
}

/* 每个新闻项下方的下划线 */
.news-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    border-top: 1px solid #B2B2B2;
}
/* 移除旧的下划线样式 */
.news-date::before {
    display: none;
}

/* 新闻内容包装器 */
.news-content-wrapper {
    display: flex;
    gap: 36px; /* 两栏之间的间距 */
    margin-top: 7px; /* 时间和内容之间的间距 */
    margin-bottom:40px;
    height: 210px; 
}

.news-image-container {
    width: 300px; /* 新的宽度要求 */
    height: 210px; /* 新的高度要求 */
    background: #EBEBEB;
    border-radius: 12px;
    overflow: hidden;
    margin-top: 8px; /* 距离上方的间距 */
}

.news-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.news-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* 改为space-between以确保内容分布 */
    height: 210px; /* 与图片容器相同的高度 */
    position: relative;
}

.news-date {
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 24px;
    color: #B2B2B2;
    line-height: 29px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 25px 0 0 0; /* 上方25px的间距 */
    position: relative;
    width: 100%;
}



.news-title {
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 26px;
    color: #323232;
    line-height: 44px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0 0 12px 0; /* 下方12px的间距 */
}

.news-title a {
    color: inherit;
    text-decoration: none;
}

.news-desc {
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: #323232;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    flex: 1; /* 让描述文字占据剩余空间 */
}

/* 修改所有"了解更多"按钮的基础样式 */
.news-more {
    background: #FFFFFF;
    border-radius: 22px;
    border: 1px solid #707070;
    padding: 0 0px;
    width: 120px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 29px;
    font-style: normal;
    text-transform: none;
    margin-bottom: -8px;
    transition: all 0.3s ease; /* 添加过渡效果 */
}

/* 修改所有"了解更多"按钮的悬停样式 */
.news-more:hover {
    background: #FFFFFF; /* 背景色保持不变 */
    border-color: #0050A2; /* 边框颜色变为#0050A2 */
    color: #0050A2; /* 文字颜色变为#0050A2 */
}

/* 响应式布局 */
@media (max-width: 768px) {
    .news-content-wrapper {
        flex-direction: column;
        gap: 20px;
    }
    
    .news-image-container {
        width: 100%;
        height: auto;
        aspect-ratio: 300/210;
        margin-top: 0;
    }
    
    .news-info {
        width: 100%;
    }
    
    .news-title {
        font-size: 22px;
        line-height: 32px;
    }
    
    .news-date {
        font-size: 20px;
        margin: 15px 0 5px 0;
    }
    
    .news-desc {
        margin-bottom: 25px;
    }
    
    .news-date::before {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .news-title {
        font-size: 18px;
        line-height: 28px;
    }
    
    .news-date {
        font-size: 16px;
    }
    
    .news-desc {
        font-size: 14px;
        line-height: 24px;
    }
    
    .news-more {
        font-size: 14px;
        height: 38px;
    }
    
    .news-item {
        padding: 20px 0;
    }
}

/* 分页容器样式 */
.pagination-container {
    margin-top: 44px; /* 与最后一个新闻的间距 */
    margin-bottom: 100px; /* 与页脚的间距 */
    display: flex;
    justify-content: center;
    align-items: center;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 23px; /* 元素之间的间距 */
}

/* 分页箭头样式 */
.pagination-arrow {
    width: 46px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #CCCCCC;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination-arrow:hover:not(.disabled) {
    background: #0050A2;
}

.pagination-arrow img {
    width: 9px;
    height: 19px;
}

.pagination-arrow.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 页码数字样式 */
.pagination-number {
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 16px;
    color: #929292;
    line-height: 33px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s ease;
    padding: 0;
    background: none;
    border: none;
    min-width: auto;
    height: auto;
}

.pagination-number:hover:not(.active) {
    color: #0050A2;
}

.pagination-number.active {
    color: #0050A2;
    background: none;
}

/* 跳转区域样式 */
.pagination-goto {
    width: 124px;
    height: 47px;
    border: 1px solid #0050A2;
    border-radius: 12px;
    display: flex;
    align-items: center;
    padding: 0 5px 0 7px;
    position: relative;
}

.goto-input {
    width: 65px; /* 调整宽度以适应文字 */
    height: 32px;
    border: none;
    background: transparent;
    text-align: left;
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 18px;
    color: #000000;
    line-height: 32px;
    outline: none;
}

/* 当输入框为空时显示占位符 */
.goto-input::placeholder {
    color: #929292;
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 18px;
    line-height: 32px;
}

.goto-button {
    width: 46px;
    height: 38px;
    background: #0050A2;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 18px;
    color: #FFFFFF;
    line-height: 34px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding: 0;
    margin-left: auto;
}

.goto-button:hover {
    opacity: 0.9;
}

/* 热门新闻样式 */
.hot-news {
    background: #f8f8f8;
    padding: 20px;
    border-radius: 4px;
}

.hot-news h3 {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
    padding-bottom: 10px;
    border-bottom: 2px solid #055999;
}

.hot-news-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.hot-news-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.hot-news-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

.hot-news-info {
    flex: 1;
}

.hot-news-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.hot-news-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.hot-news-title a:hover {
    color: #055999;
}

.hot-news-date {
    color: #999;
    font-size: 12px;
}

.news-meta {
    color: #999;
    font-size: 14px;
}

/* 491px以下屏幕的响应式样式 */
@media screen and (max-width: 491px) {
    .news-title {
        margin-bottom: 2.5vw; /* 增加标题和日期之间的间距 */
        line-height: calc(0.42vw + 29px); /* 8px + 原来的行高 */
        word-wrap: break-word; /* 允许长单词换行 */
        white-space: pre-line; /* 保留换行符并允许自动换行 */
    }

    .news-date {
        margin-bottom: 3vw; /* 增加日期和按钮之间的间距 */
    }

    .news-item {
        flex-direction: column;
        gap: 3vw; /* 增加图片和内容之间的间距 */
        padding: 5vw 0; /* 增加每个新闻项之间的间距 */
        position: relative;
    }

    .news-image-container {
        width: 100%; /* 图片容器占满宽度 */
        height: auto;
        aspect-ratio: 400/280; /* 保持图片比例 */
    }

    /* 调整分割线位置，确保内容显示完整 */
    .news-item:not(:last-child)::after {
        width: 90vw; /* 缩小分割线宽度 */
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
    }

    /* 调整内容布局 */
    .news-info {
        width: 100%;
        padding: 0 2vw; /* 添加左右内边距 */
    }

    /* 调整按钮样式 */
    .news-more {
        width: auto; /* 让按钮宽度自适应内容 */
        padding: 0 4vw; /* 添加左右内边距 */
        height: auto; /* 高度自适应内容 */
        padding-top: 1.5vw;
        padding-bottom: 1.5vw;
    }
} 

/* 英文界面使用Grid布局 */
html[lang="en"] .news-content-wrapper {
    height: 210px;
    display: grid;
    grid-template-columns: 300px 1fr;
    grid-gap: 36px;
}

html[lang="en"] .news-image-container {
    width: 300px;
    height: 210px;
}

html[lang="en"] .news-info {
    display: grid;
    grid-template-rows: auto auto 1fr auto;
    grid-row-gap: 12px; /* 控制元素之间的间距 */
    height: 210px; /* 与新闻内容包装器相同高度 */
    position: relative; /* 添加相对定位 */
}

html[lang="en"] .news-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0; /* 移除margin，使用grid-gap控制间距 */
}

html[lang="en"] .news-desc {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3; 
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    margin: 0; /* 移除margin，使用grid-gap控制间距 */
}

html[lang="en"] .news-more {
    justify-self: start; /* 按钮靠左对齐 */
    margin: 0; /* 移除margin，使用grid-gap控制间距 */
    position: absolute; /* 绝对定位 */
    bottom: -9px; /* 固定在底部 */
}