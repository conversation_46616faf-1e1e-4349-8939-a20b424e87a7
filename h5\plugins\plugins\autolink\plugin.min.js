/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.1 (2020-07-08)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=tinymce.util.Tools.resolve("tinymce.Env"),o=function(e,t){if(t<0&&(t=0),3===e.nodeType){var n=e.data.length;n<t&&(t=n)}return t},y=function(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setStart(t,o(t,n)):e.setStartBefore(t)},k=function(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setEnd(t,o(t,n)):e.setEndAfter(t)},r=function(e,t,n){var i,o,r,a,s,f,l,d=e.getParam("autolink_pattern",/^(https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.|(?:mailto:)?[A-Z0-9._%+\-]+@(?!.*@))(.+)$/i),c=e.getParam("default_link_target",!1);if("A"!==e.selection.getNode().tagName){var g=e.selection.getRng().cloneRange();if(g.startOffset<5){if(!(s=g.endContainer.previousSibling)){if(!g.endContainer.firstChild||!g.endContainer.firstChild.nextSibling)return;s=g.endContainer.firstChild.nextSibling}if(f=s.length,y(g,s,f),k(g,s,f),g.endOffset<5)return;i=g.endOffset,o=s}else{if(3!==(o=g.endContainer).nodeType&&o.firstChild){for(;3!==o.nodeType&&o.firstChild;)o=o.firstChild;3===o.nodeType&&(y(g,o,0),k(g,o,o.nodeValue.length))}i=1===g.endOffset?2:g.endOffset-1-t}for(var u,h=i;y(g,o,2<=i?i-2:0),k(g,o,1<=i?i-1:0),--i," "!==(l=g.toString())&&""!==l&&160!==l.charCodeAt(0)&&0<=i-2&&l!==n;);(u=g.toString())===n||" "===u||160===u.charCodeAt(0)?(y(g,o,i),k(g,o,h),i+=1):(0===g.startOffset?y(g,o,0):y(g,o,i),k(g,o,h)),"."===(a=g.toString()).charAt(a.length-1)&&k(g,o,h-1);var m=(a=g.toString().trim()).match(d),C=e.getParam("link_default_protocol","http","string");m&&("www."===m[1]?m[1]=C+"://www.":/@$/.test(m[1])&&!/^mailto:/.test(m[1])&&(m[1]="mailto:"+m[1]),r=e.selection.getBookmark(),e.selection.setRng(g),e.execCommand("createlink",!1,m[1]+m[2]),!1!==c&&e.dom.setAttrib(e.selection.getNode(),"target",c),e.selection.moveToBookmark(r),e.nodeChanged())}},t=function(t){var n;t.on("keydown",function(e){if(13!==e.keyCode);else r(t,-1,"")}),i.browser.isIE()?t.on("focus",function(){if(!n){n=!0;try{t.execCommand("AutoUrlDetect",!1,!0)}catch(e){}}}):(t.on("keypress",function(e){if(41!==e.keyCode);else r(t,-1,"(")}),t.on("keyup",function(e){if(32!==e.keyCode);else r(t,0,"")}))};!function n(){e.add("autolink",function(e){t(e)})}()}();