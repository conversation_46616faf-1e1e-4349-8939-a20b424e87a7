<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="How to use responsive options">
    <meta name="author" content="<PERSON>">
    <title>
      Responsive Demo | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/demos/demos.html">Demos</a> 
              </li>
              <li> <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a>  </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>Responsive</h1>
        </div>
      </div>
    </section>

    <!--  Demos -->
    <section id="demos">
      <div class="row">
        <div class="large-12 columns">
          <div class="owl-carousel owl-theme">
            <div class="item">
              <h4>1</h4>
            </div>
            <div class="item">
              <h4>2</h4>
            </div>
            <div class="item">
              <h4>3</h4>
            </div>
            <div class="item">
              <h4>4</h4>
            </div>
            <div class="item">
              <h4>5</h4>
            </div>
            <div class="item">
              <h4>6</h4>
            </div>
            <div class="item">
              <h4>7</h4>
            </div>
            <div class="item">
              <h4>8</h4>
            </div>
            <div class="item">
              <h4>9</h4>
            </div>
            <div class="item">
              <h4>10</h4>
            </div>
            <div class="item">
              <h4>11</h4>
            </div>
            <div class="item">
              <h4>12</h4>
            </div>
          </div>
          <h3 id="overview">Overview</h3>
          <blockquote>
            <p>Responsive option can be used for setting breakpoints and additional options within. Try changing your browser width to see what happens with Items and Navigations.</p>
          </blockquote>
          <h4 id="about-responsive-option">About responsive option</h4>
          <p>Setting of the responsive is very simple. Structure of responsive option:</p>
          <pre><code>responsive : {
    // breakpoint from 0 up
    0 : {
        option1 : value,
        option2 : value,
        ...
    },
    // breakpoint from 480 up
    480 : {
        option1 : value,
        option2 : value,
        ...
    },
    // breakpoint from 768 up
    768 : {
        option1 : value,
        option2 : value,
        ...
    }
}</code></pre>
          <h3 id="key-facts-">Key facts:</h3>
          <ul>
            <li>Each breakpoint key can be a Number value (like in example) or a string: &#39;480&#39;.</li>
            <li>Owl has an in-built sort option but it’s best to set from the smallest screens to the widest.</li>
            <li>Responsive options <strong>always</strong>  overwrite top level settings.</li>
            <li>As default, the responsive option is set to true so carousel always tries to fit the wrapper (even if media queries are not support IE7/IE8 etc).</li>
            <li>If you have non flexible layout then set <code>responsive:false</code>.</li>
          </ul>
          <h3 id="live-example">Live Example</h3>
          <pre><code>$(&#39;.owl-carousel&#39;).owlCarousel({
    loop:true,
    margin:10,
    responsiveClass:true,
    responsive:{
        0:{
            items:1,
            nav:true
        },
        600:{
            items:3,
            nav:false
        },
        1000:{
            items:5,
            nav:true,
            loop:false
        }
    }
})</code></pre>
          <hr>
          <h3 id="responsive-related-options-">Responsive related options:</h3>
          <h4 id="responsiveclass">responsiveClass</h4>
          <p>Optional helper class. Add &#39;owl-reponsive-&#39; + &#39;breakpoint&#39; class to main element.</p>
          <h4 id="responsivebaseelement">responsiveBaseElement</h4>
          <p>As default all responsive breakpoints are corresponding with <code>window</code> width. This option gives you an opportunity to change it to your own class/id like <code>responsiveBaseElement:&quot;.myCustomWrapper&quot;</code></p>
          <h4 id="responsiverefreshrate">responsiveRefreshRate</h4>
          <p>What this does is wait 200ms after you changed the browser width and performs refresh actions (calculating widths/ cloning items etc.) Default refresh rate is 200ms. I think this rate is optimal but you can change it if it’s to slow for you.</p>
          <p>As not every option is able to use responsive abilities, here’s a full list of responsive options.</p>
          <p>
            <div class="row">
          </p>
          <div class="large-6 columns">
            <h4 id="list-of-responsive-options">List of responsive options</h4>
            <ul>
              <li>items</li>
              <li>loop</li>
              <li>center</li>
              <li>mouseDrag</li>
              <li>touchDrag</li>
              <li>pullDrag</li>
              <li>freeDrag</li>
              <li>margin</li>
              <li>stagePadding</li>
              <li>merge</li>
              <li>mergeFit</li>
              <li>autoWidth</li>
              <li>autoHeight</li>
              <li>nav</li>
              <li>navRewind</li>
              <li>slideBy</li>
              <li>dots</li>
              <li>dotsEach</li>
              <li>autoplay</li>
              <li>autoplayTimeout</li>
              <li>smartSpeed</li>
              <li>fluidSpeed</li>
              <li>autoplaySpeed</li>
              <li>navSpeed</li>
              <li>dotsSpeed</li>
              <li>dragEndSpeed</li>
              <li>responsiveRefreshRate</li>
              <li>animateOut</li>
              <li>animateIn</li>
              <li>fallbackEasing</li>
              <li>callbacks</li>
              <li>info</li>
              <li>and all events</li>
            </ul>
          </div>
          <div class="large-6 columns">
            <h4 id="list-of-responsive-only-on-load">List of responsive only on load</h4>
            <ul>
              <li>startPosition</li>
              <li>URLhashListener</li>
              <li>navText</li>
              <li>dotsData</li>
              <li>lazyLoad</li>
              <li>lazyContent</li>
              <li>autoplayHoverPause</li>
              <li>responsiveBaseElement</li>
              <li>responsiveClass</li>
              <li>video</li>
              <li>videoHeight</li>
              <li>videoWidth</li>
              <li>nestedItemSelector</li>
              <li>itemElement</li>
              <li>stageElement</li>
              <li>navContainer</li>
              <li>dotsContainer</li>
              <li>and all classes options</li>
            </ul>
          </div>
          </div>
          <script>
            $(document).ready(function() {
              $('.owl-carousel').owlCarousel({
                loop: true,
                margin: 10,
                responsiveClass: true,
                responsive: {
                  0: {
                    items: 1,
                    nav: true
                  },
                  600: {
                    items: 3,
                    nav: false
                  },
                  1000: {
                    items: 5,
                    nav: true,
                    loop: false,
                    margin: 20
                  }
                }
              })
            })
          </script>
        </div>
      </div>
    </section>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>