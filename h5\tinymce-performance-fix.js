/**
 * TinyMCE 性能优化脚本
 * 解决触摸事件性能警告和其他性能问题
 */

(function() {
    'use strict';
    
    // 检测是否支持被动事件监听器
    let supportsPassive = false;
    try {
        const opts = Object.defineProperty({}, 'passive', {
            get: function() {
                supportsPassive = true;
            }
        });
        window.addEventListener('testPassive', null, opts);
        window.removeEventListener('testPassive', null, opts);
    } catch (e) {
        // 不支持被动事件监听器
    }
    
    // 优化 TinyMCE 触摸事件
    function optimizeTinyMCETouchEvents() {
        if (!supportsPassive) return;
        
        // 等待 TinyMCE 初始化完成
        const checkTinyMCE = setInterval(function() {
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                clearInterval(checkTinyMCE);
                
                // 获取编辑器容器
                const editorContainer = tinymce.activeEditor.getContainer();
                if (editorContainer) {
                    // 为编辑器容器添加被动触摸事件监听器
                    addPassiveTouchListeners(editorContainer);
                    
                    // 为编辑器内容区域添加被动触摸事件监听器
                    const contentArea = editorContainer.querySelector('.tox-edit-area');
                    if (contentArea) {
                        addPassiveTouchListeners(contentArea);
                    }
                    
                    // 为工具栏添加被动触摸事件监听器
                    const toolbar = editorContainer.querySelector('.tox-toolbar');
                    if (toolbar) {
                        addPassiveTouchListeners(toolbar);
                    }
                }
            }
        }, 100);
        
        // 10秒后停止检查
        setTimeout(() => clearInterval(checkTinyMCE), 10000);
    }
    
    // 为元素添加被动触摸事件监听器
    function addPassiveTouchListeners(element) {
        if (!element || !supportsPassive) return;
        
        // 移除可能存在的旧监听器
        element.removeEventListener('touchstart', function() {});
        element.removeEventListener('touchmove', function() {});
        element.removeEventListener('touchend', function() {});
        
        // 添加被动监听器
        element.addEventListener('touchstart', function(e) {
            // 被动监听器，不阻止默认行为
        }, { passive: true });
        
        element.addEventListener('touchmove', function(e) {
            // 被动监听器，不阻止默认行为
        }, { passive: true });
        
        element.addEventListener('touchend', function(e) {
            // 被动监听器，不阻止默认行为
        }, { passive: true });
    }
    
    // 优化 document.write 警告
    function suppressDocumentWriteWarnings() {
        // 保存原始的 document.write 方法
        const originalWrite = document.write;
        const originalWriteln = document.writeln;
        
        // 重写 document.write 方法，减少警告
        document.write = function(content) {
            // 在开发环境中，可以选择性地忽略某些 document.write 调用
            if (content && typeof content === 'string') {
                // 对于 TinyMCE 相关的 document.write，使用 DOM 操作替代
                if (content.includes('script') || content.includes('link')) {
                    console.warn('document.write 被拦截:', content);
                    return;
                }
            }
            
            // 其他情况调用原始方法
            return originalWrite.apply(this, arguments);
        };
        
        document.writeln = function(content) {
            // 类似处理 document.writeln
            if (content && typeof content === 'string') {
                if (content.includes('script') || content.includes('link')) {
                    console.warn('document.writeln 被拦截:', content);
                    return;
                }
            }
            
            return originalWriteln.apply(this, arguments);
        };
    }
    
    // 页面加载完成后执行优化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(optimizeTinyMCETouchEvents, 500);
            // suppressDocumentWriteWarnings(); // 可选：启用 document.write 拦截
        });
    } else {
        setTimeout(optimizeTinyMCETouchEvents, 500);
        // suppressDocumentWriteWarnings(); // 可选：启用 document.write 拦截
    }
    
    // 监听 TinyMCE 初始化事件
    if (typeof tinymce !== 'undefined') {
        tinymce.on('AddEditor', function(e) {
            const editor = e.editor;
            editor.on('init', function() {
                setTimeout(() => {
                    const container = editor.getContainer();
                    if (container) {
                        addPassiveTouchListeners(container);
                    }
                }, 100);
            });
        });
    }
    
})();
