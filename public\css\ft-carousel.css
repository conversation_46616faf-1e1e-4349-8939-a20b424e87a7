ul,
ol,
li,
div {
    margin: 0;
    padding: 0;
}

ul,
ol {
    list-style: none;
}

.ft-carousel {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.ft-carousel .carousel-inner2 {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
}

.ft-carousel .carousel-inner2 .carousel-item {
    float: left;
    height: 100%;
}

.ft-carousel .carousel-item img {
    width: 100%;
}

.ft-carousel .carousel-indicators {
    position: absolute;
    left: 30%;
    bottom: 10px;
    width: 100%;
    text-align: center;
    font-size: 0;
}

.ft-carousel .carousel-indicators span {
    display: inline-block;
    width: 12px;
    height: 12px;
    background-color: #fff;
    margin: 0 4px;
    border-radius: 50%;
    cursor: pointer;
}

.ft-carousel .carousel-indicators span.active {
    background-color: #de3a3a;
}

.ft-carousel .carousel-btn {
    position: absolute;
    top: 50%;
    width: 50px;
    height: 45px;
    margin-top: -25px;
    cursor: pointer;
}

.ft-carousel .carousel-prev-btn {
    left: 0;
    background: url(../img/prev.png) no-repeat;
}

.ft-carousel .carousel-next-btn {
    right: 0;
    background: url(../img/next.png) no-repeat;
}

.carousel-item {
    line-height: 336px;
    color: #fff;
    font-family: Arial Black
}

