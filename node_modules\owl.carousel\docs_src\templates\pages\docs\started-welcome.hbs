---
title: Welcome
subTitle: Getting Started
nav: docs
description: Owl Carousel Documentation

sort: 1

tags:
- Getting Started
---

{{#markdown }}
## Welcome

> No matter if you are a beginner or an advanced user, starting with <PERSON><PERSON> is easy.

### New Features

* Infinity Loop
* Center item
* Smart Speed
* Stage Padding
* Item Margin
* Ability to make almost all options responsive
* Various Widths
* Callback Events
* RTL
* YouTube/Vimeo/vzaar support (fetching thumbnails as well)
* Anchors navigation
* Merged Items
* and more...

### Compatibility

Owl Carousel 2.x.x is not compatibile with previous version 1.x.x. The idea stays the same and it has a lot in common with Owl1 but the core code was re-written from scratch and I’m very proud with all the new features.

Owl Carousel has been tested in following browsers/devices:

* Chrome
* Firefox
* Opera
* IE7/8/10/11
* iPad Safari
* iPod4 Safari
* Nexus 7 Chrome
* Galaxy S4
* Nokia 8s Windows8


### Library

Download a version that suits your needs:

* [Owl Carousel - {{ pkg.version }}](https://github.com/OwlCarousel2/OwlCarousel2/archive/{{ pkg.version }}.zip) - Distributed version - compiled and minified. Javascript, images and CSS included.
* [Owl Carousel Source - {{ pkg.version }}]({{ app.download }}) - Source files including this documentation. All wrapped in Grunt project.

### Files included

Distributed version structure:

```
owlcarousel/
├── assets/
│   ├── owl.carousel.css
│   ├── owl.carousel.min.css
│   ├── owl.theme.default.css
│   ├── owl.theme.default.min.css
│   ├── owl.theme.green.css
│   ├── owl.theme.green.min.css
│   └── owl.video.play.png
│
├── owl.carousel.js
├── owl.carousel.min.js
├── LICENSE-MIT
└── README.md
```

### Dependecies

Get the latest [jQuery](https://jquery.com/) or [Zepto](http://zeptojs.com/) library.
Minimum compatible jQuery version is 1.8.3 version.


### Next Step

####[Installation](started-installation.html)


{{/markdown }}
