<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Owl Carousel Documentation">
    <meta name="author" content="<PERSON>">
    <title>
      Options | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li> <a href="/OwlCarousel2/demos/demos.html">Demos</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a> 
              </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>API</h1>
        </div>
      </div>
    </section>
    <div id="docs">
      <div class="row">
        <div class="small-12 medium-3 large-3 columns">
          <ul class="side-nav">
            <li class="side-nav-head">Getting Started</li>
            <li> <a href="started-welcome.html">Welcome</a>  </li>
            <li> <a href="started-installation.html">Installation</a>  </li>
            <li> <a href="started-faq.html">FAQ</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">API</li>
            <li> <a href="api-options.html">Options</a>  </li>
            <li> <a href="api-classes.html">Classes</a>  </li>
            <li> <a href="api-events.html">Events</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Development</li>
            <li> <a href="dev-buildin-plugins.html">Built-in Plugins</a>  </li>
            <li> <a href="dev-plugin-api.html">Plugin API</a>  </li>
            <li> <a href="dev-styles.html">Sass Styles</a>  </li>
            <li> <a href="dev-external.html">External Libs</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Support</li>
            <li> <a href="support-contributing.html">Contributing</a>  </li>
            <li> <a href="support-changelog.html">Changelog</a>  </li>
            <li> <a href="support-contact.html">Contact</a>  </li>
          </ul>
        </div>
        <div class="small-12 medium-9 large-9 columns">
          <article class="docs-content">
            <h2 id="options">Options</h2>
            <blockquote>
              <p>List including all options from built-in plugins video, lazyload, autoheight and animate.</p>
            </blockquote>
            <h4 id="items">items</h4>
            <p>Type: <code>Number</code>
              <br /> Default: <code>3</code></p>
            <p>The number of items you want to see on the screen.</p>
            <hr>
            <h4 id="margin">margin</h4>
            <p>Type: <code>Number</code>
              <br /> Default: <code>0</code></p>
            <p>margin-right(px) on item.</p>
            <hr>
            <h4 id="loop">loop</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Infinity loop. Duplicate last and first items to get loop illusion.</p>
            <hr>
            <h4 id="center">center</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Center item. Works well with even an odd number of items.</p>
            <hr>
            <h4 id="mousedrag">mouseDrag</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>Mouse drag enabled.</p>
            <hr>
            <h4 id="touchdrag">touchDrag</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>Touch drag enabled.</p>
            <hr>
            <h4 id="pulldrag">pullDrag</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>Stage pull to edge.</p>
            <hr>
            <h4 id="freedrag">freeDrag</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Item pull to edge.</p>
            <hr>
            <h4 id="stagepadding">stagePadding</h4>
            <p>Type: <code>Number</code>
              <br /> Default: <code>0</code></p>
            <p>Padding left and right on stage (can see neighbours).</p>
            <hr>
            <h4 id="merge">merge</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Merge items. Looking for data-merge=&#x27;{number}&#x27; inside item..</p>
            <hr>
            <h4 id="mergefit">mergeFit</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>Fit merged items if screen is smaller than items value.</p>
            <hr>
            <h4 id="autowidth">autoWidth</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Set non grid content. Try using width style on divs.</p>
            <hr>
            <h4 id="startposition">startPosition</h4>
            <p>Type: <code>Number/String</code>
              <br /> Default: <code>0</code></p>
            <p>Start position or URL Hash string like &#x27;#id&#x27;.</p>
            <hr>
            <h4 id="urlhashlistener">URLhashListener</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Listen to url hash changes. data-hash on items is required.</p>
            <hr>
            <h4 id="nav">nav</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Show next/prev buttons.</p>
            <hr>
            <h4 id="rewind">rewind</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>Go backwards when the boundary has reached.</p>
            <hr>
            <h4 id="navtext">navText</h4>
            <p>Type: <code>Array</code>
              <br /> Default: <code>[&amp;#x27;next&amp;#x27;,&amp;#x27;prev&amp;#x27;]</code></p>
            <p>HTML allowed.</p>
            <hr>
            <h4 id="navelement">navElement</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>div</code></p>
            <p>DOM element type for a single directional navigation link.</p>
            <hr>
            <h4 id="slideby">slideBy</h4>
            <p>Type: <code>Number/String</code>
              <br /> Default: <code>1</code></p>
            <p>Navigation slide by x. &#x27;page&#x27; string can be set to slide by page.</p>
            <hr>
            <h4 id="slidetransition">slideTransition</h4>
            <p>Type: <code>String</code>
              <br /> Default: ``</p>
            <p>You can define the transition for the stage you want to use eg. linear.</p>
            <hr>
            <h4 id="dots">dots</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>Show dots navigation.</p>
            <hr>
            <h4 id="dotseach">dotsEach</h4>
            <p>Type: <code>Number/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Show dots each x item.</p>
            <hr>
            <h4 id="dotsdata">dotsData</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Used by data-dot content.</p>
            <hr>
            <h4 id="lazyload">lazyLoad</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Lazy load images. data-src and data-src-retina for highres. Also load images into background inline style if element is not &lt;img&gt;</p>
            <hr>
            <h4 id="lazyloadeager">lazyLoadEager</h4>
            <p>Type: <code>Number</code>
              <br /> Default: <code>0</code></p>
            <p>Eagerly pre-loads images to the right (and left when loop is enabled) based on how many items you want to preload.</p>
            <hr>
            <h4 id="autoplay">autoplay</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Autoplay.</p>
            <hr>
            <h4 id="autoplaytimeout">autoplayTimeout</h4>
            <p>Type: <code>Number</code>
              <br /> Default: <code>5000</code></p>
            <p>Autoplay interval timeout.</p>
            <hr>
            <h4 id="autoplayhoverpause">autoplayHoverPause</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Pause on mouse hover.</p>
            <hr>
            <h4 id="smartspeed">smartSpeed</h4>
            <p>Type: <code>Number</code>
              <br /> Default: <code>250</code></p>
            <p>Speed Calculate. More info to come..</p>
            <hr>
            <h4 id="fluidspeed">fluidSpeed</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>Number</code></p>
            <p>Speed Calculate. More info to come..</p>
            <hr>
            <h4 id="autoplayspeed">autoplaySpeed</h4>
            <p>Type: <code>Number/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>autoplay speed.</p>
            <hr>
            <h4 id="navspeed">navSpeed</h4>
            <p>Type: <code>Number/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Navigation speed.</p>
            <hr>
            <h4 id="dotsspeed">dotsSpeed</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>Number/Boolean</code></p>
            <p>Pagination speed.</p>
            <hr>
            <h4 id="dragendspeed">dragEndSpeed</h4>
            <p>Type: <code>Number/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Drag end speed.</p>
            <hr>
            <h4 id="callbacks">callbacks</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>Enable callback events.</p>
            <hr>
            <h4 id="responsive">responsive</h4>
            <p>Type: <code>Object</code>
              <br /> Default: <code>empty object</code></p>
            <p>Object containing responsive options. Can be set to false to remove responsive capabilities.</p>
            <hr>
            <h4 id="responsiverefreshrate">responsiveRefreshRate</h4>
            <p>Type: <code>Number</code>
              <br /> Default: <code>200</code></p>
            <p>Responsive refresh rate.</p>
            <hr>
            <h4 id="responsivebaseelement">responsiveBaseElement</h4>
            <p>Type: <code>DOM element</code>
              <br /> Default: <code>window</code></p>
            <p>Set on any DOM element. If you care about non responsive browser (like ie8) then use it on main wrapper. This will prevent from crazy resizing.</p>
            <hr>
            <h4 id="video">video</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Enable fetching YouTube/Vimeo/Vzaar videos.</p>
            <hr>
            <h4 id="videoheight">videoHeight</h4>
            <p>Type: <code>Number/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Set height for videos.</p>
            <hr>
            <h4 id="videowidth">videoWidth</h4>
            <p>Type: <code>Number/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Set width for videos.</p>
            <hr>
            <h4 id="animateout">animateOut</h4>
            <p>Type: <code>String/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Class for CSS3 animation out.</p>
            <hr>
            <h4 id="animatein">animateIn</h4>
            <p>Type: <code>String/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Class for CSS3 animation in.</p>
            <hr>
            <h4 id="fallbackeasing">fallbackEasing</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>swing</code></p>
            <p>Easing for CSS2 $.animate.</p>
            <hr>
            <h4 id="info">info</h4>
            <p>Type: <code>Function</code>
              <br /> Default: <code>false</code></p>
            <p>Callback to retrieve basic information (current item/pages/widths). Info function second parameter is Owl DOM object reference.</p>
            <hr>
            <h4 id="nesteditemselector">nestedItemSelector</h4>
            <p>Type: <code>String/Class</code>
              <br /> Default: <code>false</code></p>
            <p>Use it if owl items are deep nested inside some generated content. E.g &#x27;youritem&#x27;. Dont use dot before class name.</p>
            <hr>
            <h4 id="itemelement">itemElement</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>div</code></p>
            <p>DOM element type for owl-item.</p>
            <hr>
            <h4 id="stageelement">stageElement</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>div</code></p>
            <p>DOM element type for owl-stage.</p>
            <hr>
            <h4 id="navcontainer">navContainer</h4>
            <p>Type: <code>String/Class/ID/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Set your own container for nav.</p>
            <hr>
            <h4 id="dotscontainer">dotsContainer</h4>
            <p>Type: <code>String/Class/ID/Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Set your own container for nav.</p>
            <hr>
            <h4 id="checkvisible">checkVisible</h4>
            <p>Type: <code>Boolean</code>
              <br /> Default: <code>true</code></p>
            <p>If you know the carousel will always be visible you can set &#x60;checkVisibility&#x60; to &#x60;false&#x60; to prevent the expensive browser layout forced reflow the $element.is(&#x27;:visible&#x27;) does.</p>
            <hr>
            <h3 id="next-step">Next Step</h3>
            <h4 id="-classes-api-classes-html-">
              <a href="api-classes.html">Classes</a> 
            </h4>
          </article>
        </div>
      </div>
    </div>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>