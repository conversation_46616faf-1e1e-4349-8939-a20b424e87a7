const https = require('https');

// 微信公众号配置（需要替换为你的实际配置）
const WECHAT_CONFIG = {
    appId: 'wxd0334fe5bbc270d2',           // 替换为你的AppID
    appSecret: '842790a819268dfbe4adc0e58ec15814',   // 替换为你的AppSecret
    accessToken: null,              // 动态获取
    tokenExpireTime: 0              // token过期时间
};

/**
 * 获取微信稳定版access_token（推荐使用）
 * @param {boolean} forceRefresh 是否强制刷新，默认false
 * @returns {Promise<string>} access_token
 */
async function getWechatAccessToken(forceRefresh = false) {
    return new Promise((resolve, reject) => {
        // 如果不是强制刷新，检查token是否还有效（提前5分钟过期）
        if (!forceRefresh && WECHAT_CONFIG.accessToken && Date.now() < WECHAT_CONFIG.tokenExpireTime) {
            console.log('使用缓存的access_token，有效期至:', new Date(WECHAT_CONFIG.tokenExpireTime).toLocaleString());
            resolve(WECHAT_CONFIG.accessToken);
            return;
        }

        // 使用稳定版接口
        const url = 'https://api.weixin.qq.com/cgi-bin/stable_token';

        const postData = JSON.stringify({
            grant_type: 'client_credential',
            appid: WECHAT_CONFIG.appId,
            secret: WECHAT_CONFIG.appSecret,
            force_refresh: forceRefresh
        });

        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        console.log(`正在获取${forceRefresh ? '强制刷新' : '普通模式'}access_token...`);

        const req = https.request(url, options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    console.log('微信API响应:', result);

                    if (result.access_token) {
                        WECHAT_CONFIG.accessToken = result.access_token;
                        // 根据文档，稳定版接口会提前5分钟更新，所以我们提前3分钟过期确保安全
                        WECHAT_CONFIG.tokenExpireTime = Date.now() + (result.expires_in - 180) * 1000;

                        console.log('✅ 微信稳定版access_token获取成功!');
                        console.log(`   Token: ${result.access_token.substring(0, 20)}...`);
                        console.log(`   有效期: ${result.expires_in}秒`);
                        console.log(`   过期时间: ${new Date(WECHAT_CONFIG.tokenExpireTime).toLocaleString()}`);

                        resolve(result.access_token);
                    } else {
                        // 处理错误情况
                        const errorMsg = getWechatErrorMessage(result.errcode, result.errmsg);
                        reject(new Error(`获取access_token失败: ${errorMsg} (错误码: ${result.errcode})`));
                    }
                } catch (error) {
                    reject(new Error(`解析access_token响应失败: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error(`请求access_token失败: ${error.message}`));
        });

        req.write(postData);
        req.end();
    });
}

/**
 * 获取微信错误码对应的中文说明
 * @param {number} errcode 错误码
 * @param {string} errmsg 错误信息
 * @returns {string} 中文错误说明
 */
function getWechatErrorMessage(errcode, errmsg) {
    const errorMap = {
        40002: '不合法的凭证类型',
        40013: '不合法的AppID，请检查AppID的正确性',
        40125: '无效的AppSecret，请检查AppSecret的正确性',
        40164: 'IP不在白名单中，请将服务器IP添加到微信公众平台的IP白名单',
        41002: '缺少appid参数',
        41004: '缺少secret参数',
        43002: '需要POST请求',
        45009: '调用超过天级别频率限制',
        45011: 'API调用太频繁，请稍候再试',
        89503: '此次调用需要管理员确认，请耐心等候',
        89506: '该IP调用请求已被公众号管理员拒绝，请24小时后再试',
        89507: '该IP调用请求已被公众号管理员拒绝，请1小时后再试'
    };

    return errorMap[errcode] || errmsg || '未知错误';
}





/**
 * 注册微信相关的API路由
 * @param {Object} app Express应用实例
 */
function registerWechatRoutes(app) {

    // API接口：测试微信access_token获取
    app.get('/apis/wechat/test_token/', async (req, res) => {
        try {
            const accessToken = await getWechatAccessToken();

            res.json({
                status: 'ok',
                data: {
                    access_token: accessToken,
                    expire_time: new Date(WECHAT_CONFIG.tokenExpireTime).toLocaleString()
                },
                msg: 'access_token获取成功'
            });

        } catch (error) {
            console.error('获取access_token失败:', error);
            res.json({
                status: 'error',
                msg: error.message || '获取access_token失败'
            });
        }
    });


    console.log('微信公众号API路由已注册');
    console.log('可用的API端点:');
    console.log('  GET  /apis/wechat/test_token/ - 测试获取token');
}

// 如果直接运行此文件，则获取access_token
if (require.main === module) {
    console.log('开始获取微信access_token...');
    console.log('配置信息:');
    console.log(`  AppID: ${WECHAT_CONFIG.appId}`);
    console.log(`  AppSecret: ${WECHAT_CONFIG.appSecret.substring(0, 8)}...`);
    console.log('');

    // 直接获取access_token
    getWechatAccessToken()
        .then(accessToken => {
            console.log('🎉 成功获取access_token!');
            console.log(`Token: ${accessToken}`);
            console.log(`有效期至: ${new Date(WECHAT_CONFIG.tokenExpireTime).toLocaleString()}`);
            console.log('');
            console.log('你可以在其他代码中使用这个token了！');
        })
        .catch(error => {
            console.error('❌ 获取access_token失败:');
            console.error(error.message);
            console.log('');
            console.log('常见问题排查:');
            console.log('1. 检查AppID和AppSecret是否正确');
            console.log('2. 检查服务器IP是否已添加到微信白名单');
            console.log('3. 检查网络连接是否正常');
            console.log('4. 检查微信公众号是否已开通相应权限');
        });
}

module.exports = {
    getWechatAccessToken,
    registerWechatRoutes,
    WECHAT_CONFIG
};
