.hot-products {
    width: 100%;
    height: 716px; /* 939/1920 * 100 = 48.91vw */
    background: #F8F8F8;
    border-radius: 0px;
    padding: 67px 0 0 0; /* 60/1920 * 100 = 3.13vw */
    position: relative; 
}

.hot-products-title {
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: bold;
    font-size: 2.5vw; /* 48/1920 * 100 = 2.5vw */
    color: #00509F;
    line-height: 3.02vw; /* 58/1920 * 100 = 3.02vw */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 56px; /* 83/1920 * 100 = 4.32vw */
}

.hot-products-grid {
    display: grid;
    grid-template-columns: repeat(2, 25vw);
    gap: 2.08vw; /* 40px */
    justify-content: center; /* 居中整个网格 */
    max-width: 1000px; /* 1200/1920 * 100 = 62.5vw */
    width: 100%;
    margin: 0 auto;
    padding: 0 0.78vw; /* 15/1920 * 100 = 0.78vw */
}

.hot-product-item {
    width: 25vw; /* 480/1920 * 100 = 25vw */
    height: 420px; /* 520/1920 * 100 = 27.08vw */
    background: #FFFFFF;
    border-radius: 1.88vw; /* 36/1920 * 100 = 1.88vw */
    border: 0.1vw solid #707070; /* 2/1920 * 100 = 0.1vw */
    padding: 0vw 36px 0vw 36px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;

}

.hot-product-item:hover {
    border-color: #0050A2;
}

.hot-product-image {
    width: 100%;
    height: calc(100% - 116px); /* 总高度减去标题框高度和底部间距 */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 20px 0;
}

.hot-product-image img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
}

.hot-product-title {
    position: relative;
    bottom: 22px; /* 35/1920 * 100 = 1.82vw */
    width: 408px; /* 410/1920 * 100 = 21.35vw */
    height: 72px; /* 64/1920 * 100 = 3.33vw */
    background: #00509F;
    border-radius:16px; /* 12/1920 * 100 = 0.63vw */
    transition: background-color 0.3s ease;
    margin-top: auto; /* 将标题推到底部 */
    z-index: 1; /* 确保标题在图片上层 */
}

.hot-product-title a {
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 1.25vw; /* 24/1920 * 100 = 1.25vw */
    color: #FFFFFF;
    line-height: 2.76vw; /* 53/1920 * 100 = 2.76vw */
    text-align: center;
    font-style: normal;
    text-transform: none;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出文本 */
    text-overflow: ellipsis; /* 文本溢出时显示省略号 */
}

.hot-product-title:hover {
    background-color: #007DDB;
}

/* 热门产品轮播指示器样式 */
.hot-products-indicators {
    position: relative;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap:16px; /* 12/1920 * 100 = 0.63vw */
    padding: 0;
    margin: 44px auto 0 auto; /* 96/1920*100=4.99vw，设置与背景板的间距 */
    z-index: 10;
}

/* 英文界面热门产品标题样式调整 - 只修改字号 */
html[lang="en"] .hot-product-title a,
body.en-site .hot-product-title a,
.index_en .hot-product-title a,
[data-lang="en"] .hot-product-title a {
    font-size: 1.1vw; /* 英文界面字号缩小 */
}


.hot-products-indicator {
    width: 16px; /* 20/1920 * 100 = 1.04vw */
    height: 16px; /* 20/1920 * 100 = 1.04vw */
    background: #CCCCCC;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hot-products-indicator.active {
    background: #00509F;
}

.hot-products-side-box {
    position: absolute;
    margin-top: 354px;
    transform: translateY(-50%);
    width: 64px;
    height: 96px;
    background: #CCCCCC;
    border-radius: 8px;
    transition: background 0.3s;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.hot-products-side-box.left {
    left: 356px; /* 348/1920*100 */
    margin-right:40px;
}
.hot-products-side-box.right {
    right: 356px;
    margin-left:40px;
}
.hot-products-side-box:hover {
    background: #00509F;
}

/* 使用与轮播图相同的SVG箭头实现方式 */
.hot-products-arrow {
    width: 24px;
    height: 48px;
    display: block;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 24px 48px; /* 确保SVG按照指定尺寸显示 */
    transition: all 0.3s ease;
}

.hot-products-arrow.left {
    background-image: url('../images/home/<USER>');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.hot-products-arrow.right {
    background-image: url('../images/home/<USER>');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    transform: rotate(180deg);
}

/* 492px以下屏幕适配 */
@media screen and (max-width: 492px) {
    .hot-products {
        padding: 1.5vw;
    }

    .hot-products-title {
        margin-top: 2vw;
        margin-bottom: 2vw;
    }

    .hot-products-grid {
        max-width: 200vw;
        gap: 5vw; 
    }

    .hot-product-item {
        width: 28vw;
        height: 35vw;
        padding: 0vw 0vw; /* 减小内部padding */
    }

    .hot-product-image {
        margin-top: 0vw; /* 减小上边距 */
        margin-bottom: 5vw; /* 相应调整下边距 */
    }

    .hot-product-title {
        width: 25vw; /* 增加标题容器的宽度 */
        bottom: -3vw; /* 减少与网格底部的间距 */
        height: 4vw; /* 增加高度 */
    }

    .hot-product-title a {
        font-size: 1.8vw; /* 调整字体大小 */
        line-height: 4vw; /* 配合容器高度调整 */
    }

    .hot-products-indicators {
        margin-top: 6vw;
        position: relative;
        bottom: 3vw;
    }
    .hot-products-side-box.right {
        right: 15.13vw;
    }
    
    /* 491px以下英文界面特殊处理 */
    html[lang="en"] .hot-product-title a,
    body.en-site .hot-product-title a,
    .index_en .hot-product-title a,
    [data-lang="en"] .hot-product-title a,
    .english-version .hot-product-title a {
        font-size: 8px !important;
        transform: scale(0.6);
        transform-origin: center center; /* 修改为居中缩放 */
        line-height: 4vw; /* 与容器高度一致 */
        white-space: nowrap; /* 保持单行显示 */
        width: 166%; /* 增加宽度补偿缩放效果 (1/0.6 ≈ 1.66) */
        height: 100%;
        justify-content: center; /* 文本居中 */
        align-items: center; /* 垂直居中 */
        display: flex; /* 使用flex布局 */
        position: relative; /* 添加相对定位 */
        left: -33%; /* 向左偏移以补偿宽度增加 */
    }
}

