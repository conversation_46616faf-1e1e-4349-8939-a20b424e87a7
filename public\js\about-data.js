/**
 * About page data loading and display management
 */

// Current language environment (default Chinese)
let currentLang = 'zh';

// Initialization function
function initAboutData() {
    // Detect current page language
    detectPageLanguage();
    // Load about page data
    loadAboutData();
}

// Detect page language
function detectPageLanguage() {
    // Check if window.currentLanguage is set
    if (window.currentLanguage) {
        currentLang = window.currentLanguage;
        return;
    }
    
    // Get language parameter from URL
    const urlParams = new URLSearchParams(window.location.search);
    const langParam = urlParams.get('lang');
    
    // If URL has language parameter, use it
    if (langParam) {
        currentLang = langParam.toLowerCase();
    } else {
        // Otherwise check HTML tag's lang attribute
        const htmlLang = document.documentElement.lang;
        if (htmlLang) {
            currentLang = htmlLang.toLowerCase().includes('zh') ? 'zh' : 'en';
        }
    }
}

// Load about page data
function loadAboutData() {
    // Get company introduction data
    fetchAboutData();
    // Get development concept data
    fetchDevelopmentConcept();
    // Get honors data
    fetchHonors();
    // Get partners data
    fetchPartners();
    // Get company culture data
    fetchCompanyCulture();
}

// Get company introduction data
async function fetchAboutData() {
    try {
        // 获取所有company_profile数据
        const response = await $.ajax({
            url: '/apis/company_info/list',
            type: 'GET',
            data: {
                page: 1,
                page_size: 50, // 增大页面大小以确保获取所有数据
                filters: JSON.stringify({
                    info_type: 'company_profile',
                    lang: window.currentLanguage === 'en' ? 1 : 0,
                    show: true
                })
            }
        });

        if (response.status === 'ok' && response.data) {
            // 匹配标题为"关于贝启科技"或"About Beiqi"的记录
            const introTitle = window.currentLanguage === 'en' ? 'About Beiqi' : '关于贝启科技';
            const introData = response.data.filter(item => item.title === introTitle);
            
            console.log('Fetched data:', {
                language: window.currentLanguage,
                expectedTitle: introTitle,
                foundData: introData
            });

            if (introData.length > 0) {
                const sortedData = sortByDisplayOrder(introData);
                updateAboutContent(sortedData);
            } else {
                showNoDataMessage();
            }
        } else {
            showNoDataMessage();
        }
    } catch (error) {
        console.error('Error fetching about data:', error);
        showErrorMessage();
    }
}

// Get development concept data
async function fetchDevelopmentConcept() {
    try {
        console.log('=== 开始获取发展历程数据 ===');
        const historyTitle = window.currentLanguage === 'en' ? 'Development History' : '发展历程';
        console.log(`正在查找标题为"${historyTitle}"的发展历程数据`);
        
        let historyData = null;
        // 尝试获取至少5页数据，确保获取所有内容
        const pagesToTry = 5;
        console.log(`将尝试获取${pagesToTry}页数据，确保找到发展历程数据`);
        
        for (let currentPage = 1; currentPage <= pagesToTry; currentPage++) {
            console.log(`正在获取第${currentPage}页数据...`);
            
    try {
        const response = await $.ajax({
            url: '/apis/company_info/list',
            type: 'GET',
            data: {
                        page: currentPage,
                page_size: 50,
                filters: JSON.stringify({
                    info_type: 'company_profile',
                    lang: window.currentLanguage === 'en' ? 1 : 0,
                    show: true
                })
            }
        });

                if (response.status === 'ok' && response.data && response.data.length > 0) {
                    console.log(`第${currentPage}页获取到${response.data.length}条数据`);
                    
                    // 尝试精确匹配标题
                    historyData = response.data.find(item => item.title === historyTitle);
                    
                    if (historyData) {
                        console.log(`在第${currentPage}页找到精确匹配的发展历程数据`);
                        break; // 找到数据，退出循环
                    }
                    
                    // 如果没有找到精确匹配，尝试模糊匹配
                    if (!historyData) {
                        console.log(`第${currentPage}页未找到精确匹配，尝试模糊匹配`);
                        historyData = response.data.find(item => 
                            item.title && (
                                item.title.includes('发展历程') || 
                                item.title.includes('Development History')
                            )
                        );
                        
                        if (historyData) {
                            console.log(`在第${currentPage}页找到模糊匹配的发展历程数据`);
                            break; // 找到数据，退出循环
                        }
                    }
                    
                    // 注意：即使当前页数据少于50条，也继续查询后续页面
                    // 因为数据可能跨页分布，不能假设数据少于page_size就是最后一页
                    console.log(`继续查询第${currentPage + 1}页...`);
                    
                } else if (response.status === 'ok' && (!response.data || response.data.length === 0)) {
                    // 只有当返回的数据为空时，才认为已到达最后一页
                    console.log(`第${currentPage}页没有数据，已到达最后一页`);
                    break;
                } else {
                    console.log(`第${currentPage}页请求失败`);
                    // 请求失败，但仍继续尝试下一页
                }
            } catch (error) {
                console.error(`获取第${currentPage}页数据时出错:`, error);
                // 发生错误，但仍继续尝试下一页
            }
        }
        
        if (historyData) {
            console.log(`找到发展历程数据:`, historyData);
            // 更新内容
            updateDevelopmentHistory(historyData);
        } else {
            console.log(`搜索了${pagesToTry}页数据，未找到任何发展历程相关数据`);
        }
    } catch (error) {
        console.error('Error fetching development history:', error);
    }
}

// 更新发展历程内容
function updateDevelopmentHistory(historyData) {
    if (!historyData || !historyData.image_path) {
        console.log('No development history image found');
        return;
    }

    const imageContainer = document.querySelector('.development-image');
    if (!imageContainer) return;
    
    // 清空容器
    imageContainer.innerHTML = '';
    
    // 创建图片元素
    const img = document.createElement('img');
    img.src = normalizeImagePath(historyData.image_path);
    img.alt = window.currentLanguage === 'en' ? 'Development History' : '发展历程';
    
    // 添加到容器
    imageContainer.appendChild(img);
    
    // 图片加载错误处理
    img.onerror = function() {
        console.error('Failed to load development history image:', historyData.image_path);
    };
}

// Get honors data
async function fetchHonors() {
    try {
        const isEnglish = window.currentLanguage === 'en';
        let allHonorsData = [];
        let honorTitle = isEnglish ? 'Honorary certificate' : '荣誉证书'; // 默认标题
        
        console.log('=== 开始获取荣誉证书数据 ===');
        
        // 定义严格的标题匹配条件 - 无论英文还是中文界面，都使用中文"荣誉证书"作为匹配条件
        const exactTitleMatch = '荣誉证书';
        console.log(`严格筛选标题为: "${exactTitleMatch}" (无论中英文界面)`);
        
        // 尝试获取荣誉模块标题
        try {
            const titleResponse = await $.ajax({
            url: '/apis/company_info/list',
            type: 'GET',
            data: {
                page: 1,
                    page_size: 10,
                filters: JSON.stringify({
                        info_type: 'honor_title',
                        lang: isEnglish ? 1 : 0, // 仍然根据当前语言获取标题
                    show: true
                })
            }
        });

            if (titleResponse.status === 'ok' && titleResponse.data && titleResponse.data.length > 0) {
                // 尝试找到标题为"荣誉证书"或"荣誉"的记录
                const titleData = titleResponse.data.find(item => 
                    item.title === (isEnglish ? 'Honor Title' : '荣誉标题') ||
                    item.title === (isEnglish ? 'Honors' : '荣誉') ||
                    item.title === (isEnglish ? 'Honorary certificate' : '荣誉证书')
                );
                
                if (titleData && titleData.content) {
                    honorTitle = titleData.content;
                    console.log(`从数据库获取到荣誉标题: "${honorTitle}"`);
                }
            }
        } catch (error) {
            console.error('获取荣誉标题时出错:', error);
            // 使用默认标题继续
        }
        
        // 尝试获取至少5页数据，确保获取所有荣誉证书
        const pagesToTry = 5;
        console.log(`将尝试获取${pagesToTry}页数据，确保获取所有荣誉证书`);
        
        // 获取所有页面的数据 - 无论英文还是中文界面，都从中文数据中获取荣誉证书
        for (let currentPage = 1; currentPage <= pagesToTry; currentPage++) {
            console.log(`正在获取第${currentPage}页数据...`);
            
            try {
                const response = await $.ajax({
                    url: '/apis/company_info/list',
                    type: 'GET',
                    data: {
                        page: currentPage,
                        page_size: 100, // 每页100条
                        filters: JSON.stringify({
                            info_type: 'company_profile',
                            lang: 0, // 始终使用中文数据 (lang=0)
                            show: true
                        })
                    }
                });

                if (response.status === 'ok' && response.data && response.data.length > 0) {
                    console.log(`第${currentPage}页获取到${response.data.length}条数据`);
                    
                    // 输出该页数据的ID范围
                    if (response.data.length > 0) {
                        const ids = response.data.map(item => item.id || 0).sort((a, b) => a - b);
                        console.log(`第${currentPage}页数据ID范围: ${ids[0]} 到 ${ids[ids.length - 1]}`);
                    }
                    
                    // 严格筛选标题完全匹配的荣誉证书数据
                    const pageHonorsData = response.data.filter(item => 
                        item.image_path && item.title && item.title === exactTitleMatch
                    );
                    
                    console.log(`第${currentPage}页筛选出${pageHonorsData.length}条荣誉证书数据`);
                    
                    // 将当前页数据添加到总数据中
                    if (pageHonorsData.length > 0) {
                        allHonorsData = allHonorsData.concat(pageHonorsData);
                    }
                    
                    // 如果返回的数据少于请求的数量，可能已经到达最后一页
                    if (response.data.length < 100) {
                        console.log(`第${currentPage}页数据少于100条，可能已到达最后一页，但仍将继续获取下一页`);
                    }
                } else {
                    console.log(`第${currentPage}页没有获取到数据或请求失败，但仍将继续获取下一页`);
                }
            } catch (error) {
                console.error(`获取第${currentPage}页数据时出错:`, error);
                console.log(`将继续尝试获取下一页`);
            }
        }
        
        // 如果没有获取到数据，尝试使用更宽松的标题匹配
        if (allHonorsData.length === 0) {
            console.log('=== 未找到精确匹配的荣誉证书数据，尝试使用包含匹配 ===');
            
            // 再次尝试获取所有页面的数据，但使用包含匹配
            for (let currentPage = 1; currentPage <= pagesToTry; currentPage++) {
                try {
                    const response = await $.ajax({
                        url: '/apis/company_info/list',
                        type: 'GET',
                        data: {
                            page: currentPage,
                            page_size: 100,
                            filters: JSON.stringify({
                                info_type: 'company_profile',
                                lang: 0, // 始终使用中文数据
                                show: true
                            })
                        }
                    });
                    
                    if (response.status === 'ok' && response.data && response.data.length > 0) {
                        // 使用包含匹配而非完全匹配
                        const pageHonorsData = response.data.filter(item => 
                            item.image_path && item.title && item.title.includes(exactTitleMatch)
                        );
                        
                        console.log(`第${currentPage}页使用包含匹配筛选出${pageHonorsData.length}条荣誉证书数据`);
                        
                        if (pageHonorsData.length > 0) {
                            allHonorsData = allHonorsData.concat(pageHonorsData);
                        }
                    }
                } catch (error) {
                    console.error(`获取第${currentPage}页数据时出错:`, error);
                }
            }
        }
        
        // 最终统计
        console.log(`=== 最终获取到${allHonorsData.length}条荣誉证书数据 ===`);
        console.log('荣誉证书数据:');
        allHonorsData.forEach((item, index) => {
            console.log(`  [${index}] ID: ${item.id || '无ID'}, 标题: ${item.title || '无标题'}, 图片路径: ${item.image_path}`);
        });
        
        // 更新荣誉模块标题
        updateHonorTitle(honorTitle);
        
        // 更新内容
        updateHonors(allHonorsData, isEnglish);
    } catch (error) {
        console.error('Error fetching honors:', error);
    }
}

// 更新荣誉模块标题
function updateHonorTitle(title) {
    const honorTitleElement = document.querySelector('.honor-title');
    if (honorTitleElement) {
        console.log(`更新荣誉模块标题为: "${title}"`);
        honorTitleElement.textContent = title;
    } else {
        console.log('未找到荣誉标题元素');
    }
}

// 更新荣誉内容
function updateHonors(honorsData, isEnglish) {
    if (!honorsData || honorsData.length === 0) {
        console.log('No honors data found');
        return;
    }

    console.log(`准备展示${honorsData.length}张荣誉证书图片`);

    // 获取轮播容器
    const carouselContainer = document.querySelector('.honor-carousel');
    if (!carouselContainer) return;
    
    // 清空容器
    carouselContainer.innerHTML = '';
    
    // 创建胶卷式滚动容器
    const filmStrip = document.createElement('div');
    filmStrip.className = 'honor-film-strip';
    filmStrip.style.display = 'flex';
    filmStrip.style.height = '100%';
    filmStrip.style.alignItems = 'center';
    filmStrip.style.gap = '15px'; // 图片间距15px
    
    // 预处理图片数据
    const processedImages = [];
    
    // 预加载所有图片，获取实际尺寸
    const preloadPromises = honorsData.map((item, index) => {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                processedImages.push({
                    src: img.src,
                    width: img.width,
                    height: img.height,
                    title: item.title || (isEnglish ? 'Honorary certificate' : '荣誉证书'),
                    originalIndex: index
                });
                resolve();
            };
            img.onerror = () => {
                console.error('Failed to load image:', item.image_path);
                resolve();
            };
            img.src = normalizeImagePath(item.image_path);
        });
    });

    // 等待所有图片预加载完成
    Promise.all(preloadPromises).then(() => {
        // 如果没有成功加载的图片，显示提示
        if (processedImages.length === 0) {
            carouselContainer.innerHTML = '<div style="width:100%;text-align:center;padding:20px;">无法加载荣誉证书图片</div>';
            return;
        }
        
        // 对图片进行排序，确保顺序正确
        processedImages.sort((a, b) => a.originalIndex - b.originalIndex);
        
        console.log(`成功加载了${processedImages.length}张荣誉证书图片`);
        
        // 创建完整的胶卷
        createFilmStrip();
    });
    
    // 创建胶卷
    function createFilmStrip() {
        // 计算容器宽度
        const containerWidth = carouselContainer.offsetWidth;
        
        // 创建所有图片的完整集合
        const allImages = [];
        
        // 首先添加所有图片一次
        processedImages.forEach(imgData => {
            // 计算图片在胶卷中的高度比例
            const containerHeight = carouselContainer.offsetHeight * 0.9; // 使用90%的容器高度
            const aspectRatio = imgData.width / imgData.height;
            const displayHeight = containerHeight;
            const displayWidth = displayHeight * aspectRatio;
            
            allImages.push({
                ...imgData,
                displayWidth,
                displayHeight
            });
        });
        
        // 计算一组图片的总宽度
        let totalWidth = allImages.reduce((width, imgData) => {
            return width + imgData.displayWidth + 15; // 加上间距
        }, 0);
        
        console.log(`一组图片的总宽度: ${totalWidth}px`);
        
        // 创建胶卷元素 - 确保使用所有图片
        allImages.forEach(imgData => {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'honor-image-container';
            imageContainer.style.flexShrink = '0';
            imageContainer.style.height = '90%';
            imageContainer.style.display = 'flex';
            imageContainer.style.alignItems = 'center';
            imageContainer.style.justifyContent = 'center';
            
            const img = document.createElement('img');
            img.src = imgData.src;
            img.alt = imgData.title;
            img.style.height = '100%';
            img.style.objectFit = 'contain';
            
            imageContainer.appendChild(img);
            filmStrip.appendChild(imageContainer);
        });
        
        // 添加到容器
        carouselContainer.appendChild(filmStrip);
        
        // 开始无缝滚动动画
        startSmoothAnimation(filmStrip, totalWidth);
    }
    
    // 使用CSS动画实现平滑的无缝滚动
    function startSmoothAnimation(filmStrip, totalWidth) {
        // 保存所有原始图片元素
        const originalImages = Array.from(filmStrip.querySelectorAll('.honor-image-container'));
        console.log(`原始图片数量: ${originalImages.length}张`);
        
        // 输出原始图片信息
        originalImages.forEach((img, index) => {
            const imgElement = img.querySelector('img');
            console.log(`原始图片[${index}]: ${imgElement ? imgElement.src : '无图片元素'}`);
        });
        
        // 清空filmStrip
        filmStrip.innerHTML = '';
        
        // 创建包装容器，用于动画
        const wrapper = document.createElement('div');
        wrapper.className = 'honor-film-wrapper';
        wrapper.style.display = 'flex';
        wrapper.style.height = '100%';
        
        // 创建第一组图片
        const group1 = document.createElement('div');
        group1.className = 'honor-film-group group-one';
        group1.style.display = 'flex';
        group1.style.height = '100%';
        group1.style.alignItems = 'center';
        group1.style.gap = '15px'; // 图片间距15px
        group1.style.flexShrink = '0';
        
        originalImages.forEach(originalImg => {
            // 克隆原始图片元素
            const clonedImg = originalImg.cloneNode(true);
            group1.appendChild(clonedImg);
        });
        
        // 创建第二组图片（完全相同但独立的DOM元素）
        const group2 = document.createElement('div');
        group2.className = 'honor-film-group group-two';
        group2.style.display = 'flex';
        group2.style.height = '100%';
        group2.style.alignItems = 'center';
        group2.style.gap = '15px'; // 图片间距15px
        group2.style.flexShrink = '0';
        
        originalImages.forEach(originalImg => {
            // 再次克隆原始图片元素
            const clonedImg = originalImg.cloneNode(true);
            group2.appendChild(clonedImg);
        });
        
        // 将两组图片添加到包装容器
        wrapper.appendChild(group1);
        wrapper.appendChild(group2);
        
        // 将包装容器添加到filmStrip
        filmStrip.appendChild(wrapper);
        
        // 验证两组图片是否创建成功
        const groupOneImages = filmStrip.querySelectorAll('.group-one .honor-image-container');
        const groupTwoImages = filmStrip.querySelectorAll('.group-two .honor-image-container');
        
        console.log(`=== 两组图片创建结果 ===`);
        console.log(`第一组图片数量: ${groupOneImages.length}张`);
        console.log(`第二组图片数量: ${groupTwoImages.length}张`);
        
        // 输出第一组图片信息
        Array.from(groupOneImages).forEach((img, index) => {
            const imgElement = img.querySelector('img');
            console.log(`第一组图片[${index}]: ${imgElement ? imgElement.src : '无图片元素'}`);
        });
        
        // 输出第二组图片信息
        Array.from(groupTwoImages).forEach((img, index) => {
            const imgElement = img.querySelector('img');
            console.log(`第二组图片[${index}]: ${imgElement ? imgElement.src : '无图片元素'}`);
        });
        
        // 计算每组图片的总宽度
        // 测量group1的实际宽度
        const group1Width = group1.scrollWidth;
        console.log(`第一组图片实际宽度: ${group1Width}px`);
        
        // 设置CSS动画
        wrapper.style.width = `${group1Width * 2}px`; // 两倍宽度，包含两组相同的图片
        
        // 移除任何可能存在的动画
        wrapper.style.animation = 'none';
        wrapper.style.transition = 'none';
        wrapper.style.transform = 'translateX(0)';
        
        // 强制重排
        void wrapper.offsetWidth;
        
        // 计算动画持续时间 - 使用较慢的速度确保平滑
        const duration = Math.max(group1Width * 0.01, 30); // 恢复原来的速度，至少30秒一次循环
        console.log(`动画持续时间: ${duration}秒`);
        
        // 创建或更新CSS @keyframes 规则
        let styleSheet = document.getElementById('film-scroll-keyframes');
        if (!styleSheet) {
            styleSheet = document.createElement('style');
            styleSheet.id = 'film-scroll-keyframes';
            document.head.appendChild(styleSheet);
        }
        
        // 更新keyframes规则 - 使用实际测量的宽度
        styleSheet.textContent = `
            @keyframes filmScroll {
                0% { transform: translateX(0); }
                100% { transform: translateX(-${group1Width}px); }
            }
        `;
        
        // 应用动画
        startAnimation();
        
        // 监听动画迭代完成事件
        wrapper.addEventListener('animationiteration', () => {
            console.log('动画循环完成一次');
        });
        
        // 使用Intersection Observer监控荣誉区域是否在视口中
        const honorSection = document.querySelector('.honor-section');
        if (honorSection && 'IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 荣誉区域进入视口，开始动画
                        console.log('荣誉区域进入视口，开始动画');
                        startAnimation();
                    } else {
                        // 荣誉区域离开视口，暂停动画
                        console.log('荣誉区域离开视口，暂停动画');
                        pauseAnimation();
                    }
                });
            }, {
                threshold: 0.1 // 当10%的荣誉区域可见时触发
            });
            
            // 开始观察
            observer.observe(honorSection);
        }
        
        // 开始动画
        function startAnimation() {
            wrapper.style.animation = `filmScroll ${duration}s linear infinite`;
        }
        
        // 暂停动画
        function pauseAnimation() {
            // 保存当前位置
            const computedStyle = window.getComputedStyle(wrapper);
            const transform = computedStyle.getPropertyValue('transform');
            wrapper.style.animation = 'none';
            wrapper.style.transform = transform;
        }
    }
}

// Get company culture data
async function fetchCompanyCulture() {
    try {
        console.log('开始获取企业文化数据...');
        console.log('当前语言:', window.currentLanguage);
        
        const response = await $.ajax({
            url: '/apis/company_info/list',
            type: 'GET',
            data: {
                page: 1,
                page_size: 50,
                filters: JSON.stringify({
                    info_type: 'company_profile',
                    lang: window.currentLanguage === 'en' ? 1 : 0,
                    show: true
                })
            }
        });

        if (response.status === 'ok' && response.data) {
            // 匹配企业文化标题
            const cultureTitle = window.currentLanguage === 'en' ? 'Company Culture' : '企业文化';
            console.log('查找的标题:', cultureTitle);
            
            // 打印所有数据的标题
            console.log('所有数据的标题:');
            response.data.forEach((item, index) => {
                console.log(`[${index}] 标题: "${item.title}", 内容: "${item.content}"`);
            });

            const cultureData = response.data.filter(item => item.title === cultureTitle);
            console.log('找到的企业文化数据数量:', cultureData.length);

            if (cultureData.length > 0) {
                console.log('准备更新企业文化内容...');
                // 遍历所有企业文化数据
                cultureData.forEach((item, index) => {
                    console.log(`处理第${index + 1}条企业文化数据`);
                    updateCompanyCulture(item, index);
                });
            } else {
                console.warn('未找到企业文化数据');
            }
        } else {
            console.warn('API响应异常:', response);
        }
    } catch (error) {
        console.error('获取企业文化数据时出错:', error);
    }
}

// Update company culture content
function updateCompanyCulture(data, dataIndex) {
    if (!data || !data.content) {
        console.warn('无效的企业文化数据:', data);
        return;
    }

    console.log(`处理第${dataIndex + 1}条数据 - 原始内容:`, data.content);
    
    // 将内容按换行符分割,处理所有可能的换行符
    const parts = data.content.split(/[\r\n]+/).map(part => part.trim()).filter(part => part);
    console.log(`第${dataIndex + 1}条数据分割后的内容:`, parts);

    // 如果分割后不是2个部分,说明格式不对
    if (parts.length !== 2) {
        console.warn(`第${dataIndex + 1}条数据格式不正确,应该包含标题和描述`);
        return;
    }

    const [title, description] = parts;
    console.log(`第${dataIndex + 1}条数据解析结果 - 标题:`, title, '描述:', description);

    // 获取所有文化项容器
    const cultureItems = document.querySelectorAll('.culture-item');
    console.log('找到的文化项容器数量:', cultureItems.length);

    // 如果序号在有效范围内,就更新对应位置的内容
    if (dataIndex < cultureItems.length) {
        const targetItem = cultureItems[dataIndex];
        console.log(`更新第${dataIndex + 1}个模块`);

        // 更新图标
        const iconImg = targetItem.querySelector('.culture-icon-img');
        if (iconImg) {
            const iconPath = `images/about/减去 ${dataIndex + 1}.png`;
            console.log(`设置第${dataIndex + 1}个模块图标:`, iconPath);
            iconImg.src = iconPath;
        }

        // 更新标题
        const titleElement = targetItem.querySelector('.culture-item-title');
        if (titleElement) {
            console.log(`设置第${dataIndex + 1}个模块标题:`, title);
            titleElement.textContent = title;
        }

        // 更新描述
        const textElement = targetItem.querySelector('.culture-item-text');
        if (textElement) {
            console.log(`设置第${dataIndex + 1}个模块描述:`, description);
            textElement.textContent = description;
        }
    } else {
        console.warn(`第${dataIndex + 1}条数据超出容器数量范围`);
    }
}

// 监听窗口大小变化
window.addEventListener('resize', function() {
    if (isEnglishSite()) {
        const isSmallScreen = window.innerWidth <= 491;
        // 重新加载数据以切换显示方式
        fetchDevelopmentConcept();
        fetchHonors();
    }
});

// Get partners data
async function fetchPartners() {
    try {
        const response = await $.ajax({
            url: '/apis/get_partner_pic/',
            type: 'POST',
            data: {
                page: 1,
                page_size: 100,
                filters: JSON.stringify({
                    show: true
                })
            }
        });

        if (response.status === 'ok' && response.data_list) {
            // Sort by display order
            const sortedPartners = sortByDisplayOrder(response.data_list);
            updatePartners(sortedPartners);
        }
    } catch (error) {
        console.error('Error fetching partner data:', error);
        showPartnerError();
    }
}

// Show no data message
function showNoDataMessage() {
    const aboutTextContent = document.querySelector('.about-text-content');
    if (aboutTextContent) {
        aboutTextContent.textContent = window.currentLanguage === 'en' ? 
            'No company introduction information available' : 
            '暂无公司简介信息';
    }
}

// Show error message
function showErrorMessage() {
    const aboutTextContent = document.querySelector('.about-text-content');
    if (aboutTextContent) {
        aboutTextContent.textContent = window.currentLanguage === 'en' ? 
            'Failed to get company information, please try again later' : 
            '获取公司简介信息失败，请稍后再试';
    }
}

// Show partner error
function showPartnerError() {
    const partnerContent = document.querySelector('.partner-content');
    if (partnerContent) {
        partnerContent.innerHTML = '<div style="text-align: center; padding: 30px;">暂无合作伙伴信息</div>';
    }
}

// Sort by display order
function sortByDisplayOrder(items) {
    return items.sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
}

// Update page content
function updateAboutContent(data) {
    if (!data || data.length === 0) {
        showNoDataMessage();
        return;
    }

    // 更新背景图片
    const backgroundImg = document.querySelector('.about-bg-img');
    if (backgroundImg && data[0].image_path) {
        // 设置图片源，使用固定尺寸
        backgroundImg.src = normalizeImagePath(data[0].image_path);
        backgroundImg.alt = window.currentLanguage === 'en' ? 'Bearkey Background Image' : '贝启科技背景图';
        
        // 图片加载完成后调整文本框位置（如果需要）
        backgroundImg.onload = function() {
            // 在较大的屏幕上使用固定位置，响应式样式会在CSS中处理小屏幕
            const textBox = document.querySelector('.about-text-box');
            if (textBox && window.innerWidth > 1500) {
                textBox.style.top = '590px'; // 固定位置
            }
        };
    }

    // 更新标题
    const aboutTextTitle = document.querySelector('.about-text-title');
    if (aboutTextTitle) {
        aboutTextTitle.textContent = data[0].title || (window.currentLanguage === 'en' ? 'About Bearkey' : '关于贝启科技');
    }

    // 更新简介文本内容
    const aboutTextContent = document.querySelector('.about-text-content');
    if (aboutTextContent && data[0].content) {
        // 将换行符替换为<br>标签，保留换行但不添加其他样式
        const content = data[0].content.replace(/\n/g, '<br>');
        aboutTextContent.innerHTML = content;
    }
}

// Update partners display
function updatePartners(partners) {
    if (!partners || partners.length === 0) {
        showPartnerError();
        return;
    }

    const partnerContent = document.querySelector('.partner-content');
    if (!partnerContent) return;

    // Clear existing content
    partnerContent.innerHTML = '';

    // 创建简单的图片容器
    const partnerWrapper = document.createElement('div');
    partnerWrapper.style.display = 'flex';
    partnerWrapper.style.flexWrap = 'wrap';
    partnerWrapper.style.justifyContent = 'center';
    partnerWrapper.style.gap = '0'; // 移除间距
    
    // 添加所有合作伙伴图片
    partners.forEach(partner => {
        if (partner && partner.pic) {
    const img = document.createElement('img');
            img.src = normalizeImagePath(partner.pic);
    img.alt = '合作伙伴';
            // 移除所有边距和间距
            img.style.margin = '0';
            img.style.padding = '0';
            img.style.verticalAlign = 'top'; // 防止图片底部的默认间距
            img.style.display = 'block'; // 防止内联元素的空白
            
            // 预加载图片以获取原始尺寸
            const tempImg = new Image();
            tempImg.onload = function() {
                // 图片加载完成后，使用原始尺寸
                console.log(`合作伙伴图片原始尺寸: ${this.width}x${this.height}`);
                img.style.width = 'auto';
                img.style.height = 'auto';
    };
            tempImg.src = normalizeImagePath(partner.pic);
            
            // 创建一个无间距的容器
            const imgContainer = document.createElement('div');
            imgContainer.style.margin = '0';
            imgContainer.style.padding = '0';
            imgContainer.style.fontSize = '0';
            imgContainer.style.lineHeight = '0';
            imgContainer.appendChild(img);
            
            partnerWrapper.appendChild(imgContainer);
    }
    });
    
    // 将图片容器添加到内容区域
    partnerContent.appendChild(partnerWrapper);
}

// Process image path
function normalizeImagePath(imgPath) {
    if (!imgPath) return '';
    
    // Remove possible Windows path format
    imgPath = imgPath.replace(/\\/g, '/');
    
    // If it's a complete URL, return directly
    if (imgPath.startsWith('http://') || imgPath.startsWith('https://')) {
        return imgPath;
    }
    
    // If it's a relative path, ensure it starts with /
    return imgPath.startsWith('/') ? imgPath : '/' + imgPath;
}

// Helper function to check if site is English
function isEnglishSite() {
    return window.currentLanguage === 'en' || document.documentElement.lang.toLowerCase().includes('en');
}

// Page load complete
document.addEventListener('DOMContentLoaded', loadAboutData); 