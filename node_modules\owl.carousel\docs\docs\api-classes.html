<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Owl Carousel Documentation">
    <meta name="author" content="<PERSON>">
    <title>
      Classes | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li> <a href="/OwlCarousel2/demos/demos.html">Demos</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a> 
              </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>API</h1>
        </div>
      </div>
    </section>
    <div id="docs">
      <div class="row">
        <div class="small-12 medium-3 large-3 columns">
          <ul class="side-nav">
            <li class="side-nav-head">Getting Started</li>
            <li> <a href="started-welcome.html">Welcome</a>  </li>
            <li> <a href="started-installation.html">Installation</a>  </li>
            <li> <a href="started-faq.html">FAQ</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">API</li>
            <li> <a href="api-options.html">Options</a>  </li>
            <li> <a href="api-classes.html">Classes</a>  </li>
            <li> <a href="api-events.html">Events</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Development</li>
            <li> <a href="dev-buildin-plugins.html">Built-in Plugins</a>  </li>
            <li> <a href="dev-plugin-api.html">Plugin API</a>  </li>
            <li> <a href="dev-styles.html">Sass Styles</a>  </li>
            <li> <a href="dev-external.html">External Libs</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Support</li>
            <li> <a href="support-contributing.html">Contributing</a>  </li>
            <li> <a href="support-changelog.html">Changelog</a>  </li>
            <li> <a href="support-contact.html">Contact</a>  </li>
          </ul>
        </div>
        <div class="small-12 medium-9 large-9 columns">
          <article class="docs-content">
            <h2 id="classes">Classes</h2>
            <p>This is an example of how html is rendered. With the following options you can change almost every class the way you want</p>
            <pre><code>&lt;div class=&quot;owl-carousel owl-theme owl-loaded&quot;&gt;
    &lt;div class=&quot;owl-stage-outer&quot;&gt;
        &lt;div class=&quot;owl-stage&quot;&gt;
            &lt;div class=&quot;owl-item&quot;&gt;...&lt;/div&gt;
            &lt;div class=&quot;owl-item&quot;&gt;...&lt;/div&gt;
            &lt;div class=&quot;owl-item&quot;&gt;...&lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class=&quot;owl-nav&quot;&gt;
        &lt;div class=&quot;owl-prev&quot;&gt;prev&lt;/div&gt;
        &lt;div class=&quot;owl-next&quot;&gt;next&lt;/div&gt;
    &lt;/div&gt;
    &lt;div class=&quot;owl-dots&quot;&gt;
        &lt;div class=&quot;owl-dot active&quot;&gt;&lt;span&gt;&lt;/span&gt;&lt;/div&gt;
        &lt;div class=&quot;owl-dot&quot;&gt;&lt;span&gt;&lt;/span&gt;&lt;/div&gt;
        &lt;div class=&quot;owl-dot&quot;&gt;&lt;span&gt;&lt;/span&gt;&lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
            <h3 id="options">Options</h3>
            <h4 id="refreshclass">refreshClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-refresh</code></p>
            <p>Class during refresh.</p>
            <hr>
            <h4 id="loadingclass">loadingClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-loading</code></p>
            <p>Class during load.</p>
            <hr>
            <h4 id="loadedclass">loadedClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-loaded</code></p>
            <p>Class after load.</p>
            <hr>
            <h4 id="rtlclass">rtlClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-rtl</code></p>
            <p>Class for right to left mode.</p>
            <hr>
            <h4 id="dragclass">dragClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-drag</code></p>
            <p>Class for mouse drag mode.</p>
            <hr>
            <h4 id="grabclass">grabClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-grab</code></p>
            <p>Class during mouse drag.</p>
            <hr>
            <h4 id="stageclass">stageClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-stage</code></p>
            <p>Stage class.</p>
            <hr>
            <h4 id="stageouterclass">stageOuterClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-stage-outer</code></p>
            <p>Stage outer class.</p>
            <hr>
            <h4 id="navcontainerclass">navContainerClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-nav</code></p>
            <p>Navigation container class.</p>
            <hr>
            <h4 id="navclass">navClass</h4>
            <p>Type: <code>Array</code>
              <br /> Default: <code>[&amp;#x27;owl-prev&amp;#x27;,&amp;#x27;owl-next&amp;#x27;]</code></p>
            <p>Navigation buttons classes.</p>
            <hr>
            <h4 id="dotclass">dotClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-dot</code></p>
            <p>Dot Class.</p>
            <hr>
            <h4 id="dotsclass">dotsClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-dots</code></p>
            <p>Dots container class.</p>
            <hr>
            <h4 id="autoheightclass">autoHeightClass</h4>
            <p>Type: <code>String</code>
              <br /> Default: <code>owl-height</code></p>
            <p>Auto height class.</p>
            <hr>
            <h4 id="responsiveclass">responsiveClass</h4>
            <p>Type: <code>String|Boolean</code>
              <br /> Default: <code>false</code></p>
            <p>Optional helper class. Add &#x27;&lt;responsiveClass&gt;-&lt;breakpoint&gt;&#x27; class to main element. Can be used to stylize content on given breakpoint.</p>
            <hr>
            <h3 id="next-step">Next Step</h3>
            <h4 id="-events-api-events-html-">
              <a href="api-events.html">Events</a> 
            </h4>
          </article>
        </div>
      </div>
    </div>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>