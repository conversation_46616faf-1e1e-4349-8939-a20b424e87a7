@import url('http://fonts.googleapis.com/css?family=Noto+Serif:400,400italic,700|Open+Sans:400,600,700'); 
@import url('font-awesome.css');  
@import url('animate.css');
@import url('footer.css');  /* 引入页脚样式 */

body {
	font-family:'Open Sans', Arial, sans-serif;
	font-size:14px;
	font-weight:300;
	line-height:1.6em;
	color:#656565;
	/* background: #EFEFEF; */
}

a:active {
	outline:0;
}

.clear {
	clear: both;
}

h1,h2, h3, h4, h5, h6 {
	font-family:'Open Sans', Arial, sans-serif;
	font-weight: 600;
	line-height:1.1em;
	color:#333;
	margin-bottom: 20px;
}
 h2{
    font-size: 26px;
    font-weight: 700;
}
.container {
	padding: 0 20px;
	position: relative;
	max-width: 1200px;
	margin: 0 auto;
	width: 100%;
}
.help-block ul li {
    color: red;
}
.form-control {
    margin-bottom: 15px;
}
#wrapper{
	width:100%;
	margin:0;	
	padding:0;
}

.row,.row-fluid {
	margin-bottom:30px;
}

.row .row,.row-fluid .row-fluid{
	margin-bottom:30px;
}

.row.nomargin,.row-fluid.nomargin {
	margin-bottom:0;
}

img.img-polaroid {
	margin:0 0 20px 0;
}
.img-box {
	max-width:100%;
}
.flex-control-nav li{
display:none;
}
.tlinks{text-indent:-9999px;height:0;line-height:0;font-size:0;overflow:hidden;}
/*  Header
==================================== */


/*Client Slider*/
.clients{
	padding:35px 0;
}
.clients-control{
	    position: absolute;
    right: 20px;
    top: 5px;
}
.clients-slider .owl-item{margin:0 10px; display:inline-block;}
.clients-slider .item img{
	
display: block;
	
/* background-color:#fafafa; */
}
.clients-slider .item img.colored {
	top: 0;
	display: none;
	/* background-color:#f5f5f5; */
}
.clients-slider .item {
    margin: 1px;
}
.clients-slider .item:hover img.colored{display:block;}
.clients-slider .item:hover img{display:none;}
.clients-control .btn,
.clients-control .btn{
	margin-bottom:0px;
	margin-top: 17px;
	padding: 0px 6px;
	font-size: 11px;
}



ul.nav li.dropdown a {
	z-index:1000;
	display:block;
}

 select.selectmenu {
	display:none;
}
.pageTitle{
color: #fff;
margin: 30px 0 3px;
display: inline-block;
}
 
#banner{
	width: 100%;
	background:#000;
	position:relative;
	margin:0;
	padding:0;
	float: left;
	width: 100%;
}

/*  Sliders
==================================== */
/* --- flexslider --- */
#main-slider:before {
    content: '';
    width: 100%;
    height: 100%;
    background: rgba(0, 148, 255, 0.74);
    z-index: 1;
    position: absolute;
}
.flex-direction-nav a{
display:none;
}
.flexslider {
	padding:0;
	position: relative;
	zoom:1;
	background: #055999;
}
.flex-direction-nav .flex-prev{
left:0px; 
}
.flex-direction-nav .flex-next{ 
right:0px;
}
.flex-caption {zoom: 1;bottom: 0;background-color: transparent;color: #fff;margin: 0;padding: 25px 25px 25px 30px;position: absolute;left: 0;text-align: left;margin: 10px auto;right: 0px;display: inline-block;margin-left: 12%;}
.flex-caption h3 {color: #ffffff;margin-bottom: 15px;text-transform: uppercase;font-size: 38px;font-weight: bold;}
.flex-caption p {margin: 12px 0 56px;font-size: 20px;color: #ffffff;}
.skill-home{margin-bottom:50px;float: left;width: 100%;}
.c1{
    background: #f8f8f8;
    border: 1px solid #e7e7e7;
}
.c2{
    background: #f8f8f8;
    border: 1px solid #e7e7e7;
}
.c3{
    background: #f8f8f8;
    border: 1px solid #e7e7e7;
}
.c4{
    background: #f8f8f8;
    border: 1px solid #e7e7e7;
}
.skill-home .icons {padding: 36px 0 14px 0px;width: 100%;color: #797979;font-size: 42px;font-size: 68px;text-align: center;-ms-border-radius: 50%;-moz-border-radius: 50%;-webkit-border-radius: 50%;border-radius: 0;display: inline-table;float: left;}
.skill-home h2 {
padding-top: 20px;
font-size: 36px;
font-weight: 700;
} 
.skill-home h3 { 
font-size: 20px;
font-weight: 600;
}
.skill-home a {color: #ffffff;text-decoration: none;font-size: 13px;background: #055999;padding: 7px 20px;margin-top: 10px;display: inline-block;border: 1px solid #055999;}
.skill-home .box:hover{
	background: #ffffff;
	/* cursor:pointer; */
	/* color: #fff; */
	border: 1px solid #055999;
}
.skill-home .box:hover .icons,
.skill-home .box:hover h3, .skill-home .box:hover a {
    color: #055999;
    transition: all 0.3s ease;
}
.skill-home a:hover {
    background: transparent;
    color: #02477b;
    border: 1px solid #055999;
}
.testimonial-solid {
padding: 50px 0 60px 0;
margin: 0 0 0 0;
background: #FFFFFF;
text-align: center;
}
.testi-icon-area {
text-align: center;
position: absolute;
top: -84px;
    margin: 0 auto;
width: 100%;
}
.testi-icon-area .quote {
padding: 15px 0 0 0;
margin: 0 0 0 0;
background: #ffffff;
text-align: center;
color: #1891EC;
display: inline-table;
width: 70px;
height: 70px;
-ms-border-radius: 50%;
-moz-border-radius: 50%;
-webkit-border-radius: 50%;
border-radius: 50%;
font-size: 42px; 
border: 1px solid #1891EC;
display: none;
}

.testi-icon-area .carousel-inner { 
margin: 20px 0;
}
.carousel-indicators {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 12px;
    padding: 0;
    margin: 0 auto;
    z-index: 10;
    width: 100%;
    text-align: center;
}

.carousel-indicators .indicator {
    width: 16px; /* 固定16px宽度 */
    height: 16px; /* 固定16px高度 */
    background: #CCCCCC; /* 默认灰色 */
    border-radius: 4px 4px 4px 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.carousel-indicators .indicator.active {
    background: #00509F; /* 激活状态为蓝色 */
}

.text-center img {
margin: auto;
}
.aboutUs{padding: 80px 0 0;background: #055999;color: #fff;}
img.img-center {
margin: 0 auto;
display: block;
max-width: 100%;
}
.aboutUs h2 {
    color: #fff;
}
/* Testimonial
----------------------------------*/
.testimonial-area {
padding: 0 0 0 0;
margin:0;
background: url(../img/low-poly01.jpg) fixed center center;
background-size: cover;
-webkit-background-size: cover;
-moz-background-size: cover;
-ms-background-size: cover;
}
.testimonial-solid p {
color: #000000;
font-size: 16px;
line-height: 30px;
font-style: italic;
} 
section.jumbobox {
	background:#fff;
	padding: 28px 0 0 0;
	float: left;
	width: 100%;
    text-align: center;
}
.team-member{
	text-align:center;
}
.team-member h4{
	padding:10px 0 0;
	text-align:center;
	margin:0;
}
/* Clients
------------------------------------ */
#clients {
  padding: 67px 0; }
  #clients .client .img {
    height: 76px;
    width: 138px;
    cursor: pointer;
    -webkit-transition: box-shadow .1s linear;
    -moz-transition: box-shadow .1s linear;
    transition: box-shadow .1s linear; }
    #clients .client .img:hover {
      cursor: pointer;
      /*box-shadow: 0px 0px 2px 0px rgb(155, 155, 155);*/
      border-radius: 8px; }
  #clients .client .client1 {
    background: url("../img/client1.png") 0 -75px; }
    #clients .client .client1:hover {
      background-position: 1px 0px; }
  #clients .client .client2 {
    background: url("../img/client2.png") 0 -75px; }
    #clients .client .client2:hover {
      background-position: -1px 0px; }
  #clients .client .client3 {
    background: url("../img/client3.png") 0 -76px; }
    #clients .client .client3:hover {
      background-position: 0px 0px; }


/* Content
==================================== */

#content {
	position:relative;
	/* background:#fff; */
	padding: 30px 0 0px 0;
}

#content img {
	max-width:100%;
	height:auto;
}
 
.cta-text {
	text-align: center;
	margin-top:10px;
}


.big-cta .cta {
	margin-top:10px;
}
 
.box {
	width: 100%;
	/* border: 1px solid #D4D4D4; */
	display: inline-block;
	background: #efefef;
	padding-bottom: 20px;
	border: 1px solid #efefef;
}
.box-gray  {
	background: #f8f8f8;
	padding: 20px 20px 30px;
}
.box-gray  h4,.box-gray  i {
	margin-bottom: 20px;
}
.box-bottom {
	padding: 20px 0;
	text-align: center;
}
.box-bottom a {
	color: #fff;
	font-weight: 700;
}
.box-bottom a:hover {
	color: #eee;
	text-decoration: none;
}


/* Bottom
==================================== */

#bottom {
	background:#fcfcfc;
	padding:50px 0 0;

}
/* twitter */
#twitter-wrapper {
    text-align: center;
    width: 70%;
    margin: 0 auto;
}
#twitter em {
    font-style: normal;
    font-size: 13px;
}

#twitter em.twitterTime a {
	font-weight:600;
}

#twitter ul {
    padding: 0;
	list-style:none;
}
#twitter ul li {
    font-size: 20px;
    line-height: 1.6em;
    font-weight: 300;
    margin-bottom: 20px;
    position: relative;
    word-break: break-word;
}

.features .features-item {
  padding: 0 0 70px 0;
}
.features .features-item .features {
  margin-bottom: 34px;
}
.features .features-item .features .icon {
  float: left;
}
.features .features-item .features .icon i {
  z-index: 99;
  font-size: 26px;
  margin: 2px 8px 0 0;
  color: #ffc02a;
  background: #055999;
  padding: 16px;
  height: 58px;
  display: inline-block;
  border: 1px solid #055999;
}
.features .features-item .features-content {
  margin-left: 80px;
  padding-right: 68px;
}
.features .features-item .features-content h3 {
  padding-bottom: 8px;
  text-transform: uppercase;
  margin: 0;
  font-size: 18px;
}
.features .features-item .features:hover .icon-radius:after {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}
.features img{

margin: 10px 0 0 0;
}
/* page headline
==================================== */

#inner-headline {
    background: #2056ae;
    position: relative;
    margin: 0;
    padding: 0;
}

#inner-headline:before {
    content:'';
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

#inner-headline h2.pageTitle{
	color: #ffffff;
	padding: 5px 0;
	text-align: left;
	display:block;
	font-size: 34px;
	font-weight: 700;
	position: relative;
	z-index: 3;
}

/* --- breadcrumbs --- */
#inner-headline ul.breadcrumb {
	margin:40px 0;
	float:left;
}

#inner-headline ul.breadcrumb li {
	margin-bottom:0;
	padding-bottom:0;
}
#inner-headline ul.breadcrumb li {
	font-size:13px;
	color:#fff;
}

#inner-headline ul.breadcrumb li i{
	color:#dedede;
}

#inner-headline ul.breadcrumb li a {
	color:#fff;
}

ul.breadcrumb li a:hover {
	text-decoration:none;
}
.fancybox-title-inside-wrap {
    padding: 3px 30px 6px;
    background: #2F2F2F;
    text-align: center; 
}
.fancybox-title-inside-wrap h4{
    font-size: 18px;	
}
.fancybox-nav span { 
    background-color: transparent;
}
/* Forms
============================= */

/* --- contact form  ---- */
form#contactform input[type="text"] {
  width: 100%;
  border: 1px solid #f5f5f5;
  min-height: 40px;
  padding-left:20px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;

}

form#contactform textarea {
border: 1px solid #f5f5f5;
  width: 100%;
  padding-left:20px;
  padding-top:10px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;

}

form#contactform .validation {
	font-size:11px;
}

#sendmessage {
	border:1px solid #e6e6e6;
	background:#f6f6f6;
	display:none;
	text-align:center;
	padding:15px 12px 15px 65px;
	margin:10px 0;
	font-weight:600;
	margin-bottom:30px;

}

#sendmessage.show,.show  {
	display:block;
}
 
form#commentform input[type="text"] {
  width: 100%;
  min-height: 40px;
  padding-left:20px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
	-webkit-border-radius: 2px 2px 2px 2px;
		-moz-border-radius: 2px 2px 2px 2px;
			border-radius: 2px 2px 2px 2px;

}

form#commentform textarea {
  width: 100%;
  padding-left:20px;
  padding-top:10px;
  font-size:13px;
  padding-right:20px;
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
	-webkit-border-radius: 2px 2px 2px 2px;
		-moz-border-radius: 2px 2px 2px 2px;
			border-radius: 2px 2px 2px 2px;
}


/* --- search form --- */
.search{
	float:right;
	margin:35px 0 0;
	padding-bottom:0;
}

#inner-headline form.input-append {
	margin:0;
	padding:0;
}

/* --- portfolio detail --- */
.top-wrapper {
	margin-bottom:20px;
}
.info-blocks {
margin-bottom: 15px;
}
.info-blocks i.icon-info-blocks {float: left;color: #055999;font-size: 30px;min-width: 50px;margin-top: 6px;text-align: center;background: #FFFFFF;height: 64px;padding: 18px;border: 1px solid #055999;}
.info-blocks .info-blocks-in {
padding: 0 10px;
overflow: hidden;
}
.info-blocks .info-blocks-in h3 {
color: #555;
font-size: 20px;
line-height: 28px;
margin:0px;
}
.info-blocks .info-blocks-in p {
font-size: 14px;
}
  
blockquote {
	font-size:16px;
	font-weight:400;
	font-family:'Noto Serif', serif;
	font-style:italic;
	padding-left:0;
	color:#a2a2a2;
	line-height:1.6em;
	border:none;
}

blockquote cite 							{ display:block; font-size:12px; color:#666; margin-top:10px; }
blockquote cite:before 					{ content:"\2014 \0020"; }
blockquote cite a,
blockquote cite a:visited,
blockquote cite a:visited 				{ color:#555; }

/* --- pullquotes --- */

.pullquote-left {
	display:block;
	color:#a2a2a2;
	font-family:'Noto Serif', serif;
	font-size:14px;
	line-height:1.6em;
	padding-left:20px;
}

.pullquote-right {
	display:block;
	color:#a2a2a2;
	font-family:'Noto Serif', serif;
	font-size:14px;
	line-height:1.6em;
	padding-right:20px;
}

/* --- button --- */
.btn{text-align: center;background: #055999;color: #fff;border-radius: 0;border: none;padding: 8px 15px;border: 1px solid #055999;}
.btn-theme {
	color: #fff;
	background: transparent;
	border: 1px solid #FFFFFF;
	padding: 12px 30px;
	font-weight: bold;
	border-radius: 10px;
}
.btn-theme:hover {
	color: #eee;
}

/* --- list style --- */

ul.general {
	list-style:none;
	margin-left:0;
}

ul.link-list{
	margin:0;
	padding:0;
	list-style:none;
}

ul.link-list li{
	margin:0;
	padding:2px 0 2px 0;
	list-style:none;
}

/* 页脚相关样式已移动到footer.css文件 */

/* --- Heading style --- */

h4.heading {
	font-weight:700;
}

.heading { margin-bottom: 30px; }

.heading {
	position: relative;
}

.widgetheading {
	width:100%;
	padding:0;
}

#bottom .widgetheading {
	position: relative;
	border-bottom: #e6e6e6 1px solid;
	padding-bottom: 9px;
}

aside .widgetheading {
	position: relative;
	border-bottom: #e9e9e9 1px solid;
	padding-bottom: 9px;
}

/* 页脚相关样式已移动到footer.css文件 */

#bottom .widget .widgetheading span, aside .widget .widgetheading span {	
	position: absolute;
	width: 60px;
	height: 1px;
	bottom: -1px;
	right:0;
}
.box-area{padding: 0 0;/* padding-top: 0; *//* height: 125px; */float: left;text-align: center;padding: 0px 20px;width: 100%;}
/* --- Map --- */
.map{
	position:relative;
	margin-top:-50px;
	margin-bottom:40px;
}

.map iframe{
	width:100%;
	height:450px;
	border:none;
}

.map-grid iframe{
	width:100%;
	height:350px;
	border:none;
	margin:0 0 -5px 0;
	padding:0;
}

 
ul.team-detail{
	margin:-10px 0 0 0;
	padding:0;
	list-style:none;
}

ul.team-detail li{
	border-bottom:1px dotted #e9e9e9;
	margin:0 0 15px 0;
	padding:0 0 15px 0;
	list-style:none;
}

ul.team-detail li label {
	font-size:13px;
}

ul.team-detail li h4, ul.team-detail li label{
	margin-bottom:0;
}

ul.team-detail li ul.social-network {
	border:none;
	margin:0;
	padding:0;
}

ul.team-detail li ul.social-network li {
	border:none;	
	margin:0;
}
ul.team-detail li ul.social-network li i {
	margin:0;
}

 
.pricing-title{
	background:#fff;
	text-align:center;
	padding:10px 0 10px 0;
}

.pricing-title h3{
	font-weight:600;
	margin-bottom:0;
}

.pricing-offer{
	background: #fcfcfc;
    text-align: center;
	padding:40px 0 40px 0;
	font-size:18px;
	border-top:1px solid #e6e6e6;
	border-bottom:1px solid #e6e6e6;
}

.pricing-box.activeItem .pricing-offer{
	color:#fff;
}

.pricing-offer strong{
	font-size:78px;
	line-height:89px;
}

.pricing-offer sup{
	font-size:28px;
}

.pricing-container{
	background: #fff;
	text-align:center;
	font-size:14px;
}

.pricing-container strong{
color:#353535;
}

.pricing-container ul{
	list-style:none;
	padding:0;
	margin:0;
}

.pricing-container ul li{
	border-bottom: 1px solid #E6E6E6;
	list-style: none;
	padding: 15px 0 15px 0;
	margin: 0 0 0 0;
	color: #222;
}

.pricing-action{
	margin:0;
	background: #fcfcfc;
	text-align:center;
	padding:20px 0 30px 0;
}

.pricing-wrapp{
	margin:0 auto;
	width:100%;
	background:#fd0000;
}
 .pricing-box-item {
border: 1px solid #e6e6e6;
	
	position:relative;
	margin:0 0 20px 0;
	padding:0;
  -webkit-box-shadow: 0 2px 0 rgba(0,0,0,0.03);
  -moz-box-shadow: 0 2px 0 rgba(0,0,0,0.03);
  box-shadow: 0 2px 0 rgba(0,0,0,0.03);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.pricing-box-item .pricing-heading {
	text-align: center;
	padding:0px 0 0px 0;
	display:block;
}
.pricing-box-item.activeItem .pricing-heading {
	text-align: center;
	padding:0px 0 1px 0;
	border-bottom:none;
	display:block;
	color:#fff;
}
.pricing-box-item.activeItem .pricing-heading h3 {
    color: #fff;
    font-weight: 600;
}

.pricing-box-item .pricing-heading h3 strong {
	font-size: 24px;
	font-weight: bold;
	letter-spacing:-1px;
	color: #055999;
}
.pricing-box-item .pricing-heading h3 {
	font-size:32px;
	font-weight:300;
	letter-spacing:-1px;
	margin-bottom: 0;
}

.pricing-box-item .pricing-terms {
	text-align: center;
    display: block;
	overflow: hidden;
	padding: 11px 0 5px; 
}

.pricing-box-item .pricing-terms  h6 {
	margin-top: 20px;
	color: #000;
	font-size: 22px;
	font-weight: normal;
	background: #ffffff;
	padding: 20px 0;
}

.pricing-box-item .icon .price-circled {
    margin: 10px 10px 10px 0;
    display: inline-block !important;
    text-align: center !important;
    color: #fff;
    width: 68px;
    height: 68px;
	padding:12px;
    font-size: 16px;
	font-weight:700;
    line-height: 68px;
    text-shadow:none;
    cursor: pointer;
    background-color: #888;
    border-radius: 64px;
    -moz-border-radius: 64px;
    -webkit-border-radius: 64px;
}

.pricing-box-item  .pricing-action{
	margin:0;
	text-align:center;
	padding: 30px 15px;
}
 .pricing-action a:hover {
    color: #fff;
}
/* ===== Widgets ===== */

/* --- flickr --- */
.widget .flickr_badge {
	width:100%;
}
.widget .flickr_badge img { margin: 0 9px 20px 0; }

footer .widget .flickr_badge {
    width: 100%;
}
footer .widget .flickr_badge img {
    margin: 0 9px 20px 0;
}

.flickr_badge img {
    width: 50px;
    height: 50px;
    float: left;
	margin: 0 9px 20px 0;
}
 
/* --- Recent post widget --- */

.recent-post{
	margin:20px 0 0 0;
	padding:0;
	line-height:18px;
}

.recent-post h5 a:hover {
	text-decoration:none;
}

.recent-post .text h5 a {
	color:#353535;
}

/* 页脚相关样式已移动到footer.css文件 */

/* scroll to top */
.scrollup{
    position:fixed;
    width:32px;
    height:32px;
    bottom:0px;
    right:20px;
    background: #055999;
    z-index: 9999;
}

a.scrollup {
	outline:0;
	text-align: center;
}

a.scrollup:hover,a.scrollup:active,a.scrollup:focus {
	opacity:1;
	text-decoration:none;
}
a.scrollup i {
	margin-top: 10px;
	color: #fff;
}
a.scrollup i:hover {
	text-decoration:none;
}



 
.absolute{
	position:absolute;
}

.relative{
	position:relative;
}

.aligncenter{
	text-align:center;
}

.aligncenter span{
	margin-left:0;
}

.floatright {
	float:right;
}

.floatleft {
	float:left;
}

.floatnone {
	float:none;
}

.aligncenter {
	text-align:center;
}
 
img.pull-left, .align-left{
	float:left;
	margin:0 15px 15px 0;
}

.widget img.pull-left {
	float:left;
	margin:0 15px 15px 0;
}

img.pull-right, .align-right {
	float:right;
	margin:0 0 15px 15px;
}

article img.pull-left, article .align-left{
	float:left;
	margin:5px 15px 15px 0;
}

article img.pull-right, article .align-right{
	float:right;
	margin:5px 0 15px 15px;
}
/* 间距设置 */

.clear-marginbot{
	margin-bottom:0;
}

.marginbot10{
	margin-bottom:10px;
}
.marginbot20{
	margin-bottom:20px;
}
.marginbot30{
	margin-bottom:30px;
}
.marginbot40{
	margin-bottom:40px;
}

.clear-margintop{
	margin-top:0;
}

.margintop10{
	margin-top:10px;
}

.margintop20{
	margin-top:20px;
}

.margintop30{
	margin-top:30px;
}

.margintop40{
	margin-top:40px;
}
header .nav .caret {
    border-bottom-color: #f5f5f5;
    border-top-color: #adadad;
}

/*  Media queries 
============================= */

@media (min-width: 768px) and (max-width: 979px) {
	a.detail{
		background:none;
		width:100%;
	}
	
	ul.related-folio li{
		width:156px;
		margin:0 20px 0 0;
	}	
}

@media (max-width: 767px) {
	.box {
		border-bottom:1px solid #e9e9e9;
		padding-bottom:20px;
	}

	.flexslider .slide-caption {
		width: 90%; 
		padding: 2%; 
		position: absolute; 
		left: 0; 
		bottom: -40px; 
	}

	#inner-headline .breadcrumb {
		float:left;
		clear:both;
		width:100%;
	}

	.breadcrumb > li {
		font-size:13px;
	}

	ul.portfolio li article a i.icon-48{
		width:20px;
		height:20px;
		font-size:16px;
		line-height:20px;
	}

	.left-sidebar{
		border-right:none;
		padding:0 0 0 0;
		border-bottom: 1px dotted #e6e6e6;
		padding-bottom:10px;
		margin-bottom:40px;
	}
	
	.right-sidebar{
		margin-top:30px;
		border-left:none;
		padding:0 0 0 0;
	}

	[class*="span"] {
		margin-bottom:20px;
	}
}

@media (min-width:768px){
	.item-thumbs {
		position: relative;
		overflow: hidden;
		margin-bottom: 30px;
		cursor: pointer;
		width: 48%;
		float: left;
		margin: 1%;
	}
}

@media (max-width: 480px) {
	.bottom-article a.pull-right {
		float:left;
		margin-top:20px;
	}

	.search{
		float:left;
	}

	.flexslider .flex-caption {
		display:none;
	}

	.cta-text {
		margin:0 auto;
		text-align:center;	
	}
	
	ul.portfolio li article a i{
		width:20px;
		height:20px;
		font-size:14px;
	}
}

#product {
	background: url(../images/banner_products.jpg) 50% bottom no-repeat ;
	height: 700px;
}
 .hd_img{
	 height: 720px;
	 width: 100%;
    text-align: center;
}

 .content_block{
	 width: 70%;
	 height: 100%;
	 margin-left: 14%;
	 margin-bottom: 2%;
     /*background-color: #843534;*/
 }

 .new_conten{
	 float: left;
	 width: 60%;
	 height: 250px;
	 margin-left: 60px;
     /*background-color: #2056ae;*/
	 /*border-bottom: 1px solid  #c0c0c0;*/
 }

 .hot_new_conten{
	 /*background-color: #A5A5A5;*/
	 float: left;
	 width: 20%;
	 height:350px;
	 margin-left: 6%;
	 background-color: rgba(0,0,0,0.05);
 }

 .new{
	 float: left;
	 width: 100%;
	 height: 240px;
	 /*background-color: #61B331;*/
	 border-bottom: 1px solid  #c0c0c0;
 }
 .new_img1{
	 float: left;
	 width: 240px;
	 height: 240px;
	 vertical-align: middle;
	 display: table-cell;
    text-align: center;
 }
 .new_img2{
	 width: 240px;
	 height: 240px;
	 vertical-align: middle;
	 display: table-cell;
	 text-align: center;
 }
 .new_img1 img{
	 width: 240px;
	 height: 240px

 }
 .text_content{
	 width: 60%;
	 float: left;
	 margin-top: 2%;
	 margin-left: 2%;
 }
 .text_title{
	 font-size: 18px;
	 color: #2056ae;
	 font-weight:bold;
	 text-decoration:none;
	 cursor: pointer;
 }
 a:hover {text-decoration: none;}
.new_time{
	 margin-top: 40px;
 }

.hot_new{
	height: 40px;
	font-size: 16px;
	font-weight: bold;
	/*background-color: #61B331;*/
	/*display:table-cell;*/
}
.h_conten_title{
	width: 90%;
	height: 40px;
	vertical-align: middle;
	border-bottom: 1px solid  #c0c0c0;
	margin-left: 2%;
	line-height: 30px;
	/*background-color: #01B2D1;*/
}
.h_conten{
	width: 90%;
	height: 90px;
	vertical-align: middle;
	border-bottom: 1px solid  #c0c0c0;
	margin-top: 1%;
	margin-left: 2%;
	line-height: 30px;
	/*background-color: #01B2D1;*/
}

.new_details{
    display: none;
	float: left;
	width: 60%;
	height: 350px;
	margin-left: 60px;
	/*background-color: #61B331;*/
	/*border-bottom: 1px solid  #c0c0c0;*/
}
.left_link{
	font-size: 12px;
	color: #2056ae;
	font-weight:bold;
	text-decoration:none;
	cursor: pointer;
}

.details{
    height: auto;
	/*background-color: #8a6d3b;*/
}

.details_title {
	font-size: 18px;
	font-weight: bold;
}

.details_img{
	width: 200px;
	height: 200px;
}
.header_carousel{
    width: 100%;
    text-align: center;
    height: 714px
}


@media only screen and (max-width: 768px){
    .content_block{
        width: 100%;
        height: 100%;

        margin-left: 0;

    }
    .new_conten{
        float: left;
        width: 100%;
        height: 80px;
        margin-left: 0;
        margin-top: -5%;
        /*background-color: #61B331;*/
    }
    .new{
        float: left;
        width: 100%;
        height: 130px;
        margin-left: 2%;
        /*background-color: #843534;*/
        border-bottom: 1px solid  #c0c0c0;
    }

    .new_img1{
        float: left;
        width: 100px;
        height: 100px;
        vertical-align: middle;
        display: table-cell;
    text-align: center;
        /*background-color: #2056ae;*/
    }
    .new_img2{
        width: 100px;
        height: 100px;
        vertical-align: middle;
        display: table-cell;
        text-align: center;
    }
    .text_content{
        width: 70%;
        float: left;
        /*background-color: #01B2D1;*/
    }

    .text_title{
        font-size: 8px;
        color: #2056ae;
        font-weight:bold;
        text-decoration:none;
        cursor: pointer;
    }
    .text_style{
        font-size: 2px;
        /*overflow: hidden;*/
        /*text-overflow: ellipsis;*/
        /*white-space: nowrap;*/
        /*width: 62%;*/
    }
    .new_time{
        margin-top: 10px;
    }

    .hot_new_conten{
        position: absolute;
    bottom: 0;
        /*float: right;*/
        /*background-color: #A5A5A5;*/
        /*float: left;*/
        width: 375px;
        /*height:350px;*/
        margin-left: 0;
        /*margin-bottom: 2px;*/
        background-color: rgba(0,0,0,0.05);
    }
    .header_carousel{
    width: 100%;
    text-align: center;
        height: 350px;
    }

    .new_details{
        display: none;
        float: left;
        width: 100%;
        height: 350px;
        margin-left:0;
        /*background-color: #61B331;*/
        /*border-bottom: 1px solid  #c0c0c0;*/
    }
    .details{
        height: 350px;
        margin-left: 10px;
        /*background-color: #8a6d3b;*/
    }
    .details_title {
        font-size: 16px;
        font-weight: bold;
    }

}
#home_testimonial-section {
    background: #F8F8F8;
    padding: 40px 0;
    min-height: 700px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 以下是新的轮播图样式，已经包含了旧样式的精华部分 */
.carousel-bg {
    width: 100%;
    height: 657px; /* 原来是41.67vw，现在改为固定像素值 */
    background: #F8F8F8;
    border-radius: 0;
    margin: 0 auto;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    position: relative;
    padding-top: 66px; /* 60/1920 * 100 = 3.13vw */
    overflow: hidden;
}

.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: visible;
}

.carousel-item {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: none;
}

.carousel-item.active {
    opacity: 1;
    display: block;
}

/* 确保轮播内容在缩放时居中 */
.carousel-content {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    max-width: 83.33vw; /* 1600/1920 * 100 = 83.33vw */
    margin: 0 auto;
    gap: 0;
}

.carousel-indicators {
    position: absolute;
    bottom: 39px; /* 30/1920 * 100 = 1.56vw */
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 16px; /* 12/1920 * 100 = 0.63vw */
    padding: 0;
    margin: 0 auto;
    z-index: 10;
}

.carousel-indicators .indicator {
    width: 16px; /* 固定16px宽度 */
    height: 16px; /* 固定16px高度 */
    background: #CCCCCC;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicators .indicator.active {
    background: #00509F;
}

.carousel-card-left {
    flex: 0 0 auto;
    width: 740px; /* 固定宽度740px，不再使用相对单位 */
    margin-left: 256px; /* 距离左侧256px */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 0;
    padding-right: 0px;
    margin-right:62px;
    height:auto;
    position: relative; /* 添加相对定位，作为按钮组的定位参考 */
}

.carousel-title-box {
    margin-bottom: 0; /* 40/1920 * 100 = 2.08vw */
    width: 100%; /* 400/1920 * 100 = 20.83vw */
}

.carousel-title-main {
    font-family: 'Source Han Sans CN', 'Source Han Sans CN';
    font-weight: bold;
    font-size: 50px;
    color: #0050A2;
    line-height: 80px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
    white-space: nowrap; /* 确保一行展示 */
}

html[lang="en"] .carousel-title-main {
    font-size: 46px;
    word-spacing: -2px;       /* 减小单词间距 */
    letter-spacing: -2.5px;   /* 减小字母间距 */
}

.carousel-feature-list {
    list-style: none;
    padding: 0 0 0 0;                /* 移除内边距 */
    margin: -15px 0 0 0;                 /* 移除外边距 */
    display: flex;             /* 使用flex布局 */
    flex-direction: column;    /* 垂直方向排列 */
    justify-content: center;   /* 垂直居中 */
    height: 361px;            /* 固定高度 */
    position: relative;        /* 相对定位 */
}

.carousel-feature-list li {
    position: relative;
    padding-left: 25px;
    font-family: 'Source Han Sans CN', 'Source Han Sans CN';
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    line-height: 44px;
    letter-spacing: 0;
    text-align: left;
    font-style: normal;
    text-transform: none;
    white-space: normal;      /* 允许文本换行 */
    overflow: visible;        /* 内容溢出时显示 */
    margin:  0 0 0;               /* 移除外边距 */
    flex: 0 0 auto;          /* 防止被拉伸或压缩 */
    display: flex;           /* 使用flex布局 */
    align-items: center;     /* 垂直居中对齐 */
}

.carousel-feature-list li:before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    background: #00509F;
    border-radius: 2px 2px 2px 2px;
    position: absolute;
    left: 4px;
    top: 16px;
}

.carousel-btn-group {
    display: flex;
    gap: 40px;
    position: absolute; /* 使用绝对定位 */
    bottom: 93px; /* 距离底部93px */
    left: 260px; 
    z-index: 5; /* 确保在其他内容之上 */
}

.carousel-btn-main {
    width: 200px;
    height: 64px;
    background: #0050A2;
    border-radius: 12px;
    font-family: 'Source Han Sans CN', 'Source Han Sans CN';
    font-weight: 400;
    font-size: 24px;
    color: #FFFFFF;
    line-height: 53px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    text-decoration: none !important;
    border: none;
    display: flex; /* 使用flex布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    transition: background 0.3s ease;
}


.carousel-btn-main:hover {
    background: #007DDB;
    color: #FFFFFF !important;
}

.carousel-btn-sub {
    width: 200px;
    height: 64px;
    background: #0050A2;
    border-radius: 12px;
    font-family: 'Source Han Sans CN', 'Source Han Sans CN';
    font-weight: 400;
    font-size: 24px;
    color: #FFFFFF;
    line-height: 53px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    text-decoration: none !important;
    border: none;
    display: flex; /* 使用flex布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    transition: background 0.3s ease;
}

.carousel-btn-sub:hover {
    background: #007DDB;
    color: #FFFFFF !important;
}

.carousel-card-right {
    flex: 0 0 auto;
    width: 582px; /* 固定宽度582px */
    height: 484px; /* 固定高度484px */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0px; /* 与左侧卡片间距82px */
    margin-right:260px;
    margin-top: 14px; 
}

.carousel-product-img {
    width: 100%;
    height: 100%;
    border-radius: 1.88vw; /* 36/1920 * 100 = 1.88vw */
    object-fit: cover;
}


.pdesc{
	margin-top: 10px;
	height: 70px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	display: box;
	line-clamp: 3;
	box-orient: vertical;
}

.index_desc{
	height: 65px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	display: box;
	line-clamp: 3;
	box-orient: vertical;
}

.img_magrin_index1{
	margin-right: 15px;
}

.product_detail_cls{
	overflow-y: hidden;
	overflow-x: hidden;
	padding-top: 0px;
	min-height:62.5%;
}

.copyright p{
	margin-bottom: 1px;
}

/* 轮播箭头按钮 */
.carousel-arrow-container {
    position: absolute;
    cursor: pointer;
    width: 64px; /* 修改为固定宽度64px */
    height: 100px; /* 修改为固定高度100px */
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFFFFF; /* 添加白色背景 */
    border-radius: 8px; /* 添加圆角 */
    margin-top: 206px;
    margin-bottom:285px;
}

.carousel-arrow-left {
    left: 56px; /* 55/1920 * 100 */
}

.carousel-arrow-right {
    right: 56px; /* 55/1920 * 100 */
}

.carousel-arrow-btn {
    width: 100%;
    height: 100%;
    background: url('../images/home/<USER>') center center no-repeat;
    background-size: contain;
    transition: all 0.3s ease;
}

.carousel-arrow-container:hover .carousel-arrow-btn {
    background-image: url('../images/home/<USER>');
}

.carousel-arrow-right .carousel-arrow-btn {
    background-image: url('../images/home/<USER>');
    transform: rotate(180deg);
}

.carousel-arrow-right:hover .carousel-arrow-btn {
    background-image: url('../images/home/<USER>');

}

/* 移动端适配 */
@media screen and (max-width: 768px) {
    .carousel-arrow-container {
        width: 8.33vw;
        height: 13.89vw;
    }
}

/* 移动端轮播图按钮位置调整 */
@media screen and (max-width: 492px) {
    .carousel-feature-list {
        margin-bottom: 1vw;
    }
    
    .carousel-btn-group {
        margin-top: 5vw;
		margin-left: -10vw; /* 向左移动一点 */
        gap: 3vw; /* 增大按钮间距 */
    }

}

/* 工具栏样式 */
.floating-toolbar {
    position: absolute;
    right: 1.04vw; /* 20/1920 * 100 = 1.04vw */
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    width: 6.2vw; /* 119/1920 * 100 = 6.2vw */
    height: auto;
    filter: drop-shadow(0 0.26vw 0.52vw rgba(0, 0, 0, 0.1));
    background: transparent; /* 添加透明背景 */
    opacity: 0; /* 默认隐藏 */
    visibility: hidden; /* 默认隐藏 */
    transition: opacity 0.3s ease, visibility 0.3s ease; /* 添加过渡效果 */
}

/* 工具栏固定状态 */
.floating-toolbar.fixed {
    position: fixed;
    top: 50%;
    background: transparent; /* 添加透明背景 */
    opacity: 1; /* 显示工具栏 */
    visibility: visible; /* 显示工具栏 */
}

/* 二维码弹窗样式 */
.qrcode-popup {
    position: fixed;
    right: 7.8125vw; /* 150px / 1920px * 100 = 7.8125vw */
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    z-index: 9999;
    display: none;
    width: auto;
    text-align: center;
}

.qrcode-popup.show {
    display: block;
}

.qrcode-popup .qrcode-container {
    display: flex;
    flex-direction: column;
}

.qrcode-popup .qrcode-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.qrcode-popup .qrcode-image {
    width: 5.4167vw; /* 104px / 1920px * 100 = 5.4167vw */
    height: 5.4167vw; /* 104px / 1920px * 100 = 5.4167vw */
    border: none;
    border-radius: 0;
    background-color: #fff;
}

.qrcode-popup .qrcode-label {
    display: none;
}

.qrcode-popup .close-btn {
    display: none;
}

/* 电话咨询弹窗样式 */
.phone-popup {
    position: fixed;
    right: 7.8125vw; /* 150px / 1920px * 100 = 7.8125vw */
    top: 50%;
    transform: translateY(-50%);
    background-color: #fff;
    border-radius: 0;
    box-shadow: 0 0 0.5208vw rgba(0, 0, 0, 0.1); /* 10px / 1920px * 100 = 0.5208vw */
    padding: 0.7813vw; /* 15px / 1920px * 100 = 0.7813vw */
    z-index: 9999;
    display: none;
    width: 8.75vw; /* 168px / 1920px * 100 = 8.75vw */
    height: 12.2917vw; /* 236px / 1920px * 100 = 12.2917vw */
    text-align: left;
    box-sizing: border-box;
}

.phone-popup.show {
    display: block;
}

.phone-popup .phone-title {
    font-size: 0.7292vw; /* 14px / 1920px * 100 = 0.7292vw */
    font-weight: bold;
    margin-bottom: 0.5208vw; /* 10px / 1920px * 100 = 0.5208vw */
    color: #333;
    position: relative;
}

.phone-popup .phone-container {
    display: flex;
    flex-direction: column;
    gap: 0.2604vw; /* 5px / 1920px * 100 = 0.2604vw */
}

.phone-popup .phone-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 0.5208vw; /* 10px / 1920px * 100 = 0.5208vw */
}

.phone-popup .work-time {
    font-size: 0.625vw; /* 12px / 1920px * 100 = 0.625vw */
    color: #666;
    margin-top: 0.5208vw; /* 10px / 1920px * 100 = 0.5208vw */
    padding-top: 0.5208vw; /* 10px / 1920px * 100 = 0.5208vw */
    border-top: 1px solid #eee;
}

.phone-popup .work-time p {
    margin: 0.1563vw 0; /* 3px / 1920px * 100 = 0.1563vw */
}

.phone-popup .phone-number {
    font-size: 0.7292vw; /* 14px / 1920px * 100 = 0.7292vw */
    font-weight: bold;
    color: #333;
    margin-bottom: 0.1563vw; /* 3px / 1920px * 100 = 0.1563vw */
}

.phone-popup .phone-label {
    font-size: 0.625vw; /* 12px / 1920px * 100 = 0.625vw */
    color: #666;
}

.phone-popup .qrcode-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5208vw 0; /* 10px / 1920px * 100 = 0.5208vw */
}

.phone-popup .qrcode-wrapper img {
    width: 4.8438vw; /* 93px / 1920px * 100 = 4.8438vw */
    height: 4.8438vw; /* 93px / 1920px * 100 = 4.8438vw */
    border: 1px solid #eee;
}

.phone-popup .qrcode-wrapper img:hover {
    border-color: #ddd;
}

/* 针对小屏幕设备的弹窗样式调整 */
@media screen and (max-width: 491px) {
    /* 调整工具栏位置和尺寸 */
    .floating-toolbar {
        width: 15vw; /* 增加相对宽度 */
        right: 0.5vw; /* 靠右侧更近一些 */
    }
    
    /* 企业微信弹窗调整 - 减小与工具栏的间距 */
    .qrcode-popup {
        right: 16vw; /* 距离右侧更近，紧贴工具栏 */
    }
    
    .qrcode-popup .qrcode-container {
        flex-direction: column;
        gap: 0; /* 将间距改为0 */
    }
    
    .qrcode-popup .qrcode-image {
        width: 20vw; /* 相对屏幕宽度的尺寸 */
        height: 20vw;
        max-width: 80px;
        max-height: 80px;
    }
    
    /* 电话咨询弹窗调整 - 隐藏原内容，为图片预留位置 */
    .phone-popup {
        right: 16vw; /* 距离右侧更近，紧贴工具栏 */
        width: 40vw; /* 为图片预留宽度 */
        max-width: 160px;
        height: auto;
        background: transparent;
        box-shadow: none;
        padding: 0;
    }
    
    /* 隐藏原有内容 */
    .phone-popup .phone-title,
    .phone-popup .work-time,
    .phone-popup .qrcode-wrapper {
        display: none;
    }
    
    /* 为导入图片预留位置 */
    .phone-popup:after {
        content: "";
        display: block;
        width: 100%;
        height: 0;
        padding-bottom: 133%; /* 4:3比例 */
        background-image: url('../images/phone-contact.png'); /* 需要准备一张联系信息图片 */
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
}

/* 工作时间和服务热线间距调整 */
@media screen and (max-width: 1835px) and (min-width: 492px) {
    .phone-popup .work-time p {
        margin: 0.1vw 0; /* 减小段落间距 */
        line-height: 1.2; /* 减小行高 */
    }
    
    .phone-popup .work-time {
        margin-top: 0.3vw; /* 减小顶部边距 */
        padding-top: 0.3vw; /* 减小顶部内边距 */
    }
}


/* 英文界面移动端特殊样式 (≤ 491px) */
@media screen and (max-width: 491px) {
    html[lang="en"] .carousel-feature-list li {
        font-size: 8px !important;
        transform: scale(0.65);
        transform-origin: left center;
        white-space: nowrap;
        overflow: visible;
        width: 180%;
        height: auto;
        line-height: 8px;
        margin-bottom: 5px;
    }

    html[lang="en"] .carousel-btn-main,
    html[lang="en"] .carousel-btn-sub {
        font-size: 8px;
        width: auto;
        padding: 0 10px;
        height: 24px;
        line-height: 24px;
        white-space: nowrap;
    }

    html[lang="en"] .carousel-btn-group {
        margin-left: -5vw;
        justify-content: flex-start;
        gap: 2px;
        margin-top: -1px;
    }
    
    html[lang="en"] .carousel-card-right {
        margin-left: 0; /* 将间距从2.6vw减小到0.5vw */
    }
}



