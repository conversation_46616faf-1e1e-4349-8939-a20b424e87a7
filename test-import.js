const mysql = require('mysql2');

// 数据库连接配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'wechat_articles_db',
    charset: 'utf8mb4'
};

// 创建数据库连接
const db = mysql.createConnection(dbConfig);

// 连接数据库
db.connect((err) => {
    if (err) {
        console.error('❌ 数据库连接失败:', err);
        return;
    }
    console.log('✅ 数据库连接成功');
    
    // 测试清空数据库
    testClearDatabase();
});

function testClearDatabase() {
    console.log('🗑️  测试清空数据库...');
    
    const clearTableQuery = 'DELETE FROM wechat_articles';
    
    db.query(clearTableQuery, (err, result) => {
        if (err && err.code !== 'ER_NO_SUCH_TABLE') {
            console.error('❌ 清空数据库失败:', err);
            db.end();
            return;
        }
        
        if (err && err.code === 'ER_NO_SUCH_TABLE') {
            console.log('ℹ️  数据库表不存在');
        } else {
            console.log('✅ 数据库内容已清空，删除了', result.affectedRows, '条记录');
        }
        
        // 测试创建表
        testCreateTable();
    });
}

function testCreateTable() {
    console.log('📋 测试创建数据库表...');
    
    const createArticlesTable = `
        CREATE TABLE IF NOT EXISTS wechat_articles (
            id INT PRIMARY KEY AUTO_INCREMENT,
            article_id VARCHAR(255) NOT NULL,
            title VARCHAR(500),
            digest TEXT,
            content_text LONGTEXT,
            image_urls JSON,
            section_order INT DEFAULT 0 COMMENT '内容块顺序',
            parent_article_id VARCHAR(255) COMMENT '原始文章ID',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_article_id (article_id),
            INDEX idx_parent_article_id (parent_article_id),
            INDEX idx_section_order (section_order),
            INDEX idx_title (title(100)),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    db.query(createArticlesTable, (err) => {
        if (err) {
            console.error('❌ 创建数据库表失败:', err);
            db.end();
            return;
        }
        console.log('✅ 数据库表创建完成');
        
        // 测试插入一条记录
        testInsertRecord();
    });
}

function testInsertRecord() {
    console.log('📝 测试插入记录...');
    
    const insertQuery = `
        INSERT INTO wechat_articles (
            article_id, title, digest, content_text, image_urls, 
            section_order, parent_article_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const testData = [
        'test_001_1',
        '测试标题',
        '测试摘要',
        '这是测试内容文字',
        JSON.stringify(['https://example.com/image1.jpg']),
        0,
        'test_001'
    ];
    
    db.query(insertQuery, testData, (err, result) => {
        if (err) {
            console.error('❌ 插入记录失败:', err);
            db.end();
            return;
        }
        
        console.log('✅ 测试记录插入成功，ID:', result.insertId);
        
        // 查询验证
        testQuery();
    });
}

function testQuery() {
    console.log('🔍 测试查询记录...');
    
    const selectQuery = 'SELECT * FROM wechat_articles ORDER BY section_order';
    
    db.query(selectQuery, (err, results) => {
        if (err) {
            console.error('❌ 查询失败:', err);
            db.end();
            return;
        }
        
        console.log('✅ 查询成功，找到', results.length, '条记录:');
        results.forEach((row, index) => {
            console.log(`  ${index + 1}. ID: ${row.id}, Article ID: ${row.article_id}, Title: ${row.title}`);
            console.log(`     Section Order: ${row.section_order}, Parent: ${row.parent_article_id}`);
            console.log(`     Images: ${row.image_urls}`);
        });
        
        console.log('🎉 所有测试完成！');
        db.end();
    });
}
