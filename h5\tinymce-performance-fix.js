/**
 * TinyMCE 性能优化脚本
 * 解决触摸事件性能警告和其他性能问题
 */

(function() {
    'use strict';
    
    // 检测是否支持被动事件监听器
    let supportsPassive = false;
    try {
        const opts = Object.defineProperty({}, 'passive', {
            get: function() {
                supportsPassive = true;
            }
        });
        window.addEventListener('testPassive', null, opts);
        window.removeEventListener('testPassive', null, opts);
    } catch (e) {
        // 不支持被动事件监听器
    }
    
    // 优化 TinyMCE 触摸事件
    function optimizeTinyMCETouchEvents() {
        if (!supportsPassive) return;
        
        // 等待 TinyMCE 初始化完成
        const checkTinyMCE = setInterval(function() {
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                clearInterval(checkTinyMCE);
                
                // 获取编辑器容器
                const editorContainer = tinymce.activeEditor.getContainer();
                if (editorContainer) {
                    // 为编辑器容器添加被动触摸事件监听器
                    addPassiveTouchListeners(editorContainer);
                    
                    // 为编辑器内容区域添加被动触摸事件监听器
                    const contentArea = editorContainer.querySelector('.tox-edit-area');
                    if (contentArea) {
                        addPassiveTouchListeners(contentArea);
                    }
                    
                    // 为工具栏添加被动触摸事件监听器
                    const toolbar = editorContainer.querySelector('.tox-toolbar');
                    if (toolbar) {
                        addPassiveTouchListeners(toolbar);
                    }
                }
            }
        }, 100);
        
        // 10秒后停止检查
        setTimeout(() => clearInterval(checkTinyMCE), 10000);
    }
    
    // 为元素添加被动触摸事件监听器
    function addPassiveTouchListeners(element) {
        if (!element || !supportsPassive) return;
        
        // 移除可能存在的旧监听器
        element.removeEventListener('touchstart', function() {});
        element.removeEventListener('touchmove', function() {});
        element.removeEventListener('touchend', function() {});
        
        // 添加被动监听器
        element.addEventListener('touchstart', function(e) {
            // 被动监听器，不阻止默认行为
        }, { passive: true });
        
        element.addEventListener('touchmove', function(e) {
            // 被动监听器，不阻止默认行为
        }, { passive: true });
        
        element.addEventListener('touchend', function(e) {
            // 被动监听器，不阻止默认行为
        }, { passive: true });
    }
    
    // 抑制控制台警告显示
    function suppressConsoleWarnings() {
        // 保存原始的 console 方法
        const originalWarn = console.warn;
        const originalError = console.error;

        // 过滤特定的警告信息
        console.warn = function(...args) {
            const message = args.join(' ');

            // 过滤 TinyMCE 相关的性能警告
            if (message.includes('[Violation]') ||
                message.includes('document.write') ||
                message.includes('non-passive event listener') ||
                message.includes('touchstart') ||
                message.includes('touchmove')) {
                // 静默处理这些警告，不显示在控制台
                return;
            }

            // 其他警告正常显示
            return originalWarn.apply(this, arguments);
        };

        console.error = function(...args) {
            const message = args.join(' ');

            // 过滤字体 404 错误（如果字体文件确实不存在）
            if (message.includes('fontawesome-webfont.woff2') && message.includes('404')) {
                // 静默处理字体文件不存在的错误
                return;
            }

            // 其他错误正常显示
            return originalError.apply(this, arguments);
        };
    }

    // 优化 TinyMCE 内部的 document.write
    function optimizeTinyMCEDocumentWrite() {
        // 监听 TinyMCE 加载过程
        if (typeof tinymce !== 'undefined') {
            // 在 TinyMCE 初始化前进行优化
            const originalInit = tinymce.init;
            tinymce.init = function(config) {
                // 添加配置优化
                config = config || {};

                // 禁用一些可能导致 document.write 的功能
                config.convert_urls = false;
                config.remove_script_host = false;

                // 调用原始初始化
                return originalInit.call(this, config);
            };
        }
    }
    
    // 页面加载完成后执行优化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(optimizeTinyMCETouchEvents, 500);
            suppressConsoleWarnings(); // 启用控制台警告过滤
            optimizeTinyMCEDocumentWrite(); // 优化 TinyMCE document.write
        });
    } else {
        setTimeout(optimizeTinyMCETouchEvents, 500);
        suppressConsoleWarnings(); // 启用控制台警告过滤
        optimizeTinyMCEDocumentWrite(); // 优化 TinyMCE document.write
    }
    
    // 监听 TinyMCE 初始化事件
    if (typeof tinymce !== 'undefined') {
        tinymce.on('AddEditor', function(e) {
            const editor = e.editor;
            editor.on('init', function() {
                setTimeout(() => {
                    const container = editor.getContainer();
                    if (container) {
                        addPassiveTouchListeners(container);
                    }
                }, 100);
            });
        });
    }
    
})();
