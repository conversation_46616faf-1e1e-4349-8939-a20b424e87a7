/* 只在body上添加overflow-x: hidden，防止横向滚动条 */
body {
    overflow-x: hidden;
}

/* 产品中心页面样式 */

/* 内容容器 */
.content-container {
    width: 100%;
    margin: 0 auto;
    padding-left: 18.802083vw; /* 361px */
    position: relative;
}

/* 产品过滤导航容器 */
.editContent {
    width: 100%;
    height: 6.25vw; /* 120px */
    background: #F8F8F8;
    border-radius: 0 0 0 0;
    margin-bottom: 3.072916vw; /* 59px */
}

/* 产品过滤导航 */
.filter {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    list-style: none;
    padding: 0;
    margin: 0;
    padding-left: 18.802083vw; /* 361px */
    flex-wrap: nowrap;
}

.filter li {
    margin: 0;
    padding: 0;
    margin-right: 0.677083vw; /* 13px */
    display: flex;
    align-items: center;
}

.filter li:last-child {
    margin-right: 0;
}

.filter li a {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-family: "Source Han Sans CN", "思源黑体", sans-serif;
    font-size: 1.041666vw; /* 20px */
    font-weight: normal;
    line-height: 1.041666vw; /* 20px */
    text-align: center;
    letter-spacing: -0.045em;
    color: #646464;
    height: 2.083333vw; /* 40px */
    background: #FFFFFF;
    border-radius: 0.208333vw; /* 4px */
    border: 0.052083vw solid #646464; /* 1px */
    transition: all 0.3s ease;
    white-space: nowrap;
}

/* 设置每个菜单项的宽度 */
.filter li:nth-child(1) a {
    width: 4.375vw; /* 84px */
}

.filter li:nth-child(2) a {
    width: 7.65625vw; /* 147px */
}

.filter li:nth-child(3) a {
    width: 6.510416vw; /* 125px */
}

.filter li:nth-child(4) a {
    width: 6.510416vw; /* 125px */
}

.filter li:nth-child(5) a {
    width: 10.416666vw; /* 200px */
}

.filter li:nth-child(6) a {
    width: 10.416666vw; /* 200px */
}

.filter li:nth-child(7) a {
    width: 11.458333vw; /* 220px */
}

/* 统一高亮状态样式 */
.filter li a:hover,
.filter li.active a,
.filter li.active2 a,
.filter li a.active {
    color: #0050A2;
    border-color: #0050A2;
    text-decoration: none;
}

/* 产品展示区域容器 */
#row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1.927083vw 3.125vw; /* 37px 60px */
    padding: 0;
    max-width: 62.5vw; /* 1200px */
}

/* 产品卡片基础样式 */
.gallery-item-wrapper {
    width: 18.75vw; /* 360px */
    height: 18.75vw; /* 360px */
    margin: 0;
    padding: 0;
    flex: 0 0 18.75vw; /* 360px */
}

.gallery-item {
    width: 100%;
    height: 100%;
    background: #FFFFFF;
    border-radius: 1.458333vw; /* 28px */
    border: 0.104166vw solid #B2B2B2; /* 2px */
    transition: all 0.3s ease;
    overflow: hidden;
    cursor: pointer;
    position: relative;
}

/* 产品卡片悬停效果 */
.gallery-item:hover {
    background: #F8F8F8;
    border-color: #0050A2;
}

/* 产品图片容器 */
.gallery-thumb {
    width: 100%;
    height: calc(100% - 3.802083vw); /* 73px (28px标题高度 + 45px底部间距) */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.041666vw; /* 20px */
}

.gallery-thumb img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 产品标题样式 */
.gallery-details {
    position: absolute;
    bottom: 2.34375vw; /* 45px */
    left: 0;
    width: 100%;
    text-align: center;
    background: transparent;
}

.ptitle {
    font-family: "思源黑体", "Source Han Sans CN", sans-serif;
    font-size: 1.458333vw; /* 28px */
    font-weight: normal;
    line-height: 1.458333vw; /* 28px */
    letter-spacing: -0.045em;
    color: #323232;
    margin: 0;
    padding: 0;
    text-transform: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.3s ease;
}

/* 英文界面产品标题样式调整 */
[lang="en"] .ptitle,
html[lang="en"] .ptitle,
.product_en .ptitle,
body.en .ptitle {
    font-size: 1vw; /* 24px，比中文小约14% */
    line-height: 1.25vw;
    letter-spacing: -0.02em; /* 减少字母间距 */
    font-family: Arial, Helvetica, sans-serif; /* 使用英文字体 */
}

/* 标题悬停效果 */
.gallery-item:hover .ptitle {
    color: #0050A2;
}

/* 页脚上间距 */
footer {
    margin-top: 4.21875vw; /* 81px */
}

/* 针对491px以下屏幕的样式调整 */
@media screen and (max-width: 491px) {
    /* 减小菜单栏的左间距 */
    .filter {
        padding-left: 9.401041vw; /* 361px/2 = 180.5px */
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    /* 重置所有菜单项的固定宽度设置 */
    .filter li:nth-child(1) a,
    .filter li:nth-child(2) a,
    .filter li:nth-child(3) a,
    .filter li:nth-child(4) a,
    .filter li:nth-child(5) a,
    .filter li:nth-child(6) a,
    .filter li:nth-child(7) a {
        width: auto;
        height: auto;
        padding: 3px 3px;
    }

    /* 调整菜单项之间的间距 */
    .filter li {
        margin-right: 4px;
    }

    /* 确保最后一个菜单项没有右边距 */
    .filter li:last-child {
        margin-right: 0;
    }

    /* 隐藏滚动条但保持功能 */
    .filter::-webkit-scrollbar {
        display: none;
    }

    /* 产品卡片标题样式调整 */
    .ptitle {
        font-size: 8px !important;
        transform: scale(0.8);
        transform-origin: center top;
        width: 130%;
        line-height: 1;
        position: relative;
        left: -15%;
        text-align: center;
    }
    
    /* 英文界面产品卡片标题小屏幕样式 */
    [lang="en"] .ptitle,
    html[lang="en"] .ptitle,
    .product_en .ptitle,
    body.en .ptitle {
        font-size: 8px !important;
        transform: scale(0.45);
        transform-origin: center top;
        width: 220%;
        line-height: 1;
        position: relative;
        left: -60%;
        text-align: center;

    }
    
    /* 英文界面产品卡片尺寸扩大1.2倍 */
    [lang="en"] .gallery-item-wrapper,
    html[lang="en"] .gallery-item-wrapper,
    .product_en .gallery-item-wrapper,
    body.en .gallery-item-wrapper {
        width: 18.75*3.5vw; /* 360px */
        height: 18.75*3vw; /* 360px */

    }
    

    
    /* 调整标题与底部的间距 */
    .gallery-details {
        bottom: 1.171875vw; /* 45px/2 = 22.5px */
    }
}
