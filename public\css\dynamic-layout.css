/* 动态布局CSS */

/* 模块容器使用flex布局 */
.modules-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
}

/* 每个模块的基本样式 */
.module {
  flex-basis: calc(50% - 10px); /* 两列布局，考虑间距 */
  box-sizing: border-box;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

/* 空模块默认隐藏 */
.module.empty {
  display: none;
}

/* 模块标题栏 */
.module-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #eaeaea;
}

.module-header i {
  margin-right: 10px;
  color: #0d6efd;
}

.module-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

/* 模块内容区域 */
.module-content {
  padding: 15px;
  min-height: 50px; /* 最小高度 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .module {
    flex-basis: 100%; /* 在小屏幕上单列显示 */
  }
} 