:root {
    --scale-factor-percent: calc(100vw / 1920);
}
.others-section {
    width: 100%;
    height: 59.79vw; /* 1148px -> 59.79vw */
    background: #F8F8F8;
    border-radius: 0px 0px 0px 0px;
    padding: 4.27vw 0 0 0; /* 82px -> 4.27vw */
    box-sizing: border-box;
}

.others-container {
    width: 62.5vw; /* 1200px -> 62.5vw */
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 2.5vw; /* 48px -> 2.5vw */
    box-sizing: border-box;
}

.news-section {
    width: 62.5vw; /* 1200px -> 62.5vw */
    height: 27.24vw; /* 523px -> 27.24vw */
    background: #FFFFFF;
    border-radius: 1.46vw; /* 28px -> 1.46vw */
    border: 0.05vw solid #ABABAB; /* 1px -> 0.05vw */
    overflow: hidden;
    padding: 2.5vw 2.55vw 1.93vw 2.4vw; /* 48px 49px 37px 46px */
    margin: 0;
    box-sizing: border-box;
    position: relative;
}

.about-section {
    width: 62.5vw; /* 1200px -> 62.5vw */
    height: 21.72vw; /* 417px -> 21.72vw */
    background: #FFFFFF;
    border-radius: 1.88vw; /* 36px -> 1.88vw */
    border: 0.05vw solid #ABABAB; /* 1px -> 0.05vw */
    overflow: hidden;
    padding:2.5vw 2.5vw 2.5vw 2.4vw; /* 48px 48px 48px 46px */
    margin: 0;
    box-sizing: border-box;
    position: relative;
    display: flex;
    gap: 2.5vw; /* 48px -> 2.5vw */
}

.about-left {
    width: 28.54vw; /* 548px -> 28.54vw */
    padding: 0;
    box-sizing: border-box;
}

.about-right {
    width: 26.61vw; /* 511px -> 26.61vw */
    padding: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    border-bottom: none;
    margin-bottom: 0;    /* 添加这行确保底部无边距 */
    padding-bottom: 0;   /* 添加这行确保底部无内边距 */
}

.news-title {
    font-family: "Source Han Sans CN", sans-serif;
    font-weight: bold;
    font-size: 1.67vw; /* 32px -> 1.67vw */
    color: #00509F;
    line-height: 1.98vw; /* 38px -> 1.98vw */
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 0.47vw; /* 9px -> 0.47vw */
    box-sizing: border-box;
}

.more-news {
    width: 10.42vw; /* 200px -> 10.42vw */
    height: 3.33vw; /* 64px -> 3.33vw */
    background: #00509F;
    border-radius: 0.63vw; /* 12px -> 0.63vw */
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 1.25vw; /* 24px -> 1.25vw */
    color: #FFFFFF;
    line-height: 2.76vw; /* 53px -> 2.76vw */
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    text-transform: none;
    font-style: normal;
}

.more-news:hover {
    background-color: #007DDB;
    color: #FFFFFF;
}

.news-list {
    border-top: none;
    box-sizing: border-box;
    margin-top: 1.25vw; /* 24px -> 1.25vw */
    padding-top: 0;     /* 添加这行确保顶部无内边距 */
}

.news-item {
    display: flex;
    padding: 0;
    box-sizing: border-box;
    margin: 0;
    align-items: center;
    flex-direction: column;
}

.news-item-content {
    display: flex;
    width: 100%;
    align-items: center;
}

.news-item-divider {
    width: 57.71vw; /* 1108px -> 57.71vw */
    border-top: 0.05vw solid #929292; /* 1px -> 0.05vw */
    margin: 0.57vw 0 0.78vw 0; /* 11px 0 15px 0 */

}

.news-time {
    width: 7.34vw; /* 141px -> 7.34vw */
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 1.04vw; /* 20px -> 1.04vw */
    color: #4C4C4C;
    line-height: 1.82vw; /* 35px -> 1.82vw */
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-right: 0.99vw; /* 19px -> 0.99vw */
}

.news-content {
    flex: 1;
    box-sizing: border-box;
}

.news-content-text {
    width: 49.06vw; /* 942px -> 49.06vw */
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 1.04vw; /* 20px -> 1.04vw */
    color: #4C4C4C;
    line-height: 1.82vw; /* 35px -> 1.82vw */
    text-align: left;
    font-style: normal;
    text-transform: none;
    white-space: nowrap;         /* 禁止换行 */
    overflow: hidden;            /* 隐藏超出部分 */
    text-overflow: ellipsis;     /* 显示省略号 */
    display: block;              /* 确保省略号效果生效 */
}

.news-content-text:hover {
    color: #00509F;
    text-decoration: none;
}

.about-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 1.3vw; /* 25px -> 1.3vw */
    margin-top: -0.99vw; /* -19px -> -0.99vw */
    box-sizing: border-box;
}

.about-title {
    font-family: "Source Han Sans CN", sans-serif;
    font-weight: bold;
    font-size: 1.67vw; /* 32px -> 1.67vw */
    color: #00509F;
    line-height: 1.98vw; /* 38px -> 1.98vw */
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-right: 13.33vw; /* 256px -> 13.33vw */
    box-sizing: border-box;
    white-space: nowrap;
}

.learn-more {
    width: 8.28vw; /* 159px -> 8.28vw */
    height: 2.81vw; /* 54px -> 2.81vw */
    background: #00509F;
    border-radius: 0.63vw; /* 12px -> 0.63vw */
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 1.04vw; /* 20px -> 1.04vw */
    color: #FFFFFF;
    line-height: 2.76vw; /* 53px -> 2.76vw */
    text-align: center;
    font-style: normal;
    text-transform: none;
}

.learn-more:hover {
    background-color: #007DDB;
    color: #FFFFFF;
}

.about-content {
    box-sizing: border-box;
    width: 28.54vw; /* 548px -> 28.54vw */
    height: 11.98vw; /* 230px -> 11.98vw */
    overflow: hidden;
}

.about-text {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 0.89vw; /* 17px -> 0.89vw */
    color: #333333;
    line-height: 1.77vw; /* 34px -> 1.77vw */
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.about-image-container {
    width: 26.61vw; /* 511px -> 26.61vw */
    height: 16.72vw; /* 321px -> 16.72vw */
    border-radius: 0.99vw; /* 19px -> 0.99vw */
    overflow: hidden;
    box-sizing: border-box;
}

.about-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    box-sizing: border-box;
}

.news-item:last-child .news-item-divider {
    display: none;
}

/* 英文界面特殊样式 */
html[lang="en"] .about-text,
body.english-version .about-text,
.index_en .about-text,
[data-lang="en"] .about-text {
    font-size: 0.83vw; /* 16px -> 0.83vw */
    line-height: 1.46vw; /* 28px -> 1.46vw */
}

/* 英文界面隐藏about-text-image */
html[lang="en"] .about-text-image,
body.english-version .about-text-image,
.index_en .about-text-image,
[data-lang="en"] .about-text-image {
    display: none !important; /* 桌面端英文界面不显示这个图片 */
}

/* 在移动端覆盖上面的规则，让图片显示出来 */
@media screen and (max-width: 491px) {
    html[lang="en"] .about-text-image,
    body.english-version .about-text-image,
    .index_en .about-text-image,
    [data-lang="en"] .about-text-image {
        display: block !important; /* 移动端英文界面显示图片 */
    }
    
    /* 新闻内容文字调整 */
    .news-content-text {
        font-size: 0.42vw !important; /* 8px -> 0.42vw */
        transform: scale(0.8);
        transform-origin: left top;
        display: block; /* 改为block以允许换行 */
        width: 130%; /* 恢复正常宽度 */
        line-height: 0.05vw; /* 1px -> 0.05vw */
    }
    
    /* 关于贝启科技的介绍文字调整 */
    .about-text {
        font-size: 0.68vw; /* 13px -> 0.68vw */
        line-height: 0.36vw; /* 7px -> 0.36vw */
        display: none; /* 在移动端隐藏文本 */
    }
    
    /* 标题文字也相应调整 */
    .news-title, .about-title {
        font-size: 1.25vw; /* 24px -> 1.25vw */
        line-height: 0.36vw; /* 7px -> 0.36vw */
    }
    
    /* 按钮文字调整 */
    .more-news, .learn-more {
        font-size: 0.73vw; /* 14px -> 0.73vw */
    }
    
    /* 扩大了解更多按钮的蓝色背景框 */
    .learn-more {
        width: 13.02vw; /* 250px -> 13.02vw */
        height: 2.08vw; /* 40px -> 2.08vw */
    }
    
    /* 扩大文本框大小 */
    .about-content {
        width: 100%; /* 占满左侧区域 */
        height: auto; /* 高度自适应 */
        max-height: none; /* 移除高度限制 */
        overflow: visible; /* 允许内容溢出 */
    }
    
    /* 移动端显示文本图片 */
    .about-text-image {
        display: block;
        width: 100%;
        height: auto;
        border-radius: 0.42vw; /* 8px -> 0.42vw */
        object-fit: contain;
    }
    
    /* 针对491px以下屏幕，将标题和按钮间距减半 */
    .about-title {
        margin-right: 5.21vw; /* 100px -> 5.21vw */
    }
    
}


