<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>News - Bearkey - Official Website</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    
    <!-- css -->
    <link href="./css/bootstrap.min.css" rel="stylesheet"/>
    <link href="./css/style.css" rel="stylesheet"/>
    <link href="./css/footer.css" rel="stylesheet"/>
    <link href="./css/news.css" rel="stylesheet"/>
    <link href="./css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="./css/flexslider.css" rel="stylesheet"/>
    <!-- Font Awesome Icon Library -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body>
<div id="wrapper" style="display: none;">
    <!-- Navigation Bar -->
    <header>
        <!-- Navigation will be dynamically loaded via nav.js -->
    </header>

    <!-- Page Title Banner -->
    <div class="page-title-banner">
        <div class="container">
            <h1 class="page-title">News</h1>
        </div>
    </div>

    <div class="container">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb">
            <a href="/index_en.html">Bearkey Official Website</a>
            <span class="separator">/</span>
            <span class="current">Bearkey Updates</span>
        </div>

        <!-- News List Area -->
        <div class="news-list" id="newsList">
            <!-- Sample news item, actual content will be loaded dynamically -->
            <div class="news-item">
                <div class="news-date">2024.11.20</div>
                <div class="news-content-wrapper">
                    <div class="news-image-container">
                        <img src="./images/news/news1.svg" alt="OpenHarmony Talent Ecosystem Conference 2024" class="news-image">
                    </div>
                    <div class="news-info">
                        <h2 class="news-title">
                            <a href="/news-detail_en.html?id=1">OpenHarmony Talent Ecosystem Conference 2024</a>
                        </h2>
                        <div class="news-desc">On April 13th, Xiamen University of Technology held an interactive teaching practice session for RK3568 OpenHarmony. On campus, our technical team engaged in an in-depth interactive journey about the RK3568 OpenHarmony development board with students full of curiosity and exploration spirit.</div>
                        <a href="/news-detail_en.html?id=1" class="news-more">Learn More</a>
                    </div>
                </div>
            </div>

            <!-- Sample news item -->
            <div class="news-item">
                <div class="news-date">2024.10.12</div>
                <div class="news-content-wrapper">
                    <div class="news-image-container">
                        <img src="./images/news/news2.svg" alt="Bearkey Technology Deepens HarmonyOS Ecosystem, Appears at the Third OpenHarmony Technology Conference" class="news-image">
                    </div>
                    <div class="news-info">
                        <h2 class="news-title">
                            <a href="/news-detail_en.html?id=2">Bearkey Technology Deepens HarmonyOS Ecosystem, Appears at the Third OpenHarmony Technology Conference</a>
                        </h2>
                        <div class="news-desc">On October 12th, at the Third OpenHarmony Technology Conference held at Shenzhen Science Museum, Bearkey Technology, as an important contributor to the HarmonyOS ecosystem, was invited to exhibit and showcase the latest HarmonyOS development boards and solutions.</div>
                        <a href="/news-detail_en.html?id=2" class="news-more">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <!-- Footer will be dynamically loaded via footer.js -->
    </footer>
</div>

<!-- Back to top button -->
<a href="#" class="scrollup" style="display: none;"><i class="fa fa-angle-up active"></i></a>

<!-- javascript -->
<script src="js/jquery.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.fancybox.pack.js"></script>
<script src="js/jquery.fancybox-media.js"></script>
<script src="js/jquery.flexslider.js"></script>
<script src="js/nav.js"></script>
<script src="js/floating-toolbar.js"></script>
<script src="js/toolbar-data.js"></script>
<script src="js/footer.js"></script>
<script>
    // 设置当前页面语言为英文
    window.currentLanguage = 'en';
</script>
<script src="js/news-data.js"></script>
<script src="js/custom.js"></script>
</body>
</html> 