// 处理新闻数据的导入
function loadNewsData(newsData) {
    const newsList = document.querySelector('.news-list');
    if (!newsList) {
        console.error('找不到新闻列表容器');
        return;
    }

    // 清空现有内容
    newsList.innerHTML = '';

    console.log('原始新闻数据:', newsData);

    // 检查是否为有效的日期格式（YYYY-MM-DD）
    function isValidDateFormat(str) {
        return /^\d{4}-\d{2}-\d{2}$/.test(str);
    }

    // 过滤条件：只需要 info_type 为 company_news 的内容
    const validNews = newsData.filter(news => {
        const isValid = news.info_type === 'company_news' && news.content && news.content.trim() !== '';
        if (!isValid) {
            console.log('过滤掉的新闻:', news, '原因:', 
                news.info_type !== 'company_news' ? 'info_type不匹配' : '内容为空');
        }
        return isValid;
    });

    console.log('过滤后的有效新闻:', validNews);

    if (validNews.length === 0) {
        console.log('没有找到有效的新闻数据');
        newsList.innerHTML = '<div class="no-news">暂无新闻</div>';
        return;
    }

    validNews.forEach((news, index) => {
        const newsItem = document.createElement('div');
        newsItem.className = 'news-item';

        const newsItemContent = document.createElement('div');
        newsItemContent.className = 'news-item-content';

        // 创建日期部分
        const dateDiv = document.createElement('div');
        dateDiv.className = 'news-time';

        // 处理日期显示
        let displayDate = news.title;
        if (isValidDateFormat(news.title)) {
            const date = new Date(news.title);
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            displayDate = `${year} - ${month} - ${day}`;
        }

        dateDiv.textContent = displayDate;

        // 创建内容部分
        const contentDiv = document.createElement('div');
        contentDiv.className = 'news-content';

        const contentLink = document.createElement('a');
        contentLink.href = news.url || '#';
        contentLink.className = 'news-content-text';
        contentLink.textContent = news.content;

        contentDiv.appendChild(contentLink);
        newsItemContent.appendChild(dateDiv);
        newsItemContent.appendChild(contentDiv);
        newsItem.appendChild(newsItemContent);

        // 添加分割线（除了最后一个项目）
        if (index < validNews.length - 1) {
            const divider = document.createElement('div');
            divider.className = 'news-item-divider';
            newsItem.appendChild(divider);
        }

        newsList.appendChild(newsItem);
    });

    // 处理"更多动态"按钮
    const moreNewsLink = document.querySelector('.more-news');
    if (moreNewsLink) {
        // 判断当前是否为英文网站
        const isEnglishSite = window.location.pathname.includes('/en/') || 
                             window.location.pathname.includes('index_en.html') ||
                             window.location.hostname.includes('en.') || 
                             window.location.search.includes('lang=en') ||
                             document.documentElement.lang === 'en';
        
        // 更新按钮文本
        if (isEnglishSite) {
            moreNewsLink.textContent = 'More News';
        }
        
        // 查找 info_type 为 company_news 且 title 为"更多资讯"或"More News"的数据
        const moreNewsData = newsData.find(item => 
            item.info_type === 'company_news' && 
            (item.title === '更多资讯' || item.title === 'More News')
        );
        if (moreNewsData && moreNewsData.url) {
            moreNewsLink.href = moreNewsData.url;
            console.log('设置更多资讯链接:', moreNewsData.url);
        }
    }
}

// 获取新闻数据
function fetchCompanyNews() {
    console.log('开始获取新闻数据');
    
    // 判断当前是否为英文网站
    const isEnglishSite = window.location.pathname.includes('/en/') || 
                         window.location.pathname.includes('index_en.html') ||
                         window.location.hostname.includes('en.') || 
                         window.location.search.includes('lang=en');
    
    const params = {
        info_type: 'company_news',
        show: true,
        lang: isEnglishSite ? 1 : 0
    };
    
    console.log('请求参数:', params);
    
    // 使用 jQuery 的 ajax 方法，因为它在处理某些类型的响应时更可靠
    $.ajax({
        url: '/apis/company_info/list',
        method: 'GET',
        data: {
            page: 1,
            page_size: 10,
            filters: JSON.stringify(params)
        },
        success: function(result) {
            console.log('API响应:', result);
            if (result.status === 'ok' && result.data) {
                loadNewsData(result.data);
            } else {
                console.error('API返回状态不正确:', result);
                const newsList = document.querySelector('.news-list');
                if (newsList) {
                    newsList.innerHTML = '<div class="error-message">获取新闻失败</div>';
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('API请求失败:', status, error);
            const newsList = document.querySelector('.news-list');
            if (newsList) {
                newsList.innerHTML = '<div class="error-message">获取新闻失败，请稍后重试</div>';
            }
        }
    });
}

// 确保DOM完全加载后再执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        document.addEventListener('navbarLoaded', fetchCompanyNews);
    });
} else {
    document.addEventListener('navbarLoaded', fetchCompanyNews);
} 