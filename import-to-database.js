const fs = require('fs');
const path = require('path');
const mysql = require('mysql2');

/**
 * 数据库配置
 * 请根据您的数据库配置修改以下参数
 */
const DB_CONFIG = {
    host: 'localhost',
    user: 'root',
    password: 'beiqifxl',  // 使用与server.js相同的密码
    database: 'company_website',  // 使用现有的数据库
    charset: 'utf8mb4'
};

/**
 * 配置常量
 */
const NEW_DATA_DIR = './new_data';  // JSON文件目录
// 数据库记录已处理文件，不再使用本地JSON文件

// 创建数据库连接
const db = mysql.createConnection(DB_CONFIG);

// 禁用严格模式，允许NULL值和空字符串
db.query("SET SESSION sql_mode='NO_ENGINE_SUBSTITUTION'", (err) => {
    if (!err) {
        console.log('成功设置SQL模式为NO_ENGINE_SUBSTITUTION');
    }
});

// 处理数据库连接断开的情况
db.on('error', function(err) {
    if(err.code === 'PROTOCOL_CONNECTION_LOST') {
        handleDisconnect();
    } else {
        throw err;
    }
});

function handleDisconnect() {
    db.connect(function(err) {
        if(err) {
            console.log('数据库重连失败，2秒后重试...');
            setTimeout(handleDisconnect, 2000);
        } else {
            console.log('数据库重连成功');
        }
    });
}

/**
 * 从数据库读取已处理文件记录
 * @param {Function} callback 回调函数
 */
function loadProcessedFiles(callback) {
    const query = 'SELECT file_name, last_modified FROM processed_files';

    db.query(query, (err, results) => {
        if (err) {
            console.warn('⚠️  读取已处理文件记录失败:', err.message);
            return callback(null, {});
        }

        // 转换为对象格式 {fileName: {lastModified: ...}}
        const processedFiles = {};
        results.forEach(row => {
            processedFiles[row.file_name] = {
                lastModified: row.last_modified ? row.last_modified.toISOString() : null
            };
        });

        callback(null, processedFiles);
    });
}

/**
 * 保存已处理文件记录到数据库
 * @param {string} fileName 文件名
 * @param {string} filePath 文件路径
 * @param {Date} lastModified 文件最后修改时间
 * @param {number} articlesCount 文章数量
 * @param {number} sectionsCount 段落数量
 * @param {Function} callback 回调函数
 */
function saveProcessedFile(fileName, filePath, lastModified, articlesCount, sectionsCount, callback) {
    const query = `
        INSERT INTO processed_files (file_name, file_path, last_modified, articles_count, sections_count)
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            file_path = VALUES(file_path),
            last_modified = VALUES(last_modified),
            articles_count = VALUES(articles_count),
            sections_count = VALUES(sections_count),
            processed_at = CURRENT_TIMESTAMP
    `;

    db.query(query, [fileName, filePath, lastModified, articlesCount, sectionsCount], (err, result) => {
        if (err) {
            console.error('❌ 保存处理记录失败:', err.message);
            return callback(err);
        }
        console.log('📝 已更新处理记录到数据库');
        callback(null);
    });
}

/**
 * 获取new_data目录中的所有JSON文件
 * @returns {Array} JSON文件路径数组
 */
function getJsonFiles() {
    try {
        if (!fs.existsSync(NEW_DATA_DIR)) {
            console.error(`❌ 目录不存在: ${NEW_DATA_DIR}`);
            return [];
        }

        const files = fs.readdirSync(NEW_DATA_DIR);
        const jsonFiles = files
            .filter(file => file.toLowerCase().endsWith('.json'))
            .map(file => path.join(NEW_DATA_DIR, file));

        console.log(`📁 在 ${NEW_DATA_DIR} 目录中找到 ${jsonFiles.length} 个JSON文件`);
        return jsonFiles;
    } catch (error) {
        console.error('❌ 读取目录失败:', error.message);
        return [];
    }
}

/**
 * 创建数据库表结构
 * @param {Function} callback 回调函数
 */
function createTables(callback) {
    console.log('📋 创建数据库表结构...');

    // 先删除旧表，确保表结构是最新的
    console.log('🗑️  正在删除旧数据库表...');
    const dropTablesQuery = `
        DROP TABLE IF EXISTS wechat_articles;
        DROP TABLE IF EXISTS processed_files;
    `;

    // 分别执行删除语句
    db.query('DROP TABLE IF EXISTS wechat_articles', (err) => {
        if (err) {
            console.error('❌ 删除文章表失败:', err);
            return callback(err);
        }
        console.log('✅ 旧文章表已删除');

        db.query('DROP TABLE IF EXISTS processed_files', (err) => {
            if (err) {
                console.error('❌ 删除文件记录表失败:', err);
                return callback(err);
            }
            console.log('✅ 旧文件记录表已删除');

            // 创建新表
            createNewTables(callback);
        });
    });
}

/**
 * 创建新的数据库表
 * @param {Function} callback 回调函数
 */
function createNewTables(callback) {
        // 创建简化的文章表，只包含关键信息
        const createArticlesTable = `
            CREATE TABLE IF NOT EXISTS wechat_articles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                article_id VARCHAR(255) NOT NULL,
                title VARCHAR(500),
                digest TEXT,
                content_text LONGTEXT,
                image_urls JSON,
                section_order INT DEFAULT 0 COMMENT '内容块顺序',
                parent_article_id VARCHAR(255) COMMENT '原始文章ID',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_article_id (article_id),
                INDEX idx_parent_article_id (parent_article_id),
                INDEX idx_section_order (section_order),
                INDEX idx_title (title(100)),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `;

        // 创建已处理文件记录表
        const createProcessedFilesTable = `
            CREATE TABLE IF NOT EXISTS processed_files (
                id INT PRIMARY KEY AUTO_INCREMENT,
                file_name VARCHAR(255) UNIQUE NOT NULL,
                file_path VARCHAR(500),
                last_modified DATETIME,
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                articles_count INT DEFAULT 0,
                sections_count INT DEFAULT 0,
                INDEX idx_file_name (file_name),
                INDEX idx_processed_at (processed_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `;

        db.query(createArticlesTable, (err) => {
            if (err) {
                console.error('❌ 创建文章表失败:', err);
                return callback(err);
            }
            console.log('✅ 文章表创建完成');

            // 创建已处理文件记录表
            db.query(createProcessedFilesTable, (err) => {
                if (err) {
                    console.error('❌ 创建文件记录表失败:', err);
                    return callback(err);
                }
                console.log('✅ 文件记录表创建完成');
                callback(null);
            });
        });
}

/**
 * 从HTML内容中提取纯文本
 * @param {string} html HTML内容
 * @returns {string} 纯文本内容
 */
function extractTextFromHtml(html) {
    if (!html) return '';
    
    // 简单的HTML标签移除（在生产环境中建议使用专门的HTML解析库）
    return html
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // 移除script标签
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')   // 移除style标签
        .replace(/<[^>]*>/g, '')                          // 移除所有HTML标签
        .replace(/&nbsp;/g, ' ')                          // 替换&nbsp;
        .replace(/&lt;/g, '<')                            // 替换&lt;
        .replace(/&gt;/g, '>')                            // 替换&gt;
        .replace(/&amp;/g, '&')                           // 替换&amp;
        .replace(/&quot;/g, '"')                          // 替换&quot;
        .replace(/&#39;/g, "'")                           // 替换&#39;
        .replace(/\s+/g, ' ')                             // 合并多个空格
        .trim();
}

/**
 * 从HTML内容中按顺序提取图片URL
 * @param {string} html HTML内容
 * @returns {Array} 按顺序排列的图片URL数组
 */
function extractImagesFromHtml(html) {
    if (!html) return [];

    const images = [];
    const imgRegex = /<img[^>]*(?:src|data-src)=["']([^"']+)["'][^>]*>/gi;

    let match;
    while ((match = imgRegex.exec(html)) !== null) {
        const imgTag = match[0];

        // 优先获取data-src，如果没有则获取src
        const dataSrcMatch = imgTag.match(/data-src=["']([^"']+)["']/i);
        const srcMatch = imgTag.match(/src=["']([^"']+)["']/i);

        const imageUrl = dataSrcMatch ? dataSrcMatch[1] : (srcMatch ? srcMatch[1] : null);

        if (imageUrl && !images.includes(imageUrl)) {
            images.push(imageUrl);
        }
    }

    return images;
}

/**
 * 将HTML内容按模块分段（图片+文字块）
 * @param {string} html HTML内容
 * @returns {Array} 分段后的内容数组
 */
function parseContentSections(html) {
    if (!html) return [];

    const sections = [];

    // 将HTML按段落和图片分割
    // 先处理图片标签，为每个图片添加分割标记
    let processedHtml = html.replace(/<img[^>]*>/gi, (match) => {
        return `|||SECTION_BREAK|||${match}|||SECTION_BREAK|||`;
    });

    // 按分割标记分段
    const rawSections = processedHtml.split('|||SECTION_BREAK|||').filter(section => section.trim());

    let currentTextContent = '';
    let sectionOrder = 0;

    rawSections.forEach((section, index) => {
        const trimmedSection = section.trim();
        if (!trimmedSection) return;

        // 检查是否是图片标签
        if (trimmedSection.match(/^<img[^>]*>$/i)) {
            // 如果之前有文字内容，先保存文字段
            if (currentTextContent.trim()) {
                const textContent = extractTextFromHtml(currentTextContent);
                if (textContent.trim()) {
                    sections.push({
                        order: sectionOrder++,
                        type: 'text',
                        content_text: textContent.trim(),
                        image_urls: [],
                        original_html: currentTextContent.trim()
                    });
                }
                currentTextContent = '';
            }

            // 保存图片段
            const imageUrls = extractImagesFromHtml(trimmedSection);
            if (imageUrls.length > 0) {
                sections.push({
                    order: sectionOrder++,
                    type: 'image',
                    content_text: '',
                    image_urls: imageUrls,
                    original_html: trimmedSection
                });
            }
        } else {
            // 累积文字内容
            currentTextContent += section;
        }
    });

    // 处理最后剩余的文字内容
    if (currentTextContent.trim()) {
        const textContent = extractTextFromHtml(currentTextContent);
        if (textContent.trim()) {
            sections.push({
                order: sectionOrder++,
                type: 'text',
                content_text: textContent.trim(),
                image_urls: [],
                original_html: currentTextContent.trim()
            });
        }
    }

    // 如果没有找到明确的分段，将整个内容作为一段
    if (sections.length === 0 && html.trim()) {
        const textContent = extractTextFromHtml(html);
        const imageUrls = extractImagesFromHtml(html);

        sections.push({
            order: 0,
            type: 'mixed',
            content_text: textContent.trim(),
            image_urls: imageUrls,
            original_html: html.trim()
        });
    }

    console.log(`  📑 内容分段完成: 共 ${sections.length} 个段落`);
    sections.forEach((section, index) => {
        console.log(`    ${index + 1}. ${section.type} - 文字:${section.content_text.length}字符, 图片:${section.image_urls.length}张`);
    });

    return sections;
}

/**
 * 导入单篇文章到数据库（按模块分段）
 * @param {Object} article 文章数据
 * @param {Function} callback 回调函数
 */
function importSingleArticle(article, callback) {
    let insertedCount = 0;

    if (!article.news_item || article.news_item.length === 0) {
        console.log(`⚠️  文章 ${article.article_id} 没有内容，跳过`);
        return callback(null, insertedCount);
    }

    // 处理多个news_item的情况
    let processedItems = 0;
    const totalItems = article.news_item.length;
    let hasError = false;

    article.news_item.forEach((item, itemIndex) => {
        if (hasError) return;

        try {
            console.log(`    📄 处理内容项 ${itemIndex + 1}/${totalItems}: ${item.title || '无标题'}`);

            // 将内容按模块分段
            const contentSections = parseContentSections(item.content);

            if (contentSections.length === 0) {
                console.log(`    ⚠️  内容项 ${itemIndex + 1} 没有有效内容，跳过`);
                processedItems++;
                if (processedItems === totalItems) {
                    callback(null, insertedCount);
                }
                return;
            }

            // 为每个内容段创建数据库记录
            let processedSections = 0;
            const totalSections = contentSections.length;

            contentSections.forEach((section, sectionIndex) => {
                // 生成唯一的article_id：原始ID_项目索引_段落索引
                const sectionArticleId = totalItems > 1
                    ? `${article.article_id}_${itemIndex + 1}_${sectionIndex + 1}`
                    : `${article.article_id}_${sectionIndex + 1}`;

                // 将图片URL数组转换为JSON字符串
                const imageUrlsJson = JSON.stringify(section.image_urls);

                // 插入文章段落记录
                const insertSectionQuery = `
                    INSERT INTO wechat_articles (
                        article_id, title, digest, content_text, image_urls,
                        section_order, parent_article_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                        title = VALUES(title),
                        digest = VALUES(digest),
                        content_text = VALUES(content_text),
                        image_urls = VALUES(image_urls),
                        section_order = VALUES(section_order),
                        parent_article_id = VALUES(parent_article_id),
                        updated_at = CURRENT_TIMESTAMP
                `;

                db.query(insertSectionQuery, [
                    sectionArticleId,
                    item.title || null,
                    item.digest || null,
                    section.content_text,
                    imageUrlsJson,
                    section.order,
                    article.article_id
                ], (err, result) => {
                    if (err) {
                        console.error(`    ❌ 插入段落失败: ${err.message}`);
                        hasError = true;
                        return callback(err);
                    }

                    insertedCount++;
                    console.log(`    ✅ 段落 ${sectionIndex + 1}/${totalSections}: ${section.type} (文字:${section.content_text.length}字符, 图片:${section.image_urls.length}张)`);

                    processedSections++;
                    if (processedSections === totalSections) {
                        // 当前item的所有段落处理完成
                        processedItems++;
                        if (processedItems === totalItems) {
                            // 所有item处理完成
                            callback(null, insertedCount);
                        }
                    }
                });
            });

        } catch (error) {
            console.error(`  ❌ 处理文章失败: ${error.message}`);
            hasError = true;
            callback(error);
        }
    });
}

/**
 * 自动处理new_data目录中的所有JSON文件
 */
function processAllJsonFiles() {
    console.log('🚀 开始自动处理new_data目录中的JSON文件...');

    // 获取所有JSON文件
    const jsonFiles = getJsonFiles();
    if (jsonFiles.length === 0) {
        console.log('📭 没有找到JSON文件，程序结束');
        return;
    }

    // 从数据库读取已处理文件记录
    loadProcessedFiles((err, processedFiles) => {
        if (err) {
            console.error('❌ 读取处理记录失败:', err);
            return;
        }

        console.log(`📋 已处理文件记录: ${Object.keys(processedFiles).length} 个文件`);

    // 筛选出未处理的文件
    const unprocessedFiles = jsonFiles.filter(filePath => {
        const fileName = path.basename(filePath);
        const fileStats = fs.statSync(filePath);
        const fileInfo = processedFiles[fileName];

        // 如果文件未处理过，或者文件已被修改
        if (!fileInfo || fileInfo.lastModified !== fileStats.mtime.toISOString()) {
            return true;
        }

        console.log(`⏭️  跳过已处理文件: ${fileName}`);
        return false;
    });

    if (unprocessedFiles.length === 0) {
        console.log('✅ 所有文件都已处理完成，无需重复处理');
        return;
    }

    console.log(`📥 找到 ${unprocessedFiles.length} 个未处理的文件:`);
    unprocessedFiles.forEach((file, index) => {
        console.log(`  ${index + 1}. ${path.basename(file)}`);
    });

    // 连接数据库
    console.log('\n🔌 连接数据库...');
    db.ping((err) => {
        if (err) {
            console.error('❌ 数据库连接失败:', err);
            return;
        }
        console.log('✅ 数据库连接成功');

        // 创建表结构
        createTables((err) => {
            if (err) {
                console.error('❌ 创建表失败:', err);
                return;
            }

            // 开始批量处理文件
            processBatchFiles(unprocessedFiles);
        });
    });
    }); // 闭合 loadProcessedFiles 回调
}

/**
 * 批量处理文件
 * @param {Array} filesToProcess 待处理文件列表
 */
function processBatchFiles(filesToProcess) {
    let currentFileIndex = 0;
    let totalInserted = 0;
    let totalSuccessFiles = 0;
    let totalFailedFiles = 0;
    const failedFiles = [];

    function processNextFile() {
        if (currentFileIndex >= filesToProcess.length) {
            // 所有文件处理完成
            showFinalSummary();
            return;
        }

        const filePath = filesToProcess[currentFileIndex];
        const fileName = path.basename(filePath);

        console.log(`\n${'='.repeat(60)}`);
        console.log(`📄 [${currentFileIndex + 1}/${filesToProcess.length}] 处理文件: ${fileName}`);
        console.log(`${'='.repeat(60)}`);

        importJsonToDatabase(filePath, (err, insertedCount) => {
            const fileStats = fs.statSync(filePath);

            if (err) {
                console.error(`❌ 处理文件失败: ${fileName} - ${err.message}`);
                totalFailedFiles++;
                failedFiles.push({
                    fileName: fileName,
                    error: err.message
                });
            } else {
                console.log(`✅ 文件处理成功: ${fileName} (插入 ${insertedCount} 条记录)`);
                totalInserted += insertedCount;
                totalSuccessFiles++;

                // 保存处理记录到数据库
                saveProcessedFile(
                    fileName,
                    filePath,
                    fileStats.mtime,
                    1, // 文章数量 (这里简化为1，实际可以统计)
                    insertedCount, // 段落数量
                    (err) => {
                        if (err) {
                            console.error(`❌ 保存处理记录失败: ${fileName}`);
                        }
                    }
                );
            }

            currentFileIndex++;
            // 处理下一个文件
            setTimeout(processNextFile, 100); // 短暂延迟避免数据库压力
        });
    }

    function showFinalSummary() {
        console.log('\n' + '='.repeat(80));
        console.log('🎉 批量处理完成！');
        console.log('='.repeat(80));
        console.log(`📊 处理统计:`);
        console.log(`  • 总文件数: ${filesToProcess.length}`);
        console.log(`  • 成功处理: ${totalSuccessFiles}`);
        console.log(`  • 处理失败: ${totalFailedFiles}`);
        console.log(`  • 总插入记录: ${totalInserted}`);
        console.log(`  • 成功率: ${((totalSuccessFiles / filesToProcess.length) * 100).toFixed(1)}%`);

        if (failedFiles.length > 0) {
            console.log('\n❌ 失败的文件:');
            failedFiles.forEach(item => {
                console.log(`  - ${item.fileName}: ${item.error}`);
            });
        }

        console.log('\n📝 处理记录已保存到: processed_files.json');
        console.log('='.repeat(80));

        // 关闭数据库连接
        db.end((err) => {
            if (err) {
                console.error('❌ 关闭数据库连接失败:', err);
            } else {
                console.log('🔌 数据库连接已关闭');
            }
        });
    }

    // 开始处理第一个文件
    processNextFile();
}

/**
 * 从JSON文件导入文章到数据库
 * @param {string} jsonFilePath JSON文件路径
 * @param {Function} callback 回调函数
 */
function importJsonToDatabase(jsonFilePath, callback) {
    try {
        // 读取JSON文件
        console.log(`📖 正在读取JSON文件: ${path.basename(jsonFilePath)}`);
        const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
        console.log(`📄 找到 ${jsonData.length} 篇文章`);

        // 开始导入
        console.log('📥 开始导入文章...');
        let totalInserted = 0;
        let successCount = 0;
        let processedCount = 0;
        const failedArticles = [];

        // 处理每篇文章
        jsonData.forEach((article, index) => {
            console.log(`  [${index + 1}/${jsonData.length}] 导入文章: ${article.article_id}`);

            importSingleArticle(article, (err, insertedCount) => {
                processedCount++;

                if (err) {
                    console.error(`  ❌ 导入文章 ${article.article_id} 失败: ${err.message}`);
                    failedArticles.push({
                        article_id: article.article_id,
                        error: err.message
                    });
                } else {
                    totalInserted += insertedCount;
                    successCount++;
                }

                // 检查是否所有文章都处理完成
                if (processedCount === jsonData.length) {
                    // 显示统计信息
                    console.log(`📊 文件处理完成: ${path.basename(jsonFilePath)}`);
                    console.log(`  • 处理文章数: ${jsonData.length}`);
                    console.log(`  • 成功导入: ${successCount}`);
                    console.log(`  • 失败数量: ${failedArticles.length}`);
                    console.log(`  • 插入记录数: ${totalInserted}`);
                    console.log(`  • 成功率: ${((successCount / jsonData.length) * 100).toFixed(1)}%`);

                    if (failedArticles.length > 0) {
                        console.log('  ❌ 失败的文章:');
                        failedArticles.forEach(item => {
                            console.log(`    - ${item.article_id}: ${item.error}`);
                        });
                    }

                    // 调用回调函数
                    if (callback) {
                        if (failedArticles.length === jsonData.length) {
                            // 所有文章都失败
                            callback(new Error('所有文章导入失败'), 0);
                        } else {
                            callback(null, totalInserted);
                        }
                    }
                }
            });
        });

    } catch (error) {
        console.error(`❌ 读取文件失败: ${error.message}`);
        if (callback) {
            callback(error, 0);
        }
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const args = process.argv.slice(2);

    console.log('🚀 微信文章数据导入工具');
    console.log(`🗄️  数据库: ${DB_CONFIG.host}:${DB_CONFIG.database}`);
    console.log('⚠️  请确保已正确配置数据库连接信息！');
    console.log('');

    if (args.length === 0 || args[0] === 'auto') {
        // 自动模式：处理new_data目录中的所有JSON文件
        console.log('🔄 自动模式：处理new_data目录中的所有JSON文件');
        console.log('📁 目标目录: ./new_data');
        console.log('📝 处理记录: ./processed_files.json');
        console.log('');

        try {
            processAllJsonFiles();
        } catch (error) {
            console.error(`❌ 自动处理失败: ${error.message}`);
            process.exit(1);
        }
    } else {
        // 手动模式：处理指定的JSON文件
        const jsonFilePath = args[0];

        if (!fs.existsSync(jsonFilePath)) {
            console.error(`❌ JSON文件不存在: ${jsonFilePath}`);
            console.log('');
            console.log('使用方法:');
            console.log('  自动模式: node import-to-database.js');
            console.log('  自动模式: node import-to-database.js auto');
            console.log('  手动模式: node import-to-database.js <JSON文件路径>');
            console.log('');
            console.log('示例:');
            console.log('  node import-to-database.js');
            console.log('  node import-to-database.js all_articles_1753266656246.json');
            process.exit(1);
        }

        console.log('📖 手动模式：处理指定文件');
        console.log(`📄 输入文件: ${jsonFilePath}`);
        console.log('');

        // 连接数据库
        console.log('🔌 连接数据库...');
        db.ping((err) => {
            if (err) {
                console.error('❌ 数据库连接失败:', err);
                process.exit(1);
            }
            console.log('✅ 数据库连接成功');

            // 创建表结构
            createTables((err) => {
                if (err) {
                    console.error('❌ 创建表失败:', err);
                    process.exit(1);
                }

                try {
                    importJsonToDatabase(jsonFilePath, (err, insertedCount) => {
                        if (err) {
                            console.error(`❌ 导入失败: ${err.message}`);
                            process.exit(1);
                        } else {
                            console.log(`\n🎉 导入完成！总共插入 ${insertedCount} 条记录`);
                        }

                        // 关闭数据库连接
                        db.end((err) => {
                            if (err) {
                                console.error('❌ 关闭数据库连接失败:', err);
                            } else {
                                console.log('🔌 数据库连接已关闭');
                            }
                        });
                    });
                } catch (error) {
                    console.error(`❌ 操作失败: ${error.message}`);
                    process.exit(1);
                }
            });
        });
    }
}

module.exports = {
    importJsonToDatabase,
    processAllJsonFiles,
    createTables,
    extractTextFromHtml,
    extractImagesFromHtml,
    importSingleArticle,
    loadProcessedFiles,
    saveProcessedFile,
    getJsonFiles,
    db // 导出数据库连接，方便其他模块使用
};
