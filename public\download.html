<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>RK3568数据采集网关-资料下载-Bearkey-官网</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    
    <!-- css -->
    <link href="./css/bootstrap.min.css" rel="stylesheet"/>
    <link href="./css/style.css" rel="stylesheet"/>
    <link href="./css/footer.css" rel="stylesheet"/>
    <link href="./css/download-page.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body>
<div id="wrapper" style="display: none;">
    <!-- 导航栏 -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header>

    <!-- 页面标题背景板 -->
    <div class="page-title-banner">
        <div class="container">
            <h1 class="page-title">资料下载</h1>
        </div>
    </div>

    <div class="containert">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">贝启科技官网</a>
            <span class="separator">/</span>
            <span class="current">资料下载</span>
        </div>

        <!-- 主要内容区域 -->
        <div class="download-main">
            <!-- 左侧选择区域 -->
            <div class="download-sidebar">
                <!-- 菜单将通过JavaScript动态加载 -->
            </div>

            <!-- 右侧内容区域 -->
            <div class="download-content">
                <!-- 产品展示区 -->
                <div class="download-product">
                    <div class="product-image">
                        <img src="" alt="产品图片" id="product-image" style="display:none;">
                    </div>
                    <div class="product-actions">
                        <div class="product-name" id="product-title">RK3566数据采集网关</div>
                        <div class="product-buttons" id="product-buttons">
                            <a href="#" class="btn-product">产品说明书</a>
                            <a href="#" class="btn-product">立即购买</a>
                        </div>
                    </div>
                </div>

                <!-- 下载内容容器 -->
                <div class="download-container">
                    <!-- 左列 -->
                    <div class="download-column" id="left-column"></div>
                    <!-- 右列 -->
                    <div class="download-column" id="right-column"></div>
                </div>

                <!-- 所有下载区块的容器 -->
                <div id="all-sections" style="display: none;">
                    <!-- 工具部分 -->
                    <div class="download-section" data-type="tools">
                        <h2 class="section-title">工具</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">RK驱动助手</div>
                                    <div class="item-note"></div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name"></div>
                                    <div class="item-note"></div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name"></div>
                                    <div class="item-note"></div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 资源部分 -->
                    <div class="download-section" data-type="resources">
                        <h2 class="section-title">资源</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Linux-headers</div>
                                    <div class="item-note">版本号：1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 源代码部分 -->
                    <div class="download-section" data-type="source">
                        <h2 class="section-title">源代码</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Android 7.1 industry SDK源码</div>
                                    <div class="item-note">版本号：1234</div>
                                    <div class="item-note">此资源SDK为主要维护版本</div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Linux-SDK源码包</div>
                                    <div class="item-note">版本号：1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 固件部分 -->
                    <div class="download-section" data-type="firmware">
                        <h2 class="section-title">固件</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Ubuntu 18.04 固件</div>
                                    <div class="item-note">版本号：1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Industry 10.1寸MPI固件</div>
                                    <div class="item-note">版本号：</div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件系统部分 -->
                    <div class="download-section" data-type="filesystem">
                        <h2 class="section-title">文件系统</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Linux根文件系统镜像(arm64&arm32)</div>
                                    <div class="item-note">版本号：1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="./images/home/<USER>" alt="下载">
                                    <span>下载</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <!-- 页脚将通过footer.js动态加载 -->
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript -->
<script src="js/jquery.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>

<script src="js/nav.js"></script>
<script src="js/floating-toolbar.js"></script>
<script src="js/toolbar-data.js"></script>
<script src="js/download.js"></script>
<script src="js/note-text-formatter.js"></script>
<script src="js/lazy-loader.js"></script>
<script>
// 返回置顶功能
$(document).ready(function() {
    // 监听滚动事件
    $(window).scroll(function(){
        if ($(this).scrollTop() > 100) {
            $('.scrollup').fadeIn();
        } else {
            $('.scrollup').fadeOut();
        }
    });

    // 点击返回顶部
    $('.scrollup').click(function(){
        $("html, body").animate({ scrollTop: 0 }, 1000);
        return false;
    });
});
</script>

<script>
// 全局菜单状态保存函数
function saveMenuState(menuId, submenuId) {
    localStorage.setItem('downloadMenuState', JSON.stringify({
        menuId: menuId,
        submenuId: submenuId,
        timestamp: Date.now()
    }));
}
</script>
</body>
</html> 