/**
 * 字体回退策略
 * 处理 Font Awesome 字体文件缺失的情况
 */

(function() {
    'use strict';
    
    // 检查字体文件是否存在
    function checkFontFile(url) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('HEAD', url, true);
            xhr.onload = function() {
                if (xhr.status === 200) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            };
            xhr.onerror = function() {
                resolve(false);
            };
            xhr.send();
        });
    }
    
    // 创建备用字体样式
    function createFallbackFontCSS() {
        const style = document.createElement('style');
        style.id = 'font-awesome-fallback';
        style.textContent = `
            /* Font Awesome 备用样式 */
            .fa, [class^="fa-"], [class*=" fa-"] {
                font-family: Arial, sans-serif !important;
                font-style: normal;
                font-weight: normal;
                text-decoration: none;
                display: inline-block;
                width: auto;
                height: auto;
                line-height: 1;
                vertical-align: baseline;
            }
            
            /* 常用图标的文本替代 */
            .fa-home:before { content: "🏠"; }
            .fa-user:before { content: "👤"; }
            .fa-cog:before { content: "⚙️"; }
            .fa-search:before { content: "🔍"; }
            .fa-edit:before { content: "✏️"; }
            .fa-save:before { content: "💾"; }
            .fa-trash:before { content: "🗑️"; }
            .fa-plus:before { content: "+"; }
            .fa-minus:before { content: "-"; }
            .fa-times:before { content: "×"; }
            .fa-check:before { content: "✓"; }
            .fa-arrow-left:before { content: "←"; }
            .fa-arrow-right:before { content: "→"; }
            .fa-arrow-up:before { content: "↑"; }
            .fa-arrow-down:before { content: "↓"; }
            .fa-download:before { content: "⬇"; }
            .fa-upload:before { content: "⬆"; }
            .fa-file:before { content: "📄"; }
            .fa-folder:before { content: "📁"; }
            .fa-image:before { content: "🖼️"; }
            .fa-video:before { content: "🎥"; }
            .fa-music:before { content: "🎵"; }
            .fa-link:before { content: "🔗"; }
            .fa-mail:before { content: "✉️"; }
            .fa-phone:before { content: "📞"; }
            .fa-calendar:before { content: "📅"; }
            .fa-clock:before { content: "🕐"; }
            .fa-star:before { content: "⭐"; }
            .fa-heart:before { content: "❤️"; }
            .fa-thumbs-up:before { content: "👍"; }
            .fa-thumbs-down:before { content: "👎"; }
            .fa-warning:before { content: "⚠️"; }
            .fa-info:before { content: "ℹ️"; }
            .fa-question:before { content: "❓"; }
            .fa-exclamation:before { content: "❗"; }
            
            /* TinyMCE 工具栏图标 */
            .icon-save:before { content: "💾"; }
            .icon-arrow-left:before { content: "←"; }
            .icon-th:before { content: "⊞"; }
            .icon-share-alt:before { content: "↗"; }
            .icon-icon-user:before { content: "👤"; }
            .icon-icon-home:before { content: "🏠"; }
            .icon-icon-th-list:before { content: "☰"; }
            .icon-icon-file:before { content: "📄"; }
            .icon-icon-info-sign:before { content: "ℹ️"; }
        `;
        document.head.appendChild(style);
    }
    
    // 检查并应用字体回退策略
    async function initFontFallback() {
        const fontUrls = [
            '/admin/font-awesome/fonts/fontawesome-webfont.woff2?v=4.0.3',
            '/admin/font-awesome/fonts/fontawesome-webfont.woff?v=4.0.3'
        ];
        
        let fontAvailable = false;
        
        // 检查字体文件是否可用
        for (const url of fontUrls) {
            const available = await checkFontFile(url);
            if (available) {
                fontAvailable = true;
                break;
            }
        }
        
        // 如果字体文件不可用，应用备用样式
        if (!fontAvailable) {
            console.log('🔤 Font Awesome 字体文件不可用，启用备用图标');
            createFallbackFontCSS();
            
            // 移除预加载链接，避免 404 错误
            const preloadLinks = document.querySelectorAll('link[rel="preload"][href*="fontawesome"]');
            preloadLinks.forEach(link => link.remove());
        } else {
            console.log('✅ Font Awesome 字体文件可用');
        }
    }
    
    // 页面加载完成后检查字体
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFontFallback);
    } else {
        initFontFallback();
    }
    
})();
