/**
 * 资料下载页面的弹窗功能和事件处理
 */
// 在文件最开始添加
console.log('=== download.js 开始加载 ===');

// 调试工具对象
const DebugTools = {
    logMenuState: function() {
        console.group('=== 菜单状态详细信息 ===');
        try {
            const state = localStorage.getItem('downloadMenuState');
            console.log('当前时间:', new Date().toLocaleString());
            console.log('当前页面:', window.location.pathname);
            console.log('原始状态数据:', state);
            
            if (state) {
                const parsedState = JSON.parse(state);
                console.log('解析后的状态:', {
                    menuId: parsedState.menuId,
                    submenuId: parsedState.submenuId,
                    parentMenu: parsedState.parentMenu,
                    lastPath: parsedState.lastPath,
                    保存时间: new Date(parsedState.timestamp).toLocaleString()
                });
            } else {
                console.log('localStorage中没有保存的菜单状态');
            }
        } catch (e) {
            console.error('读取状态出错:', e);
        }
        console.groupEnd();
    }
};

// 在document.ready之前先输出一次状态
console.log('=== 页面加载前的菜单状态 ===');
DebugTools.logMenuState();

// 兼容各浏览器的强制置顶函数
function forceScrollTop() {
    window.scrollTo(0, 0);
    if (document.documentElement) document.documentElement.scrollTop = 0;
    if (document.body) document.body.scrollTop = 0;
    var wrapper = document.getElementById('wrapper');
    if (wrapper) wrapper.scrollTop = 0;
}

$(document).ready(function() {
    setTimeout(forceScrollTop, 50); // 页面加载时强制置顶，兼容国产浏览器
    console.log('=== document.ready 触发 ===');
    DebugTools.logMenuState();

    // 语言切换缓存清理机制
    clearLanguageSwitchCache();
    
    // 判断当前页面是否是download.html或download_en.html
    function isDownloadPage() {
        const path = window.location.pathname;
        return path.endsWith('/download.html') || path.endsWith('/download_en.html');
    }

    // 如果是下载页面，强制设置终端菜单为展开状态
    if (isDownloadPage()) {
        const isEnglish = isEnglishSite();
        const terminalMenuTitle = isEnglish ? 'Terminal' : '终端';
        
        // 保存新的菜单状态，强制只展开终端菜单
        try {
            const newState = {
                menuId: null,
                parentMenu: terminalMenuTitle,
                isExpanded: true,
                timestamp: Date.now()
            };
            localStorage.setItem('downloadMenuState', JSON.stringify(newState));
            console.log('初始化终端菜单状态:', newState);
        } catch (e) {
            console.error('保存菜单状态出错:', e);
        }
    }
    
    // 在文件开头添加全局菜单状态管理
    // 菜单状态管理
    const MenuStateManager = {
        // 获取状态
        getState: function() {
            try {
                return JSON.parse(localStorage.getItem('downloadMenuState') || '{}');
            } catch (e) {
                console.error('读取菜单状态出错:', e);
                return {};
            }
        },
        
        // 保存状态
        setState: function(state) {
            try {
                localStorage.setItem('downloadMenuState', JSON.stringify(state));
            } catch (e) {
                console.error('保存菜单状态出错:', e);
            }
        },
        
        // 保存菜单项状态
        saveMenuState: function(menuId, submenuId, parentMenu) {
            console.group('=== 保存菜单状态 ===');
            console.log('保存参数:', { menuId, submenuId, parentMenu });
            
            const state = {
                menuId: menuId,
                submenuId: submenuId,
                parentMenu: parentMenu,
                timestamp: Date.now(),
                lastPath: window.location.pathname
            };
            
            try {
                localStorage.setItem('downloadMenuState', JSON.stringify(state));
                console.log('状态保存成功:', state);
            } catch (e) {
                console.error('保存状态失败:', e);
            }
            
            console.groupEnd();
            return state;
        },
        
        // 获取最后一次选中的菜单
        getLastActiveMenu: function() {
            const state = this.getState();
            if (state && state.parentMenu && (Date.now() - (state.timestamp || 0) < 30 * 60 * 1000)) {
                return state.parentMenu;
            }
            return null;
        }
    };

    // 在文件开头添加调试函数
    const MenuDebugger = {
        logState: function() {
            console.group('菜单状态调试信息');
            console.log('当前页面路径:', window.location.pathname);
            
            const state = MenuStateManager.getState();
            console.log('localStorage中的菜单状态:', state);
            
            if (state.parentMenu) {
                console.log('最后激活的菜单:', state.parentMenu);
                console.log('最后激活时间:', new Date(state.timestamp).toLocaleString());
                console.log('最后访问路径:', state.lastPath);
            } else {
                console.log('没有保存的菜单状态');
            }
            
            console.groupEnd();
        }
    };

    // 添加URL处理工具函数
    function normalizeUrl(url) {
        if (!url) return '#';
        
        // 如果是完整的URL，直接返回
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }
        
        // 获取当前页面的路径信息
        const currentPath = window.location.pathname;
        const isInSubdir = currentPath.split('/').length > 3; // 检查是否在子目录中
        
        // 如果URL以'/'开头，从根目录开始
        if (url.startsWith('/')) {
            return url;
        }
        
        // 处理相对路径
        if (url.startsWith('./')) {
            url = url.substring(2);
        }
        
        // 如果在子目录中且URL是相对路径
        if (isInSubdir && !url.startsWith('../')) {
            // 如果URL包含'download/'前缀，移除它
            if (url.startsWith('download/')) {
                url = url.substring('download/'.length);
            }
            return '../' + url;
        }
        
        return url;
    }

    // 修改菜单项生成部分
    function generateMenuItem(item, childItem) {
        const childUrl = normalizeUrl(childItem.url || '');
        return `
            <div class="menu-item" data-id="${childItem.id}" data-parent-menu="${item.title}">
                <a href="${childUrl}" onclick="saveMenuStateOnProductClick(${item.id}, ${childItem.id}, '${item.title}'); return true;">${childItem.content || ''}</a>
            </div>
        `;
    }

    // 添加全局函数，保存从菜单点击进入产品页时的菜单状态
    window.saveMenuStateOnProductClick = function(menuId, submenuId, parentMenu) {
        console.log('保存菜单点击状态:', { menuId, submenuId, parentMenu });
        
        try {
            const state = {
                menuId: menuId,
                submenuId: submenuId,
                parentMenu: parentMenu,
                timestamp: Date.now()
            };
            localStorage.setItem('downloadMenuState', JSON.stringify(state));
        } catch (e) {
            console.error('保存菜单状态出错:', e);
        }
        
        return true;
    };

    // 修改菜单标题点击事件处理
    function bindMenuEvents() {
        // 绑定菜单点击事件
        $('.menu-title').off('click').on('click', function() {
            var $menu = $(this).closest('.expandable-menu');
            var wasExpanded = $menu.hasClass('expanded');
            var menuTitle = $menu.data('title');
            
            console.log('点击菜单:', menuTitle, '当前状态:', wasExpanded ? '展开' : '收起');
            
            if (wasExpanded) {
                // 收起当前菜单
                console.log('收起菜单:', menuTitle);
                $menu.removeClass('expanded');
                $menu.find('.arrow-icon').attr('src', '../images/home/<USER>');
                $menu.find('.menu-items').hide();
                $menu.find('.menu-divider').hide();
                
                // 注意：不保存菜单状态
            } else {
                // 展开当前菜单
                console.log('展开菜单:', menuTitle);
                $menu.addClass('expanded');
                $menu.find('.arrow-icon').attr('src', '../images/home/<USER>');
                $menu.find('.menu-items').show();
                $menu.find('.menu-divider').show();
                
                // 注意：不保存菜单状态
            }
            
            // 使用较短的延迟立即调整一次，然后再用较长的延迟再次调整
            setTimeout(adjustLayout, 50);
            setTimeout(adjustLayout, 300);
        });
    }

    // 检查元素是否由downloads.js控制
    function isControlledByDownloads(element) {
        return $(element).closest('[data-controller="downloads"]').length > 0;
    }

    // 检测是否在子目录中
    const isInSubdir = window.location.pathname.split('/').length > 2;
    const pathPrefix = isInSubdir ? '../' : '';

    // 在文档加载时先隐藏所有下载内容
    $('.download-section').hide();
    
    // 添加加载状态指示器
    // var loadingHtml = `
    //     <div class="loading-indicator" style="text-align: center; padding: 20px;">
    //         <div class="spinner" style="display: inline-block; width: 20px; height: 20px; border: 2px solid #f3f3f3; border-top: 2px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
    //         <style>
    //             @keyframes spin {
    //                 0% { transform: rotate(0deg); }
    //                 100% { transform: rotate(360deg); }
    //             }
    //         </style>
    //     </div>
    // `;
    // $('.download-container').prepend(loadingHtml);

    // 跟踪加载状态
    let loadingStates = {
        tools: false,
        resources: false,
        sourceCode: false,
        firmware: false,
        fileSystem: false
    };

    // 检查是否所有内容都已加载
    function checkAllLoaded() {
        return Object.values(loadingStates).every(state => state === true);
    }

    // 显示内容的函数（去掉.loading-indicator相关逻辑）
    function showContent() {
        if (checkAllLoaded()) {
            $('.download-section').show();
            // 重新计算列布局
            balanceColumns();

            // balanceColumns()后自动检测分栏效果，若失败则重试
            (function autoBalanceRetry() {
                let tryCount = 0;
                function checkAndRetry() {
                    const leftHas = $('#left-column').children().length > 0;
                    const rightHas = $('#right-column').children().length > 0;
                    if ((!leftHas || !rightHas) && tryCount < 5) {
                        balanceColumns();
                        tryCount++;
                        setTimeout(checkAndRetry, 120);
                    }
                }
                setTimeout(checkAndRetry, 120);
            })();

            setTimeout(forceScrollTop, 50); // 内容显示后再次置顶，兼容国产浏览器

            // 动态创建footer标签并插入body末尾
            if ($('footer').length === 0) {
                $('body').append('<footer></footer>');
            }
            // 动态加载footer.js并初始化footer
            var showFooter = function() {
                if (typeof window.initFooter === 'function') {
                    window.initFooter();
                    // 页脚初始化完成后，延迟调整布局
                    setTimeout(function() {
                        if (typeof adjustLayout === 'function') {
                            adjustLayout();
                        }
                    }, 500);
                }
            };
            if (!window.initFooter) {
                var script = document.createElement('script');
                script.src = 'js/footer.js';
                script.onload = showFooter;
                document.body.appendChild(script);
            } else {
                showFooter();
            }
        }
    }

    // 判断当前是否为英文网站（不使用缓存，确保语言切换时能正确检测）
    function isEnglishSite() {
        // 优先检查 HTML lang 属性
        const htmlLang = document.documentElement.lang || '';
        let result = false;
        if (htmlLang.toLowerCase().startsWith('en')) {
            result = true;
        } else if (htmlLang.toLowerCase().startsWith('zh')) {
            result = false;
        } else {
            // 如果没有明确的 lang 属性，再检查其他条件
            result = window.location.pathname.includes('_en.html') ||
                   window.location.pathname.includes('/en/') ||
                   window.location.hostname.includes('en.') ||
                   window.location.search.includes('lang=en');
        }
        return result;
    }

    // 获取语言标识符，用于缓存键
    function getLanguageId() {
        return isEnglishSite() ? 'en' : 'zh';
    }

    // 获取界面文本，根据当前语言返回对应文本
    function getText(textKey) {
        const textMap = {
            'download': isEnglishSite() ? 'Download' : '下载',
            'extractCode': isEnglishSite() ? 'Extract Code:' : '提取码：',
            'version': isEnglishSite() ? 'Version:' : '版本号：',
            'officialDownload': isEnglishSite() ? 'Official Download' : '官方下载',
            'noDownloadLinks': isEnglishSite() ? 'No download links available' : '暂无可用下载链接',
            'resourceDownloadDirectory': isEnglishSite() ? 'Downloads' : '资源下载目录'
        };
        return textMap[textKey] || textKey;
    }

    // 获取产品名称函数
    function getProductNameFromTitle() {
        const pageTitle = document.title;
        const lang = isEnglishSite();
        let productName = '';
        
        console.log('从页面标题提取产品名:', pageTitle);
        
        if (lang) {
            // 英文网站，格式如："RK3588 industrial control board-Downloads-Bearkey-Official Website"
            const match = pageTitle.match(/^([^-]+)/);
            if (match) {
                productName = match[1].trim();
                console.log('从英文标题提取的产品名:', productName);
            }
        } else {
            // 中文网站，格式如："RK3588工业控制主板-资料下载-Bearkey-官网"
            const match = pageTitle.match(/^([^-]+)/);
            if (match) {
                productName = match[1].trim();
                console.log('从中文标题提取的产品名:', productName);
            }
        }
        
        // 移除产品名称后可能的特殊字符或空格
        productName = productName.trim();
        console.log('最终提取的产品名:', productName);
        
        return productName;
    }

    // 加载工具部分数据（加缓存）
    function loadToolsSection() {
        console.log('开始加载工具部分...');
        const isEnglish = isEnglishSite();
        const lang = isEnglish ? 1 : 0;  // 根据当前语言环境决定获取哪种语言的数据
        const productName = getProductNameFromTitle();
        const languageId = getLanguageId();
        const cacheKey = `download_tools_section_${productName}_${languageId}_${lang}`;
        // 先查缓存
        const cachedData = getCache(cacheKey);
        if (cachedData) {
            renderToolsSection(cachedData, isEnglish, lang, productName);
            loadingStates.tools = true;
            showContent();
            return;
        }
        // 定义中英文产品名映射关系
        const productNameMap = {
            'RK3399Pro core board': 'RK3399Pro核心板',
            'RK3568 Data Acquisition Gateway': 'RK3568数据采集网关',
            // 可以根据需要添加更多映射
        };
        // 获取对应的中文产品名
        const chineseProductName = productNameMap[productName] || productName;
        // 英文产品名 + "工具"
        const englishToolType = productName + '工具';
        // 中文产品名 + "工具"
        const chineseToolType = chineseProductName + '工具';
        console.log('当前产品名(英文):', productName);
        console.log('当前产品名(中文):', chineseProductName);
        console.log('查找工具类型(英文):', englishToolType);
        console.log('查找工具类型(中文):', chineseToolType);
        console.log('当前语言:', isEnglish ? '英文' : '中文');
        $.ajax({
            url: '/apis/download_detail_list/',
            type: 'GET',
            data: { 
                lang: lang,  // 根据当前语言环境获取对应语言的数据
                page: 1,    // 从第1页开始
                size: 100   // 每页100条，确保获取所有数据
            },
            dataType: 'json',
            success: function(response) {
                if(response && response.status === 'ok' && response.data && response.data.length > 0) {
                    setCache(cacheKey, response.data, 60); // 缓存1小时
                    renderToolsSection(response.data, isEnglish, lang, productName);
                } else {
                    renderToolsSection([], isEnglish, lang, productName);
                }
                loadingStates.tools = true;
                showContent();
            },
            error: function(xhr, status, error) {
                renderToolsSection([], isEnglish, lang, productName);
                loadingStates.tools = true;
                showContent();
            }
        });
    }

    // 抽取渲染工具区为独立函数（加图片预加载）
    function renderToolsSection(responseData, isEnglish, lang, productName) {
        const productNameMap = {
            'RK3399Pro core board': 'RK3399Pro核心板',
            'RK3568 Data Acquisition Gateway': 'RK3568数据采集网关',
        };
        const chineseProductName = productNameMap[productName] || productName;
        const englishToolType = productName + '工具';
        const chineseToolType = chineseProductName + '工具';
        var tools = [];
        if (isEnglish) {
            tools = responseData.filter(function(item) {
                return (item.show === true || item.show === 1) && 
                       item.type === englishToolType &&
                       item.lang === lang;
            });
            if (tools.length === 0) {
                tools = responseData.filter(function(item) {
                    return (item.show === true || item.show === 1) && 
                           item.type === chineseToolType &&
                           item.lang === lang;
                });
            }
        } else {
            tools = responseData.filter(function(item) {
                return (item.show === true || item.show === 1) && 
                       item.type === chineseToolType &&
                       item.lang === lang;
            });
        }
        tools = tools.sort(function(a, b) {
            return (a.display_order || 100) - (b.display_order || 100);
        });
        if(tools.length > 0) {
            var toolsHtml = '';
            var preloadImages = [];
            tools.forEach(function(tool, idx) {
                var title = tool.title || '';
                var content = tool.content || '';
                var code = tool.code || '';
                var noteText = '';
                if (code) {
                    noteText = isEnglish ? `extracted code: ${code}` : `提取码: ${code}`;
                } else {
                    noteText = content;
                }
                // 预加载图片（如有图片字段）
                var imgSrc = tool.image_path || '';
                if (imgSrc && !imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
                    imgSrc = getPathPrefix() + imgSrc;
                }
                if (imgSrc) {
                    preloadImages.push({imgSrc, idx});
                }
                toolsHtml += `
                    <div class="download-item">
                        <div class="item-info">
                            <div class="item-name">${title}</div>
                            <div class="item-note">${noteText}</div>
                        </div>
                        <div class="download-action">
                            <img class="tool-img" data-imgidx="${idx}" style="display:none;" src="${imgSrc}" alt="${getText('download')}">
                            <span>${getText('download')}</span>
                        </div>
                    </div>
                `;
            });
            $('.download-section[data-type="tools"] .download-list').html(toolsHtml);
            // 预加载所有图片，加载完再显示
            preloadImages.forEach(function(obj) {
                var img = new window.Image();
                img.onload = function() {
                    $(`img.tool-img[data-imgidx='${obj.idx}']`).show();
                };
                img.src = obj.imgSrc;
            });
        } else {
            $('.download-section[data-type="tools"] .download-list').html('<div class="no-data">暂无工具数据</div>');
        }
    }

    // 获取当前页面的相对路径前缀（不需要缓存，计算很快）
    function getPathPrefix() {
        // 检查是否在子目录中
        const isInSubdir = window.location.pathname.split('/').length > 2;
        return isInSubdir ? '../' : './';
    }

    // 产品区域缓存key生成
    function getProductDisplayCacheKey() {
        const lang = isEnglishSite() ? 1 : 0;
        const languageId = getLanguageId();
        const productName = getProductNameFromTitle();
        return `download_product_display_${productName}_${languageId}_${lang}`;
    }

    // 修改loadProductDisplay函数，添加缓存
    function loadProductDisplay() {
        const lang = isEnglishSite() ? 1 : 0;
        const pathPrefix = getPathPrefix();
        const productName = getProductNameFromTitle();
        const cacheKey = getProductDisplayCacheKey();
        // 先查缓存
        const cachedData = getCache(cacheKey);
        if (cachedData) {
            renderProductDisplay(cachedData, pathPrefix, productName);
            loadingStates.resources = true;
            showContent();
            loadNavMenu();
            return;
        }
        // 获取产品数据
        $.ajax({
            url: '/apis/download_detail_list/',
            type: 'GET',
            data: { 
                type: '产品',
                lang: lang,
                page: 1,
                size: 1000
            },
            dataType: 'json',
            success: function(response) {
                if(response && response.status === 'ok' && response.data && response.data.length > 0) {
                    setCache(cacheKey, response.data, 60); // 缓存1小时
                    renderProductDisplay(response.data, pathPrefix, productName);
                }
                loadingStates.resources = true;
                showContent();
                loadNavMenu();
            }
        });
    }

    // 抽取产品展示区渲染为独立函数（加图片预加载）
    function renderProductDisplay(data, pathPrefix, productName) {
        var products = data
            .filter(function(item) {
                return item.show === true || item.show === 1;
            })
            .sort(function(a, b) {
                return (a.display_order || 100) - (b.display_order || 100);
            });
        var matchedProduct = products.find(function(product) {
            return product.title === productName;
        });
        if(matchedProduct) {
            // 预加载图片
            if(matchedProduct.image_path) {
                var imagePath = matchedProduct.image_path;
                if (!imagePath.startsWith('http') && !imagePath.startsWith('/')) {
                    imagePath = pathPrefix + imagePath;
                }
                // 彻底隐藏图片并移除src，避免浏览器显示loading占位符
                $('#product-image').hide().attr('src', '');
                var img = new window.Image();
                img.onload = function() {
                    $('#product-image').attr({
                        'src': imagePath,
                        'alt': matchedProduct.title || '产品图片'
                    }).show();
                };
                img.src = imagePath;
            }
            // 更新产品标题
            $('#product-title').text(productName)
                           .attr('data-text', productName);
            // 获取按钮数据
            loadProductButtons(productName, pathPrefix);
        }
    }

    // 抽取按钮渲染为独立函数
    function loadProductButtons(productName, pathPrefix) {
        const lang = isEnglishSite() ? 1 : 0;
        $.ajax({
            url: '/apis/download_detail_list/',
            type: 'GET',
            data: { 
                type: '产品详情按键',
                lang: lang,
                page: 1,
                size: 1000
            },
            dataType: 'json',
            success: function(btnResponse) {
                if(btnResponse && btnResponse.status === 'ok' && btnResponse.data && btnResponse.data.length > 0) {
                    var buttons = btnResponse.data
                        .filter(function(item) {
                            return (item.show === true || item.show === 1) && 
                                   item.title && item.title.includes(productName);
                        })
                        .sort(function(a, b) {
                            return (a.display_order || 100) - (b.display_order || 100);
                        });
                    var $buttonContainer = $('#product-buttons');
                    $buttonContainer.empty();
                    buttons.slice(0, 2).forEach(function(btn) {
                        var btnUrl = normalizeUrl(btn.url || '#');
                        var btnText = btn.content || btn.title || (lang === 1 ? 'Button' : '按钮');
                        var $btn = $('<a></a>')
                            .addClass('btn-product')
                            .attr('href', btnUrl)
                            .text(btnText);
                        $buttonContainer.append($btn);
                    });
                }
            }
        });
    }

    // 修改loadDownloadItems函数
    function loadDownloadItems(type, container) {
        console.log(`开始加载${type}部分...`);
        const isEnglish = isEnglishSite();
        const lang = isEnglish ? 1 : 0;
        const productName = getProductNameFromTitle();
        
        // 定义中英文产品名映射关系
        const productNameMap = {
            'RK3399Pro core board': 'RK3399Pro核心板',
            'RK3568 Data Acquisition Gateway': 'RK3568数据采集网关',
            // 可以根据需要添加更多映射
        };

        // 获取对应的中文产品名
        const chineseProductName = productNameMap[productName] || productName;
        
        // 获取对应的类型名称（英文或中文）
        const typeMap = {
            '工具': { en: 'Tools', zh: '工具' },
            '资源': { en: 'Resources', zh: '资源' },
            '源代码': { en: 'Source Code', zh: '源代码' },
            '固件': { en: 'Firmware', zh: '固件' },
            '文件系统': { en: 'File System', zh: '文件系统' }
        };
        
        // 确定当前类型的英文和中文名称
        let currentType = type;
        let englishType = '';
        let chineseType = '';
        
        for (let key in typeMap) {
            if (type === key || type === typeMap[key].en) {
                currentType = isEnglish ? typeMap[key].en : key;
                englishType = typeMap[key].en;
                chineseType = key;
                break;
            }
        }

        // 英文界面：先尝试英文类型，如果找不到再尝试中文类型
        console.log('当前产品名:', isEnglish ? productName : chineseProductName);
        console.log('当前语言:', isEnglish ? '英文' : '中文');
        
        $.ajax({
            url: '/apis/download_detail_list/',
            type: 'GET',
            data: { 
                lang: lang,
                page: 1,
                size: 100
            },
            dataType: 'json',
            success: function(response) {
                console.log(`${type}数据响应:`, response);
                
                if (response && response.status === 'ok' && response.data && response.data.length > 0) {
                    // 英文界面：先尝试英文类型
                    var items = [];
                    if (isEnglish) {
                        // 先尝试英文类型
                        items = response.data.filter(function(item) {
                            return (item.show === true || item.show === 1) && 
                                   item.type === `${productName} ${englishType}` &&
                                   item.lang === lang;
                        });
                        
                        // 如果找不到英文类型的结果，尝试中文类型
                        if (items.length === 0) {
                            console.log('未找到英文类型数据，尝试使用中文类型搜索');
                            items = response.data.filter(function(item) {
                                return (item.show === true || item.show === 1) && 
                                       item.type === `${chineseProductName}${chineseType}` &&
                                       item.lang === lang;
                            });
                        }
                    } else {
                        // 中文界面：只使用中文类型搜索
                        items = response.data.filter(function(item) {
                            return (item.show === true || item.show === 1) && 
                                   item.type === `${chineseProductName}${chineseType}` &&
                                   item.lang === lang;
                        });
                    }

                    // 详细日志记录筛选后的项目
                    console.log('筛选出的项目IDs:', items.map(item => item.id));
                    
                    // 排序
                    items = items.sort(function(a, b) {
                        return (a.display_order || 100) - (b.display_order || 100);
                    });
                    
                    console.log(`筛选后的${type}数据:`, items);
                    console.log(`筛选后${type}数量:`, items.length);

                    if (items.length > 0) {
                        let html = '';
                        items.forEach(function(item) {
                            const title = item.title || '';
                            const content = item.content || '';
                            const code = item.code || '';
                            const version = item.version || '';
                            
                            // 确定显示在item-note中的文本内容，按优先级：提取码 > 版本号 > 内容
                            let noteText = '';
                            if (code) {
                                noteText = isEnglish ? `extracted code: ${code}` : `提取码: ${code}`;
                            } else if (version) {
                                noteText = `${getText('version')} ${version}`;
                            } else if (content) {
                                noteText = content;
                            }
                            
                            html += `
                                <div class="download-item">
                                    <div class="item-info">
                                        <div class="item-name">${title}</div>
                                        <div class="item-note">${noteText}</div>
                                    </div>
                                    <div class="download-action">
                                        <img src="${pathPrefix}images/home/<USER>" alt="${getText('download')}">
                                        <span>${getText('download')}</span>
                                    </div>
                                </div>
                            `;
                        });
                        container.find('.download-list').html(html);
                    } else {
                        console.log(`没有找到匹配的${type}数据`);
                        container.find('.download-list').html(`
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-note">${isEnglish ? `No ${englishType} data available` : `暂无${type}数据`}</div>
                                </div>
                            </div>
                        `);
                    }
                } else {
                    console.log(`${type}数据响应无效或为空`);
                    container.find('.download-list').html(`
                        <div class="download-item">
                            <div class="item-info">
                                <div class="item-note">${isEnglish ? `No ${englishType} data available` : `暂无${type}数据`}</div>
                            </div>
                        </div>
                    `);
                }
                
                // 更新加载状态
                switch(currentType) {
                    case '工具':
                    case 'Tools':
                        loadingStates.tools = true;
                        break;
                    case '资源':
                    case 'Resources':
                        loadingStates.resources = true;
                        break;
                    case '源代码':
                    case 'Source Code':
                        loadingStates.sourceCode = true;
                        break;
                    case '固件':
                    case 'Firmware':
                        loadingStates.firmware = true;
                        break;
                    case '文件系统':
                    case 'File System':
                        loadingStates.fileSystem = true;
                        break;
                }
                
                console.log(`${type}加载完成，当前加载状态:`, loadingStates);
                showContent();
            },
            error: function(xhr, status, error) {
                console.error(`加载${type}数据失败:`, {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                
                // 即使出错也标记为已加载，以避免永久加载状态
                switch(currentType) {
                    case '工具':
                    case 'Tools':
                        loadingStates.tools = true;
                        break;
                    case '资源':
                    case 'Resources':
                        loadingStates.resources = true;
                        break;
                    case '源代码':
                    case 'Source Code':
                        loadingStates.sourceCode = true;
                        break;
                    case '固件':
                    case 'Firmware':
                        loadingStates.firmware = true;
                        break;
                    case '文件系统':
                    case 'File System':
                        loadingStates.fileSystem = true;
                        break;
                }
                
                container.find('.download-list').html(`
                    <div class="download-item">
                        <div class="item-info">
                            <div class="item-note">${isEnglish ? `Failed to load ${englishType} data` : `加载${type}数据失败`}</div>
                        </div>
                    </div>
                `);
                console.log(`${type}加载失败，当前加载状态:`, loadingStates);
                showContent();
            }
        });
    }

    // 加载所有内容
    function initializeDownloadPage() {
        // 只处理不受downloads.js控制的元素
        if (!isControlledByDownloads('#all-sections')) {
            loadProductDisplay(); // 加载产品展示区，它会在加载完成后触发菜单加载
            
            // 加载所有分类的下载项
            const isEnglish = isEnglishSite();
            
            // 根据当前语言环境决定使用哪种选择器和类型名称
            if (isEnglish) {
                loadDownloadItems('Tools', $('.download-section[data-type="tools"]'));
                loadDownloadItems('Resources', $('.download-section[data-type="resources"]'));
                loadDownloadItems('Source Code', $('.download-section[data-type="source"]'));
                loadDownloadItems('Firmware', $('.download-section[data-type="firmware"]'));
                loadDownloadItems('File System', $('.download-section[data-type="filesystem"]'));
            } else {
                loadDownloadItems('工具', $('.download-section:contains("工具")'));
                loadDownloadItems('资源', $('.download-section:contains("资源")'));
                loadDownloadItems('源代码', $('.download-section:contains("源代码")'));
                loadDownloadItems('固件', $('.download-section:contains("固件")'));
                loadDownloadItems('文件系统', $('.download-section:contains("文件系统")'));
            }
        }
    }

    // 弹窗HTML结构
    var toolModalHtml = `
        <div class="modal fade" id="toolDownloadModal" tabindex="-1" role="dialog" aria-labelledby="toolDownloadModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content tool-modal-content">
                    <div class="modal-header tool-modal-header">
                        <span class="tool-modal-title">${getText('resourceDownloadDirectory')}</span>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body tool-modal-body">
                        <!-- 工具名、提取码、按钮区将动态填充 -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // 如果弹窗不存在，则添加到body
    if($('#toolDownloadModal').length === 0) {
        $('body').append(toolModalHtml);
    }

    // 移除modal-open类（解决弹窗关闭后可能出现的滚动问题）
    $(document).on('hidden.bs.modal', function(){
        $('body').removeClass('modal-open');
    });

    // 修改工具弹窗点击事件处理
    $(document).on('click', '.download-action', function(e) {
        // 如果元素由downloads.js控制，则跳过处理
        if (isControlledByDownloads(this)) {
            return;
        }
        e.preventDefault();
        const isEnglish = isEnglishSite();
        const lang = isEnglish ? 1 : 0;
        const productName = getProductNameFromTitle();
        const $item = $(this).closest('.download-item');
        const itemTitle = $item.find('.item-name').text();
        
        // 定义中英文产品名映射关系
        const productNameMap = {
            'RK3399Pro core board': 'RK3399Pro核心板',
            'RK3568 Data Acquisition Gateway': 'RK3568数据采集网关',
            // 可以根据需要添加更多映射
        };

        // 获取对应的中文产品名
        const chineseProductName = productNameMap[productName] || productName;
        
        // 确定当前点击项所属的类型
        const $section = $item.closest('.download-section');
        let itemType = '';
        let chineseType = '';
        if ($section.length > 0) {
            // 根据section的data-type属性来确定类型
            const sectionType = $section.data('type');
            switch(sectionType) {
                case 'tools':
                    itemType = isEnglish ? 'Tools' : '工具';
                    chineseType = '工具';
                    break;
                case 'resources':
                    itemType = isEnglish ? 'Resources' : '资源';
                    chineseType = '资源';
                    break;
                case 'source':
                    itemType = isEnglish ? 'Source Code' : '源代码';
                    chineseType = '源代码';
                    break;
                case 'firmware':
                    itemType = isEnglish ? 'Firmware' : '固件';
                    chineseType = '固件';
                    break;
                case 'filesystem':
                    itemType = isEnglish ? 'File System' : '文件系统';
                    chineseType = '文件系统';
                    break;
            }
        }
        
        // 构建链接类型（中英文）
        const englishLinkType = `${productName} ${itemType} Link`;
        const chineseLinkType = `${chineseProductName}${chineseType}链接`;
        
        console.log('点击项目:', itemTitle);
        console.log('项目类型:', itemType);
        console.log('查找链接类型:', isEnglish ? englishLinkType : chineseLinkType);
        console.log('当前语言:', isEnglish ? '英文' : '中文');
        
        // 查询下载链接数据
        $.ajax({
            url: '/apis/download_detail_list/',
            type: 'GET',
            data: { 
                lang: lang,
                page: 1,
                size: 100
            },
            dataType: 'json',
            success: function(response) {
                console.log('所有数据响应:', response);
                if(response && response.status === 'ok' && response.data && response.data.length > 0) {
                    // 筛选匹配的数据
                    var matched = [];
                    
                    if (isEnglish) {
                        // 英文界面：先尝试英文链接类型
                        matched = response.data.filter(function(item){
                            return (item.title||'') === itemTitle && 
                                   item.type === englishLinkType &&
                                   (item.show === true || item.show === 1) &&
                                   item.lang == lang;
                        });
                        
                        // 如果找不到，尝试中文链接类型
                        if (matched.length === 0) {
                            console.log('未找到英文链接类型数据，尝试使用中文链接类型搜索');
                            matched = response.data.filter(function(item){
                                return (item.title||'') === itemTitle && 
                                       item.type === chineseLinkType &&
                                       (item.show === true || item.show === 1) &&
                                       item.lang == lang;
                            });
                        }
                    } else {
                        // 中文界面：只使用中文链接类型
                        matched = response.data.filter(function(item){
                            return (item.title||'') === itemTitle && 
                                   item.type === chineseLinkType &&
                                   (item.show === true || item.show === 1) &&
                                   item.lang == lang;
                        });
                    }
                    
                    console.log('筛选后的下载链接:', matched);
                    
                    // 工具名
                    var itemNameHtml = `<div class="tool-modal-toolname">${itemTitle}</div>`;
                    // 分割线
                    var divider = '<hr class="tool-modal-divider">';
                    // 按钮区
                    var btnsHtml = '';
                    // 提取码区
                    var codeHtml = '';
                    
                    if(matched.length > 0) {
                        // 按display_order排序
                        matched.sort(function(a, b) {
                            const orderA = a.display_order !== undefined ? a.display_order : 1000 + a.id;
                            const orderB = b.display_order !== undefined ? b.display_order : 1000 + b.id;
                            return orderA - orderB;
                        });
                        
                        // 提取码（如果有）
                        var code = matched[0].code || '';
                        if(code) {
                            codeHtml = `<div class="tool-modal-code">${getText('extractCode')}<span>${code}</span></div>`;
                        }
                        
                        // 横向按钮区
                        btnsHtml = '<div class="tool-modal-btns">';
                        // 首先添加官方下载按钮（如果存在）
                        var officialDownload = matched.find(function(item) {
                            const isOfficialContent = isEnglish ? 
                                item.content === 'Official Download' || item.content === 'Official' :
                                item.content === '官方下载';
                            return isOfficialContent && item.url && item.url !== '#';
                        });
                        if(officialDownload) {
                            btnsHtml += `<a href="${normalizeUrl(officialDownload.url)}" target="_blank" class="download-btn">${officialDownload.content || getText('officialDownload')}</a>`;
                        }
                        // 添加其他下载按钮
                        matched.forEach(function(item) {
                            if(item !== officialDownload && item.url && item.url !== '#') {
                                const normalizedUrl = normalizeUrl(item.url);
                                btnsHtml += `<a href="${normalizedUrl}" target="_blank" class="tool-modal-btn tool-modal-btn-active">${item.content || getText('download')}</a>`;
                            }
                        });
                        btnsHtml += '</div>';
                        
                        $('.tool-modal-body').html(itemNameHtml + codeHtml + divider + btnsHtml);
                    } else {
                        console.log('没有找到匹配的下载链接');
                        $('.tool-modal-body').html(itemNameHtml + divider + `<div class="tool-modal-empty">${getText('noDownloadLinks')}</div>`);
                    }
                    $('#toolDownloadModal').modal('show');
                }
            },
            error: function(xhr, status, error) {
                console.error('加载数据失败:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
            }
        });
    });

    // ====== 本地缓存工具函数 ======
    function setCache(key, data, expireMinutes = 60) {
        const cacheObj = {
            data: data,
            expire: Date.now() + expireMinutes * 60 * 1000
        };
        localStorage.setItem(key, JSON.stringify(cacheObj));
    }

    function getCache(key) {
        const cacheStr = localStorage.getItem(key);
        if (!cacheStr) return null;
        try {
            const cacheObj = JSON.parse(cacheStr);
            if (Date.now() < cacheObj.expire) {
                return cacheObj.data;
            } else {
                localStorage.removeItem(key);
                return null;
            }
        } catch (e) {
            localStorage.removeItem(key);
            return null;
        }
    }

    // 清理语言切换相关的缓存
    function clearLanguageSwitchCache() {
        console.log('检查是否需要清理语言切换缓存...');

        // 获取当前语言标识
        const currentLangId = getLanguageId();
        const lastLangId = localStorage.getItem('download_last_language');

        console.log('当前语言:', currentLangId, '上次语言:', lastLangId);

        // 如果语言发生了变化，清理相关缓存
        if (lastLangId && lastLangId !== currentLangId) {
            console.log('检测到语言切换，清理缓存...');

            // 清理所有下载相关的缓存
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('download_')) {
                    keysToRemove.push(key);
                }
            }

            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                console.log('已清理缓存:', key);
            });

            console.log(`已清理 ${keysToRemove.length} 个缓存项`);
        }

        // 保存当前语言标识
        localStorage.setItem('download_last_language', currentLangId);
    }

    // 完全重写菜单加载和交互逻辑
    function loadNavMenu() {
        console.group('=== 加载导航菜单 ===');
        const lang = isEnglishSite() ? 1 : 0;
        const languageId = getLanguageId();
        const navCacheKey = `download_nav_${languageId}_${lang}`;
        // 判断是否是下载页面
        const isDownloadPage = window.location.pathname.endsWith('/download.html') || 
                             window.location.pathname.endsWith('/download_en.html');
        // 获取保存的状态
        const savedState = localStorage.getItem('downloadMenuState');
        let parsedState = null;
        try {
            if (savedState) {
                parsedState = JSON.parse(savedState);
                if (isDownloadPage) {
                    parsedState.parentMenu = isEnglishSite() ? 'Terminal' : '终端';
                }
            }
        } catch (e) {
            console.error('解析菜单状态出错:', e);
        }
        // 先查缓存
        const cachedNav = getCache(navCacheKey);
        if (cachedNav) {
            renderNavMenu(cachedNav, parsedState);
            console.groupEnd();
            return;
        }
        // 没有缓存才请求
        $.ajax({
            url: '/apis/download_nav_list/',
            type: 'GET',
            data: { lang: lang, page: 1, size: 1000 },
            dataType: 'json',
            success: function(response) {
                if(response && response.status === 'ok' && response.data) {
                    setCache(navCacheKey, response.data, 60); // 缓存1小时
                    renderNavMenu(response.data, parsedState);
                }
            },
            complete: function() {
                console.groupEnd();
            }
        });
    }
    
    // 抽取菜单渲染为独立函数，供缓存和接口共用
    function renderNavMenu(navData, parsedState) {
        // 按照显示顺序排序
        var navItems = navData.sort(function(a, b) {
            return (a.display_order || 100) - (b.display_order || 100);
        });
        // 过滤主菜单项和子菜单项
        var mainNavItems = navItems.filter(function(item) {
            return item.type === 'nav' && item.show;
        });
        var productItems = navItems.filter(function(item) {
            return item.type === 'product' && item.show;
        });
        $('.download-sidebar').empty();
        mainNavItems.forEach(function(item) {
            var groupId = 'main-group-' + item.id;
            const shouldExpand = parsedState && parsedState.parentMenu === item.title;
            if(item.content && item.content.includes('select')) {
                var selectHtml = `
                    <div class="sidebar-category">
                        <select class="category-select">
                            <option>${item.title || ''}</option>
                        </select>
                    </div>
                `;
                $('.download-sidebar').append(selectHtml);
            } else {
                var menuHtml = `
                    <div class="expandable-menu" id="${groupId}" data-id="${item.id}" data-title="${item.title}">
                        <div class="menu-title">
                            ${item.title || ''}
                            <img src="../images/home/<USER>" class="arrow-icon" alt="展开">
                        </div>
                        <div class="menu-divider" style="display: none;"></div>
                        <div class="menu-items" style="display: none;">
                            <!-- 子菜单项将动态填充 -->
                        </div>
                    </div>
                `;
                $('.download-sidebar').append(menuHtml);
                var childItems = productItems.filter(function(childItem) {
                    const childTitle = (childItem.title || '').toLowerCase().trim();
                    const parentTitle = (item.title || '').toLowerCase().trim();

                    console.log('匹配检查:', {
                        parentTitle: parentTitle,
                        childTitle: childTitle,
                        originalChildTitle: childItem.title
                    });

                    // 更灵活的匹配逻辑
                    const menuCategories = {
                        'core board': ['core board', 'core module', '核心板'],
                        'motherboard': ['motherboard', 'carrier board', 'mainboard', '主板', '载板'],
                        'terminal': ['terminal', 'box', '终端', '盒子'],
                        'aiot solution': ['aiot', 'solution', '解决方案'],
                        'openharmony': ['openharmony', 'open harmony'],
                        'mineharmony': ['mineharmony', 'mine harmony'],
                        '核心板': ['core board', 'core module', '核心板'],
                        '主板': ['motherboard', 'carrier board', 'mainboard', '主板', '载板'],
                        '终端': ['terminal', 'box', '终端', '盒子'],
                        'aiot解决方案': ['aiot', 'solution', '解决方案'],
                        'openharmony': ['openharmony', 'open harmony'],
                        'mineharmony': ['mineharmony', 'mine harmony']
                    };

                    const categoryKeywords = menuCategories[parentTitle] || [];

                    // 如果没有找到关键词，尝试直接匹配
                    if (categoryKeywords.length === 0) {
                        console.log('没有找到关键词，尝试直接匹配');
                        return false;
                    }

                    const isMatch = categoryKeywords.some(keyword => {
                        const keywordLower = keyword.toLowerCase();
                        // 检查子项标题是否包含关键词
                        const titleMatch = childTitle.includes(keywordLower);
                        console.log(`关键词 "${keyword}" 匹配结果:`, titleMatch);
                        return titleMatch;
                    });

                    console.log('最终匹配结果:', isMatch);
                    return isMatch;
                });
                var $menu = $(`#${groupId}`);
                if(childItems.length > 0) {
                    childItems.forEach(function(childItem) {
                        var childUrl = normalizeUrl(childItem.url || '');
                        var childItemHtml = `
                            <div class="menu-item" data-id="${childItem.id}" data-parent-menu="${item.title}">
                                <a href="${childUrl}">${childItem.content || ''}</a>
                            </div>
                        `;
                        $menu.find('.menu-items').append(childItemHtml);
                    });
                }
                if (shouldExpand) {
                    $menu.addClass('expanded');
                    $menu.find('.arrow-icon').attr('src', '../images/home/<USER>');
                    $menu.find('.menu-items').show();
                    $menu.find('.menu-divider').show();
                }
            }
        });
        // 绑定菜单点击事件
        bindMenuEvents();
        $('.menu-item a').off('click').on('click', function() {
            var $menuItem = $(this).closest('.menu-item');
            var parentMenu = $menuItem.data('parent-menu');
            var $menu = $menuItem.closest('.expandable-menu');
            var menuId = $menu.data('id');
            try {
                localStorage.setItem('downloadMenuState', JSON.stringify({
                    menuId: menuId,
                    submenuId: $menuItem.data('id'),
                    parentMenu: parentMenu,
                    timestamp: Date.now()
                }));
            } catch (e) {}
        });
        setTimeout(adjustLayout, 300);
        bindMenuEvents();
        setTimeout(function() {
            highlightCurrentMenu();
        }, 500);
    }

    // 添加中英文菜单标题映射
    const menuTitleMap = {
        '核心板': 'Core board',
        '主板': 'Motherboard',
        '终端': 'Terminal',
        'AIOT解决方案': 'AIOT solution',
        'OpenHarmony': 'OpenHarmony',
        'MineHarmony': 'MineHarmony'
    };

    // 添加映射查找函数
    function getMatchedMenuTitle(title) {
        const isEnglish = isEnglishSite();
        
        if (isEnglish) {
            // 如果是英文站点，先查找是否有对应的中文标题
            for (let [cn, en] of Object.entries(menuTitleMap)) {
                if (title === cn) {
                    return en;  // 返回英文标题
                }
            }
        } else {
            // 如果是中文站点，先查找是否有对应的英文标题
            for (let [cn, en] of Object.entries(menuTitleMap)) {
                if (title === en) {
                    return cn;  // 返回中文标题
                }
            }
        }
        
        // 如果没找到映射，返回原标题
        return title;
    }

    // 修改highlightCurrentMenu函数
    function highlightCurrentMenu() {
        console.group('=== 执行菜单高亮 ===');
        
        // 判断是否是下载页面
        const isDownloadPage = window.location.pathname.endsWith('/download.html') || 
                             window.location.pathname.endsWith('/download_en.html');
        
        // 获取保存的状态
        const savedState = MenuStateManager.getState();
        console.log('获取到的保存状态:', savedState);
        
        // 如果是下载页面，强制使用终端菜单
        if (isDownloadPage) {
            savedState.parentMenu = isEnglishSite() ? 'Terminal' : '终端';
        }
        
        // 获取当前产品名称
        const currentProductName = getProductNameFromTitle();
        console.log('当前页面产品名称:', currentProductName);
        
        // 首先关闭所有菜单
        $('.expandable-menu').removeClass('expanded')
            .find('.arrow-icon').attr('src', '../images/home/<USER>')
            .end()
            .find('.menu-items').hide()
            .end()
            .find('.menu-divider').hide();
        
        // 移除所有active状态
        $('.menu-item').removeClass('active');
        
        if (savedState && savedState.parentMenu) {
            console.log('尝试展开菜单:', savedState.parentMenu);
            
            // 获取当前语言环境下对应的菜单标题
            const targetMenuTitle = getMatchedMenuTitle(savedState.parentMenu);
            console.log('映射后的菜单标题:', {
                原标题: savedState.parentMenu,
                映射后: targetMenuTitle,
                当前语言: isEnglishSite() ? '英文' : '中文'
            });
            
            // 查找对应的菜单
            const $targetMenu = $('.expandable-menu').filter(function() {
                const menuTitle = $(this).find('.menu-title').text().trim();
                const matches = menuTitle === targetMenuTitle;
                console.log('比较菜单:', menuTitle, '是否匹配:', matches);
                return matches;
            });
            
            if ($targetMenu.length > 0) {
                console.log('找到要展开的菜单:', targetMenuTitle);
                
                // 在展开菜单前先计算要高亮的项
                let $highlightItem = null;
                
                // 1. 先检查保存的submenuId
                if (savedState.submenuId) {
                    $highlightItem = $targetMenu.find(`.menu-item[data-id="${savedState.submenuId}"]`);
                    if ($highlightItem.length > 0) {
                        console.log('找到保存的高亮项:', savedState.submenuId);
                    }
                }
                
                // 2. 如果没找到保存的submenuId，立即根据产品名称查找
                if (!$highlightItem || !$highlightItem.length) {
                    const productNameLower = currentProductName.toLowerCase();
                    const $menuItems = $targetMenu.find('.menu-items .menu-item');
                    
                    $menuItems.each(function() {
                        const $item = $(this);
                        const itemText = $item.find('a').text().trim().toLowerCase();
                        
                        if (itemText.includes(productNameLower) || 
                            productNameLower.includes(itemText)) {
                            $highlightItem = $item;
                            
                            // 预先保存匹配状态
                            try {
                                const menuId = $targetMenu.data('id');
                                const submenuId = $item.data('id');
                                const parentMenu = $item.data('parent-menu');
                                
                                if (menuId && submenuId && parentMenu) {
                                    MenuStateManager.saveMenuState(menuId, submenuId, parentMenu);
                                    console.log('保存新的菜单状态:', { menuId, submenuId, parentMenu });
                                }
                            } catch (e) {
                                console.error('保存菜单状态出错:', e);
                            }
                            
                            return false; // 找到匹配项后停止循环
                        }
                    });
                }
                
                // 同步执行展开和高亮
                $targetMenu
                    .addClass('expanded')
                    .find('.arrow-icon').attr('src', '../images/home/<USER>')
                    .end()
                    .find('.menu-items, .menu-divider').show();
                
                // 如果找到了要高亮的项，立即高亮
                if ($highlightItem && $highlightItem.length) {
                    $highlightItem.addClass('active');
                }
                
                // 立即调整布局
                adjustLayout();
            } else {
                console.log('未找到要展开的菜单:', targetMenuTitle);
            }
        } else {
            console.log('没有保存的菜单状态或parentMenu');
        }
        
        console.groupEnd();
        
        // 调整布局
        setTimeout(adjustLayout, 300);
    }
    
    // 修改调整页面布局函数
    function adjustLayout() {
        console.group('=== 动态调整页面布局 ===');
        
        // 获取最后一个可见的菜单项
        var $lastVisibleMenuItem = $('.menu-items:visible').last();
        var lastMenuBottom = 0;
        
        // 获取侧边栏的底部位置（包括未展开的菜单）
        var $sidebar = $('.download-sidebar');
        var sidebarBottom = $sidebar.offset().top + $sidebar.outerHeight();
        
        // 确定实际的底部位置（展开菜单或侧边栏的最大值）
        if ($lastVisibleMenuItem.length > 0) {
            lastMenuBottom = $lastVisibleMenuItem.offset().top + $lastVisibleMenuItem.outerHeight();
            lastMenuBottom = Math.max(lastMenuBottom, sidebarBottom);
        } else {
            lastMenuBottom = sidebarBottom;
        }
        console.log('最后元素底部位置:', lastMenuBottom);
        
        // 获取页脚位置和高度
        var $footer = $('footer');
        var footerTop = 0;
        var footerHeight = 0;

        // 检查页脚是否存在且已初始化
        if ($footer.length > 0 && $footer.offset()) {
            footerTop = $footer.offset().top;
            footerHeight = $footer.outerHeight();
            console.log('页脚位置:', footerTop, '页脚高度:', footerHeight);
        } else {
            // 页脚还没有完全加载，使用默认值
            console.log('页脚还未完全加载，使用默认值');
            footerTop = $(document).height(); // 使用文档高度作为默认页脚位置
            footerHeight = 540; // 使用CSS中定义的默认页脚高度
        }
        
        // 设置安全距离
        var safetyMargin = 120;
        
        // 获取当前文档高度和视窗高度
        var docHeight = $(document).height();
        var viewportHeight = $(window).height();
        console.log('文档高度:', docHeight, '视窗高度:', viewportHeight);
        
        // 计算当前与页脚的实际距离
        var currentSpaceToFooter = footerTop - lastMenuBottom;
        console.log('当前与页脚的距离:', currentSpaceToFooter);

        // 获取当前主容器高度
        var $main = $('.download-main');
        var currentMainHeight = $main.outerHeight();
        var neededHeight = currentMainHeight;

        // 只有在页脚完全加载时才进行距离调整
        if ($footer.length > 0 && $footer.offset()) {
            // 根据与页脚的距离调整高度
            if (currentSpaceToFooter < safetyMargin) {
                // 距离太小，需要增加高度
                var additionalHeight = safetyMargin - currentSpaceToFooter;
                neededHeight = currentMainHeight + additionalHeight;
                console.log('需要增加高度:', additionalHeight);
            } else if (currentSpaceToFooter > safetyMargin) {
                // 距离太大，需要减少高度
                var excessHeight = currentSpaceToFooter - safetyMargin;
                neededHeight = currentMainHeight - excessHeight;
                console.log('需要减少高度:', excessHeight);
            }
        } else {
            console.log('页脚未完全加载，跳过距离调整');
        }
        
        // 确保内容区域至少填满视窗高度（减去页脚高度和安全边距）
        var minContentHeight = viewportHeight - footerHeight - safetyMargin;
        neededHeight = Math.max(neededHeight, minContentHeight);
        
        // 确保高度不小于侧边栏高度
        var sidebarHeight = $sidebar.outerHeight();
        neededHeight = Math.max(neededHeight, sidebarHeight);
        
        console.log('计算得到的所需高度:', neededHeight, '当前高度:', currentMainHeight);
        
        // 应用新高度（只有在高度确实需要改变时才应用）
        if (Math.abs(neededHeight - currentMainHeight) > 1) {
            $main.css({
                'min-height': neededHeight + 'px',
                'margin-bottom': safetyMargin + 'px'
            });
            console.log('应用新高度:', neededHeight);
        } else {
            console.log('高度变化不大，保持当前高度');
        }
        
        console.groupEnd();
    }
    
    // 调用加载导航菜单
    loadNavMenu();
    
    // 页面加载和窗口调整时重新计算布局
    $(window).on('load resize', adjustLayout);

    // 动态分栏布局函数
    function balanceColumns() {
        var sections = $('#all-sections .download-section');
        var leftColumn = $('#left-column');
        var rightColumn = $('#right-column');
        
        // 清空两列
        leftColumn.empty();
        rightColumn.empty();
        
        var totalHeight = 0;
        var sectionHeights = [];
        
        // 计算每个section的高度
        sections.each(function() {
            var $section = $(this).clone();
            leftColumn.append($section);
            var sectionHeight = $section.outerHeight(true);
            totalHeight += sectionHeight;
            sectionHeights.push({
                element: $(this),
                height: sectionHeight
            });
        });
        
        // 清空左列，准备重新分配
        leftColumn.empty();
        
        // 目标高度是总高度的一半
        var targetHeight = totalHeight / 2;
        var currentHeight = 0;
        var splitIndex = -1;
        
        // 找到最接近一半高度的分割点
        for (var i = 0; i < sectionHeights.length; i++) {
            currentHeight += sectionHeights[i].height;
            if (currentHeight >= targetHeight) {
                // 检查是否这个位置更接近目标高度
                var diffCurrent = Math.abs(currentHeight - targetHeight);
                var diffPrevious = Math.abs(currentHeight - sectionHeights[i].height - targetHeight);
                
                splitIndex = diffCurrent < diffPrevious ? i : i - 1;
                break;
            }
        }
        
        // 分配内容到两列
        sections.each(function(index) {
            var $section = $(this).clone();
            if (index <= splitIndex) {
                leftColumn.append($section);
            } else {
                rightColumn.append($section);
            }
        });
    }
    
    // 页面加载完成后执行分栏
    $(window).on('load', function() {
        balanceColumns();
    });
    
    // 窗口大小改变时重新计算
    var resizeTimer;
    $(window).on('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            balanceColumns();
        }, 250); // 添加防抖，避免频繁计算
    });

    // 确保在菜单加载完成后执行高亮
    $(document).on('menuLoaded', function() {
        console.log('=== 菜单加载完成，执行高亮 ===');
        highlightCurrentMenu();
    });

    // 初始化页面
    initializeDownloadPage();
    // loadToolsSection(); // 移除这个调用，因为它现在已经包含在 initializeDownloadPage 中

    // 在页面卸载时也记录状态
    $(window).on('beforeunload', function() {
        console.group('页面卸载时的菜单状态');
        MenuDebugger.logState();
        console.groupEnd();
    });

    // 强制刷新缓存的处理函数
    function handleForceRefresh() {
        console.log('[download.js] 收到后台强制刷新缓存信号，开始清除缓存...');
        // 清除所有download_相关缓存
        Object.keys(localStorage).forEach(function(key) {
            if (key.startsWith('download_')) {
                console.log('[download.js] 清除缓存key:', key);
                localStorage.removeItem(key);
            }
        });
        console.log('[download.js] 缓存清除完毕，重新获取数据...');

        // 强制重新加载所有数据，绕过控制检查
        console.log('[download.js] 开始强制重新加载所有数据...');

        // 重新加载产品展示区
        loadProductDisplay();

        // 重新加载所有分类的下载项
        const isEnglish = isEnglishSite();
        console.log('[download.js] 当前语言环境:', isEnglish ? '英文' : '中文');

        if (isEnglish) {
            loadDownloadItems('Tools', $('.download-section[data-type="tools"]'));
            loadDownloadItems('Resources', $('.download-section[data-type="resources"]'));
            loadDownloadItems('Source Code', $('.download-section[data-type="source"]'));
            loadDownloadItems('Firmware', $('.download-section[data-type="firmware"]'));
            loadDownloadItems('File System', $('.download-section[data-type="filesystem"]'));
        } else {
            loadDownloadItems('工具', $('.download-section:contains("工具")'));
            loadDownloadItems('资源', $('.download-section:contains("资源")'));
            loadDownloadItems('源代码', $('.download-section:contains("源代码")'));
            loadDownloadItems('固件', $('.download-section:contains("固件")'));
            loadDownloadItems('文件系统', $('.download-section:contains("文件系统")'));
        }

        console.log('[download.js] 强制重新加载完成');
    }

    // 定期检查数据库中的刷新信号
    // 从localStorage读取上次保存的字段值，如果没有则使用初始值
    let localRefreshContent = localStorage.getItem('local_refresh_content') || 'INIT_VALUE';
    console.log('[download.js] 启动缓存刷新检查机制');
    console.log('[download.js] 从localStorage读取本地字段:', localRefreshContent);

    setInterval(function() {
        console.log('[download.js] 开始检查后台刷新信号...');
        $.ajax({
            url: '/apis/download_detail_list/',
            type: 'GET',
            data: {
                type: 'CACHE_REFRESH_SIGNAL',
                lang: 0,
                page: 1,
                size: 10
            },
            dataType: 'json',
            success: function(response) {
                console.log('[download.js] 后台API响应:', response);

                // 在结果中查找固定标题的记录
                var refreshRecord = null;
                if (response && response.status === 'ok' && response.data && response.data.length > 0) {
                    refreshRecord = response.data.find(item => item.title === 'cache_refresh_signal');
                }

                if (refreshRecord) {
                    var backendContent = refreshRecord.content || '';
                    console.log('=== 字段对比开始 ===');
                    console.log('[download.js] 获取后台字段 backendContent:', backendContent);
                    console.log('[download.js] 当前本地字段 localRefreshContent:', localRefreshContent);
                    console.log('[download.js] 比较结果 backendContent !== localRefreshContent:', backendContent !== localRefreshContent);

                    // 比较本地字段与后台字段
                    if (backendContent !== localRefreshContent) {
                        console.log('[download.js] ✅ 字段不一致，执行刷新操作');
                        console.log('[download.js] 准备更新本地字段...');

                        // 更新本地字段为后台字段
                        localRefreshContent = backendContent;
                        // 保存到localStorage，确保页面刷新后也能保持状态
                        localStorage.setItem('local_refresh_content', localRefreshContent);
                        console.log('[download.js] ✅ 本地字段已更新为:', localRefreshContent);
                        console.log('[download.js] ✅ 已保存到localStorage');

                        // 触发缓存刷新
                        console.log('[download.js] 🔄 触发缓存刷新（重新获取数据）');
                        handleForceRefresh();  // 清除缓存并重新获取数据
                    } else {
                        console.log('[download.js] ❌ 字段一致，跳过刷新');
                    }
                    console.log('=== 字段对比结束 ===');
                } else {
                    console.log('[download.js] ❌ 没有找到刷新信号记录');
                }
            },
            error: function(xhr, status, error) {
                console.log('[download.js] ❌ API请求失败:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
            }
        });
    }, 5000); // 每5秒检查一次数据库

});