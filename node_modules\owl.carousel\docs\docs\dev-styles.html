<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Owl Carousel Documentation">
    <meta name="author" content="<PERSON>">
    <title>
      Sass Styles | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li> <a href="/OwlCarousel2/demos/demos.html">Demos</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a> 
              </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>Development</h1>
        </div>
      </div>
    </section>
    <div id="docs">
      <div class="row">
        <div class="small-12 medium-3 large-3 columns">
          <ul class="side-nav">
            <li class="side-nav-head">Getting Started</li>
            <li> <a href="started-welcome.html">Welcome</a>  </li>
            <li> <a href="started-installation.html">Installation</a>  </li>
            <li> <a href="started-faq.html">FAQ</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">API</li>
            <li> <a href="api-options.html">Options</a>  </li>
            <li> <a href="api-classes.html">Classes</a>  </li>
            <li> <a href="api-events.html">Events</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Development</li>
            <li> <a href="dev-buildin-plugins.html">Built-in Plugins</a>  </li>
            <li> <a href="dev-plugin-api.html">Plugin API</a>  </li>
            <li> <a href="dev-styles.html">Sass Styles</a>  </li>
            <li> <a href="dev-external.html">External Libs</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Support</li>
            <li> <a href="support-contributing.html">Contributing</a>  </li>
            <li> <a href="support-changelog.html">Changelog</a>  </li>
            <li> <a href="support-contact.html">Contact</a>  </li>
          </ul>
        </div>
        <div class="small-12 medium-9 large-9 columns">
          <article class="docs-content">
            <h2 id="using-sass">Using Sass</h2>
            <blockquote>
              <p>Owl uses the Sass pre-processor to build CSS for all main modules and themes. If you don’t know Sass, have a look at their
                <a href="http://sass-lang.com/">website</a>  and you’ll love it. Owl uses a faster adaptation of Sass written in C,
                <a href="http://libsass.org/">libsass</a>  (via
                <a href="https://github.com/sindresorhus/grunt-sass">grunt-sass</a> ), that doesn&#39;t require a Ruby dependency for our build process.</p>
            </blockquote>
            <p>To build the CSS from its Sass source, it’s required to have:</p>
            <ul>
              <li> <a href="https://nodejs.org/">Node.js</a>  </li>
              <li> <a href="http://gruntjs.com/">Grunt</a>  </li>
            </ul>
            <p>Check this
              <a href="https://benfrain.com/lightning-fast-sass-compiling-with-libsass-node-sass-and-grunt-sass/">tutorial</a>  to learn how to use Sass and libsass in Grunt environment.</p>
            <h3 id="scss-files-included">SCSS Files included</h3>
            <p>Source files can be found on
              <a href="https://github.com/OwlCarousel2/OwlCarousel2">Github Project</a> 
            </p>
            <pre><code>src/
└── scss/
    ├── _mixins.scss
    ├── _theme.scss
    ├── owl.carousel.scss
    ├── owl.animate.scss
    ├── owl.autoheight.scss
    ├── owl.lazyload.scss
    ├── owl.video.scss
    ├── owl.theme.default.scss
    └── owl.theme.green.scss</code></pre>
            <h3 id="_mixins-scss">_mixins.scss</h3>
            <p>_mixins contain basic snippets generators for CSS3 cross-browser styles.</p>
            <h3 id="_theme-scss">_theme.scss</h3>
            <p>Scss structure for theme. Use owl.carousel.default.scss to change variables and generate new styles.</p>
            <h3 id="owl-carousel-scss">owl.carousel.scss</h3>
            <p>Core file to handle basic styles and override some unnecessary browsers behaviors. You shouldn’t change this file unless you have to.</p>
            <h3 id="owl-pluginname-scss">owl.[pluginname].scss</h3>
            <p>Styles for modules.</p>
            <h3 id="owl-theme-scss">owl.theme.*.scss</h3>
            <p>Theme files for dots and navigations buttons. Use <code>owl.theme.default.scss</code> to upgrade to your own styles or create a new theme.</p>
          </article>
        </div>
      </div>
    </div>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>