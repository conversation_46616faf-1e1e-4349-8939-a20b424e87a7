<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>厦门贝启科技有限公司-Bearkey-官网</title>
    <meta name="keywords" Content="厦门贝启科技,贝启科技,Bearkey官网,贝启云,智能会议系统,无线投屏,人工智能,工控主板,人脸识别,智慧视觉开发,智能音频模块,物联网,开源主板,智能故事机,AIoT,音视频处理,RK3399Pro,RK1808,边缘计算">
    <link rel="icon" href="images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="厦门贝启科技,贝启科技,Bearkey官网,贝启云,智能会议系统,无线投屏,人工智能,工控主板,人脸识别,智慧视觉开发,智能音频模块,物联网,开源主板,智能故事机,AIoT,音视频处理,RK3399Pro,RK1808,边缘计算"/>
    <meta name="author" content=""/>
    <!-- css -->
    <link href="css/bootstrap.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="simple-line-icons/css/simple-line-icons.css">
    <link href="css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="css/flexslider.css" rel="stylesheet"/>
    <link href="css/style.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 - 用于导航栏图标 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!--<link href="css/wiki.css" rel="stylesheet"/>-->
    <style>
        td {
            text-align: left;
        }

        /*p {*/
        /*    text-align: left;*/
        /*}*/

        .table tbody tr td {
            vertical-align: middle;
        }

        .item_wiki{
            background-color: white;
            /*min-height: 100px;*/
            width: 100%;
            border-radius: 5px;
            display:inline-block;
            /*padding-bottom: 20px;*/
            border:1px solid #e7e7e7;
            margin-bottom: 20px;
        }
        .item_wiki:hover{
            /*background-color: white;*/
            border:1px solid #b3d8ff;
        }
        
        /* 调试信息样式 */
        #debug_info {
            background-color: rgba(255,255,255,0.8);
            padding: 10px;
            font-size: 12px;
            color: #333;
            display: none;
            position: absolute;
            top: 100px;
            right: 10px;
            max-width: 300px;
            z-index: 1000;
            border: 1px solid #ddd;
        }
    </style>
    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body>
<div id="wrapper">
    <!-- start header -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header>
    <section id="inner-headline">
    </section>
    <div id="wiki_banner">
        <!-- 这里的图片会被从后台获取的wiki_banner内容替换 -->
        <img src="wiki/blank.png" style="width: 100%" id="wiki_banner_img">
                </div>
    <!-- 添加调试信息，在生产环境可以移除 -->
    <div id="debug_info">
        <h4>调试信息</h4>
        <div id="debug_content"></div>
        </div>
    <section id="content">
        <div class="container" id="wiki_content">
            <!--<div>-->
                <!--<div style="background-color: black;width: 100%;padding: 5px;border-radius: 5px;margin-bottom: 20px" > <h3 style="color: white;margin-left: 10px">喝西北风</h3> </div>-->
                <!--<div class="row">-->
                    <!--<div class="col-md-4 text-cente" onclick="javascript:location.href='www.baidu.com'">-->
                        <!--<div class="item_wiki ">-->
                            <!--<div style="margin: 10px" class="text-center">-->
                                <!--<img class="img-responsive" src="images/mall/m2.jpg" style="max-width: 80%;margin-top: 30px" >-->
                                <!--<div style="margin-top: 20px;"><h3 style="font-weight: normal">3399</h3></div>-->
                                <!--<div style="margin-top: 20px;"><h5 style="font-weight: normal">高性能核心板</h5></div>-->
                            <!--</div>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</div>-->

        </div>
    </section>
    <section class="content">
        <div class="container  text-center" id="desc">
        </div>
    </section>
    <footer>
        <!-- 页脚内容将通过footer.js动态加载 -->
    </footer>
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>
<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="js/jquery.js"></script>
<script src="js/jquery.cookie.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.fancybox.pack.js"></script>
<script src="js/jquery.fancybox-media.js"></script>
<script src="js/portfolio/jquery.quicksand.js"></script>
<script src="js/portfolio/setting.js"></script>
<script src="js/jquery.flexslider.js"></script>
<script src="js/animate.js"></script>
<script src="js/custom.js"></script>
<script src="js/tools.js"></script>
<script src="js/jquery.base64.js"></script>
<script src="js/main.js"></script>
<script src="js/nav.js"></script>
<script src="js/footer.js"></script>
<script type="text/javascript">
    // 获取wiki_banner图片
    function loadWikiBanner() {
        // 判断当前是否为英文网站
        const isEnglishSite = window.location.pathname.includes('_en.html');
        const lang = isEnglishSite ? 1 : 0;
        
        console.log('当前网站类型:', isEnglishSite ? '英文' : '中文', 'lang=', lang);
        
        // 构建请求参数
        const params = new URLSearchParams();
        params.append('page', '1');
        params.append('page_size', '10');
        params.append('filters', JSON.stringify({
            lang: lang,
            info_type: 'wiki_banner',
            show: true
        }));
        
        const apiUrl = '/apis/company_info/list?' + params.toString();
        console.log('正在请求API:', apiUrl);
        
        // 从后台获取wiki_banner数据
        $.ajax({
            url: apiUrl,
            type: 'GET',
            dataType: 'json',
            success: function(result) {
                console.log('API返回数据:', result);
                
                if (result.status === 'ok' && result.data && result.data.length > 0) {
                    // 找到对应的banner数据
                    var bannerData = result.data[0];
                    console.log('找到Banner数据:', bannerData);
                    if (bannerData.image_path) {
                        // 处理图片路径
                        var imgSrc = bannerData.image_path;
                        console.log('原始图片路径:', imgSrc);
                        
                        // 处理各种可能的路径格式
                        if (imgSrc.includes('C:')) {
                            // 将Windows路径转换为Web路径
                            var fileName = imgSrc.split('\\').pop().split('/').pop();
                            imgSrc = '/uploads/' + fileName;
                            console.log('Windows路径转换后:', imgSrc);
                        } else if (imgSrc.startsWith('/admin/uploads/')) {
                            // 将管理后台路径转换为前端路径
                            imgSrc = imgSrc.replace('/admin/uploads/', '/uploads/');
                            console.log('管理后台路径转换后:', imgSrc);
                        } else if (imgSrc.startsWith('/uploads/')) {
                            // 已经是正确的路径格式，不需要修改
                            console.log('已经是正确的路径格式:', imgSrc);
                        } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
                            // 相对路径转换为绝对路径
                            imgSrc = '/' + imgSrc;
                            console.log('相对路径转换后:', imgSrc);
                        }
                        
                        console.log('最终Banner图片路径:', imgSrc);
                        
                        // 更新banner图片并添加加载事件
                        $('#wiki_banner_img').attr('src', imgSrc)
                            .on('load', function() {
                                console.log('Wiki Banner图片加载成功');
                                
                                // 检查图片是否真的加载成功
                                checkImageExists(imgSrc, function(exists) {
                                    if (exists) {
                                        console.log('图片验证成功: ' + imgSrc);
                                    } else {
                                        console.log('图片验证失败，但浏览器未触发onerror事件');
                                    }
                                });
                            })
                            .on('error', function() {
                                console.error('Wiki Banner图片加载失败，使用默认图片');
                                $(this).attr('src', 'wiki/blank.png');
                                
                                // 尝试直接访问图片URL，看看是否真的不存在
                                checkImageExists(imgSrc, function(exists) {
                                    if (exists) {
                                        console.log('图片实际存在，但加载失败，可能是跨域问题');
                                    } else {
                                        console.log('图片确实不存在: ' + imgSrc);
                                    }
                                });
                            });
                    } else {
                        // 如果没有图片，使用默认图片
                        $('#wiki_banner_img').attr('src', 'wiki/blank.png');
                    }
                } else {
                    // 如果没有数据，使用默认图片
                    $('#wiki_banner_img').attr('src', 'wiki/blank.png');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取Banner图片失败:', error);
                console.error('状态码:', xhr.status);
                console.error('响应文本:', xhr.responseText);
                // 出错时使用默认图片
                $('#wiki_banner_img').attr('src', 'wiki/blank.png');
            }
        });
    }

    // 获取当前语言
    function getCurrentLang() {
        // 从cookie或其他存储中获取当前语言设置
        var lang = $.cookie('site_lang');
        return lang === 'en' ? 1 : 0; // 0表示中文，1表示英文
    }

    function init() {
        // 判断当前是否为英文网站
        const isEnglishSite = window.location.pathname.includes('_en.html');
        const lang = isEnglishSite ? 1 : 0;
        
        console.log('当前网站类型:', isEnglishSite ? '英文' : '中文', 'lang=', lang);
        
        // 构建请求参数
        const params = new URLSearchParams();
        params.append('page', '1');
        params.append('page_size', '50'); // 获取足够多的数据
        params.append('filters', JSON.stringify({
            lang: lang,
            info_type: 'wiki_detail',
            show: true
        }));
        
        const apiUrl = '/apis/company_info/list?' + params.toString();
        console.log('正在请求Wiki详情API:', apiUrl);
        
        // 从后台获取wiki_detail数据
        $.ajax({
            url: apiUrl,
            type: 'GET',
            dataType: 'json',
            success: function(result) {
                console.log('Wiki详情API返回数据:', result);
                
                if (result.status === 'ok' && result.data && result.data.length > 0) {
                    // 处理返回的数据
                    const wikiItems = result.data;
                    console.log('找到Wiki详情数据:', wikiItems.length, '条');
                    
                    // 按类别分组
                    const wikiGroups = {};
                    wikiItems.forEach(item => {
                        // 使用item.category作为分组键，如果没有则使用"其他"
                        const category = item.category || '其他';
                        if (!wikiGroups[category]) {
                            wikiGroups[category] = [];
                        }
                        wikiGroups[category].push(item);
                    });
                    
                    // 对每个分组内的项目按display_order排序
                    Object.keys(wikiGroups).forEach(category => {
                        wikiGroups[category].sort((a, b) => {
                            // 数字越小越靠前
                            return (a.display_order || 100) - (b.display_order || 100);
                        });
                    });
                    
                    // 生成HTML
                    let wikiStr = '';
                    Object.keys(wikiGroups).forEach(category => {
                        const items = wikiGroups[category];
                        
                        wikiStr += '<div>';
                        // 如果需要显示类别标题，可以取消下面的注释
                        // wikiStr += '<div style="background-color: black;width: 100%;padding: 5px;border-radius: 5px;margin-bottom: 20px" > <h3 style="color: white;margin-left: 10px">'+ category +'</h3> </div>';
                        
                        wikiStr += '<div class="row">';
                        items.forEach(item => {
                            // 处理图片路径
                            let imgSrc = item.image_path || '';
                            if (imgSrc) {
                                if (imgSrc.includes('C:')) {
                                    const fileName = imgSrc.split('\\').pop().split('/').pop();
                                    imgSrc = '/uploads/' + fileName;
                                } else if (imgSrc.startsWith('/admin/uploads/')) {
                                    imgSrc = imgSrc.replace('/admin/uploads/', '/uploads/');
                                } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
                                    imgSrc = '/' + imgSrc;
                                }
                            } else {
                                imgSrc = 'wiki/blank.png'; // 默认图片
                            }
                            
                            // 直接使用数据库中的URL作为跳转链接
                            let fullLink = item.url || '#';
                            console.log('数据库中的URL:', fullLink, '项目ID:', item.id, '标题:', item.title);
                            
                            // 如果URL为空或为#，则构建默认链接
                            if (fullLink === '#' || fullLink === '') {
                                // 处理链接
                                let link = item.link || '#';
                                console.log('使用link字段:', link);
                                
                                // 如果链接是以#开头或为空，使用默认链接
                                if (link === '#' || link === '') {
                                    link = '/wiki/TB-96AI/';
                                    console.log('使用默认链接:', link);
                                } 
                                // 如果链接不是以http开头，且不是以/开头，则添加/
                                else if (!link.startsWith('http') && !link.startsWith('/')) {
                                    link = '/' + link;
                                }
                                
                                // 确保链接末尾有斜杠，这对base64编码后的链接解析很重要
                                if (!link.endsWith('/') && !link.includes('?')) {
                                    link = link + '/';
                                }
                                
                                console.log('处理后的链接:', link);
                                // 使用调试函数进行编码，并显示详细信息
                                const encodedLink = debugBase64(link);
                                console.log('编码后的链接:', encodedLink);
                                
                                // 构建完整的链接URL
                                if (item.ifid) {
                                    // 如果有ifid参数，添加到链接中
                                    fullLink = 'wiki_detail.html?_r='+ encodedLink + '&ifid=' + item.ifid;
                                } else {
                                    fullLink = 'wiki_detail.html?_r='+ encodedLink;
                                }
                            }
                            
                            console.log('最终使用的链接:', fullLink);
                            
                            wikiStr += '<div class="col-md-4 text-cente" onclick="javascript:location.href=\''+ fullLink +'\'" style="cursor: pointer">\n' +
                                '                        <div class="item_wiki ">\n' +
                                '                            <div style="margin: 10px" class="text-center">\n' +
                                '                                <img class="img-responsive" src="'+ imgSrc +'" style="max-width: 80%;" >\n' +
                                '                                <div style="margin-top: 20px;"><h3 style="font-weight: normal">'+ (item.title || '') +'</h3></div>\n' +
                                '                                <div><h5 style="font-weight: normal">'+ (item.content || '') +'</h5></div>\n' +
                                '                            </div>\n' +
                                '                        </div>\n' +
                                '                    </div>        ';
                        });
                        wikiStr += '</div>'; // end row
                        wikiStr += '</div>';
                    });
                    
                    $('#wiki_content').html(wikiStr);
                } else {
                    console.log('未找到Wiki详情数据，尝试使用本地配置文件');
                    // 如果后台没有数据，尝试使用本地配置文件
                    try {
                        var wiki_conf = get_local_file_info('static/conf/wiki.json');
        if (typeof wiki_conf !== 'object') wiki_conf = JSON.parse(wiki_conf);
        var all_wiki = wiki_conf.all;
                        var wiki_str = '';

        $.each(all_wiki, function (all_k, all_v) {
            wiki_str += '<div>';
            // wiki_str += '<div style="background-color: black;width: 100%;padding: 5px;border-radius: 5px;margin-bottom: 20px" > <h3 style="color: white;margin-left: 10px">'+ all_v.class +'</h3> </div>';

           var wikis = all_v.wikis;
           wiki_str += '<div class="row">';
           $.each(wikis, function (wikis_k, wikis_v) {
               var en_link = $.base64.encode(wikis_v.link);
               wiki_str += '<div class="col-md-4 text-cente" onclick="javascript:location.href=\'wiki_detail.html?_r='+ en_link +'\'" style="cursor: pointer">\n' +
                   '                        <div class="item_wiki ">\n' +
                   '                            <div style="margin: 10px" class="text-center">\n' +
                   '                                <img class="img-responsive" src="'+ wikis_v.img +'" style="max-width: 80%;" >\n' +
                   '                                <div style="margin-top: 20px;"><h3 style="font-weight: normal">'+ wikis_v.name +'</h3></div>\n' +
                                    '                                <div><h5 style="font-weight: normal">'+  wikis_v.desc +'</h5></div>\n' +
                                    '                            </div>\n' +
                                    '                        </div>\n' +
                                    '                    </div>        ';
                            });
                            wiki_str += '</div>'; // end row
                            wiki_str += '</div>';
                        });
                        $('#wiki_content').html(wiki_str);
                    } catch (error) {
                        console.error('加载本地配置文件失败:', error);
                        $('#wiki_content').html('<div class="alert alert-warning">暂无维基教程数据</div>');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('获取Wiki详情数据失败:', error);
                console.error('状态码:', xhr.status);
                console.error('响应文本:', xhr.responseText);
                
                // 尝试使用本地配置文件
                try {
                    var wiki_conf = get_local_file_info('static/conf/wiki.json');
                    if (typeof wiki_conf !== 'object') wiki_conf = JSON.parse(wiki_conf);
                    var all_wiki = wiki_conf.all;
                    var wiki_str = '';

                    $.each(all_wiki, function (all_k, all_v) {
                        wiki_str += '<div>';
                        // wiki_str += '<div style="background-color: black;width: 100%;padding: 5px;border-radius: 5px;margin-bottom: 20px" > <h3 style="color: white;margin-left: 10px">'+ all_v.class +'</h3> </div>';

                        var wikis = all_v.wikis;
                        wiki_str += '<div class="row">';
                        $.each(wikis, function (wikis_k, wikis_v) {
                            var en_link = $.base64.encode(wikis_v.link);
                            wiki_str += '<div class="col-md-4 text-cente" onclick="javascript:location.href=\'wiki_detail.html?_r='+ en_link +'\'" style="cursor: pointer">\n' +
                                '                        <div class="item_wiki ">\n' +
                                '                            <div style="margin: 10px" class="text-center">\n' +
                                '                                <img class="img-responsive" src="'+ wikis_v.img +'" style="max-width: 80%;" >\n' +
                                '                                <div style="margin-top: 20px;"><h3 style="font-weight: normal">'+ wikis_v.name +'</h3></div>\n' +
                   '                                <div><h5 style="font-weight: normal">'+  wikis_v.desc +'</h5></div>\n' +
                   '                            </div>\n' +
                   '                        </div>\n' +
                   '                    </div>        ';
           });
           wiki_str += '</div>'; // end row
           wiki_str += '</div>';
        });
        $('#wiki_content').html(wiki_str);
                } catch (error) {
                    console.error('加载本地配置文件失败:', error);
                    $('#wiki_content').html('<div class="alert alert-warning">暂无维基教程数据</div>');
                }
            }
        });
    }

    // 检查图片是否存在
    function checkImageExists(url, callback) {
        const img = new Image();
        img.src = url;
        img.onload = function() {
            callback(true);
        };
        img.onerror = function() {
            callback(false);
        };
    }
    
    // 添加调试日志
    function addDebugLog(message) {
        const now = new Date();
        const timeStr = now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds();
        $('#debug_content').append('<div><strong>[' + timeStr + ']</strong> ' + message + '</div>');
        console.log('[Debug] ' + message);
    }

    // 添加调试函数，用于显示链接编码前后的对比
    function debugBase64(input) {
        const encoded = $.base64.encode(input);
        const decoded = $.base64.decode(encoded);
        console.log('原始链接:', input);
        console.log('编码后:', encoded);
        console.log('解码后:', decoded);
        console.log('编码是否正确:', input === decoded);
        addDebugLog('链接编码: ' + input + ' -> ' + encoded);
        return encoded;
    }
    
    function showDirStructure(files) {
        // ... existing code ...
    }

    $(document).ready(function () {
        // 测试链接编码
        console.log('===== 链接编码测试 =====');
        debugBase64('/wiki/TB-96AI/');
        debugBase64('https://www.bearkey.com.cn/wiki_detail.html');
        console.log('========================');
        
        // 加载banner图片
        loadWikiBanner();
        
        // 初始化wiki内容
        init();

        // 检查是否需要显示调试信息
        if (window.location.search.includes('debug=true')) {
            $('#debug_info').show();
            addDebugLog('调试模式已启用');
        }
        
        // 隐藏语言切换按钮 - 仅在wiki.html页面
        // 等待导航栏加载完成后执行
        document.addEventListener('navbarLoaded', function() {
            // 查找语言切换按钮并隐藏
            $('a[onclick="chg_lang()"]').parent('li').hide();
            console.log('已隐藏语言切换按钮');
        });
    });
</script>
</body>
</html>