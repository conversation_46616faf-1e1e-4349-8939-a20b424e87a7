<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>方案定制-Bearkey-官网</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    
    <!-- css -->
    <link href="./css/bootstrap.min.css" rel="stylesheet"/>
    <link href="./css/style.css" rel="stylesheet"/>
    <link href="./css/footer.css" rel="stylesheet"/>
    <link href="./css/solution.css" rel="stylesheet"/>
    <link href="./css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="./css/flexslider.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body>
<div id="wrapper" style="display: none">
    <!-- 导航栏 -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header>

    <!-- 页面标题背景板 -->
    <div class="page-title-banner">
        <div class="container">
            <h1 class="page-title">方案定制</h1>
        </div>
    </div>

    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">贝启科技官网</a>
            <span class="separator">/</span>
            <span class="current">方案定制</span>
        </div>

        <!-- 方案定制内容 -->
        <div class="solution-content">
            <!-- 方案定制卡片区域 -->
            <div class="solution-cards">
                <!-- 第一个卡片 -->
                <div class="solution-card">
                    <div class="card-icon">
                        <img src="" alt="">
                    </div>
                    <h3 class="card-title"></h3>
                    <p class="card-description"></p>
                </div>

                <!-- 第二个卡片 -->
                <div class="solution-card">
                    <div class="card-icon">
                        <img src="" alt="">
                    </div>
                    <h3 class="card-title"></h3>
                    <p class="card-description"></p>
                </div>

                <!-- 第三个卡片 -->
                <div class="solution-card">
                    <div class="card-icon">
                        <img src="" alt="">
                    </div>
                    <h3 class="card-title"></h3>
                    <p class="card-description"></p>
                </div>
            </div>
        </div>

        <!-- 商业定制模块 -->
        <div class="business-customize">
            <h2 class="business-title">商业定制</h2>
            <form class="customize-form" onsubmit="return false;">
                <div class="form-row">
                    <input type="text" placeholder="姓名（必填）" required>
                    <input type="tel" placeholder="联系手机（必填）" required>
                    <input type="text" placeholder="邮箱地址（必填）" required>
                </div>
                <div class="form-row">
                    <input type="text" placeholder="公司（必填）" required class="company-input">
                    <input type="text" placeholder="定制类型（必填）" required class="type-input">
                </div>
                <div class="form-row">
                    <textarea placeholder="定制需求（必填）" required></textarea>
                </div>
                <div class="form-submit">
                    <button type="submit">提交</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 页脚 -->
    <footer>
        <!-- 页脚将通过footer.js动态加载 -->
    </footer>
</div>

<!-- javascript -->
<script src="js/jquery.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.fancybox.pack.js"></script>
<script src="js/jquery.fancybox-media.js"></script>
<script src="js/jquery.flexslider.js"></script>
<script src="js/nav.js"></script>
<script src="js/floating-toolbar.js"></script>
<script src="js/toolbar-data.js"></script>
<script src="js/solution.js"></script>
<script src="js/footer.js"></script>
<script src="js/custom.js"></script>
<!-- 返回顶部按钮 -->
<a href="#" class="scrollup" style="display: none;"><i class="fa fa-angle-up active"></i></a>
</body>
</html> 