/**
 * 下载页面文本格式化工具
 * 专门处理item-note元素在小屏幕下的文本显示
 * 独立于CSS样式，通过JS直接控制
 */
(function() {
    // 配置参数
    const config = {
        maxScreenWidth: 491,      // 小屏幕最大宽度
        charsPerLine: 45,         // 中文文本每行最大字符数
        engCharsPerLine: 60,      // 英文文本每行最大字符数
        checkInterval: 500        // 检查间隔(毫秒)
    };
    
    // 样式定义 - 从CSS中提取核心样式
    const styles = {
        container: {
            display: 'inline-block',
            width: '100%',
            height:'auto',
            position: 'relative',
            paddingRight: '5px',
            fontFamily: '"思源黑体", "Source Han Sans CN", sans-serif'
        },
        formattedText: {
            fontSize: '8px',               // 与CSS中保持一致
            transform: 'scale(0.7)',       // 与CSS中保持一致
            transformOrigin: 'left top',   // 与CSS中保持一致
            display: 'block',              // 与CSS中保持一致
            lineHeight: '1',               // 与CSS中保持一致
            color: '#929292',              // 与CSS中item-note颜色一致
            width: '150%',                 // 使用150%宽度
            wordBreak: 'break-word',       // 确保单词可以换行         // 添加底部内边距，增加与下划线的距离
        }
    };
    
    // 应用内联样式到元素
    function applyStyles(element, styleObj) {
        for (const prop in styleObj) {
            element.style[prop] = styleObj[prop];
        }
    }
    
    // 创建元素并应用样式
    function createStyledElement(tagName, styleObj, text) {
        const element = document.createElement(tagName);
        if (text) element.textContent = text;
        applyStyles(element, styleObj);
        return element;
    }
    
    // 判断是否为英文网站
    function isEnglishSite() {
        return window.location.pathname.includes('_en.html') || 
               window.location.pathname.includes('/en/') ||
               window.location.hostname.includes('en.') || 
               window.location.search.includes('lang=en') ||
               document.documentElement.lang === 'en';
    }
    
    // 检测文本是否主要为英文
    function isEnglishText(text) {
        // 如果已经判断是英文网站，直接返回true
        if (isEnglishSite()) return true;
        
        // 否则分析文本内容
        const englishPattern = /[a-zA-Z]/g;
        const chinesePattern = /[\u4e00-\u9fa5]/g;
        
        const englishMatches = text.match(englishPattern) || [];
        const chineseMatches = text.match(chinesePattern) || [];
        
        // 如果英文字符数量明显多于中文字符，认为是英文文本
        return englishMatches.length > chineseMatches.length * 1.5;
    }

    // 智能文本分行处理
    function getFormattedLines(text, isEnglishText) {
        // 先处理原始文本中的换行符
        // 注意：需要同时处理\n和\r\n两种换行符
        const textLines = text.split(/\r?\n/);
        const resultLines = [];
        
        // 确定每行字符限制
        const charLimit = isEnglishText ? config.engCharsPerLine : config.charsPerLine;
        
        // 处理每一行原始文本
        textLines.forEach(line => {
            // 如果原始行为空，直接添加一个空行
            if (line.trim() === '') {
                resultLines.push('');
                return;
            }
            
            // 将当前行按空格分割成单词或短语
            const words = line.split(' ');
            let currentLine = '';
            
            // 针对英文和中文使用相似的基于字符长度的算法
            words.forEach(word => {
                // 测试添加这个单词后的行长度
                const testLine = currentLine.length === 0 ? word : currentLine + ' ' + word;
                
                // 如果行长度超过限制，开始新行
                if (testLine.length > charLimit && currentLine !== '') {
                    resultLines.push(currentLine);
                    currentLine = word;
                } else {
                    // 否则继续添加到当前行
                    currentLine = testLine;
                }
            });
            
            // 添加最后一行
            if (currentLine !== '') {
                resultLines.push(currentLine);
            }
        });
        
        return resultLines;
    }
    
    // 主要处理函数
    function formatNoteText() {
        // 只在小屏幕下执行
        if (window.innerWidth > config.maxScreenWidth) return;
        
        // 查找所有item-note元素
        const noteElements = document.querySelectorAll('.item-note');
        
        noteElements.forEach(noteElement => {
            // 检查是否已处理过
            if (noteElement.getAttribute('data-formatted') === 'true') return;
            
            // 标记为已处理
            noteElement.setAttribute('data-formatted', 'true');
            
            // 保存原始样式用于恢复
            noteElement.setAttribute('data-original-style', noteElement.getAttribute('style') || '');
            
            // 获取原始文本 - 使用innerHTML以保留原始HTML格式
            const originalHTML = noteElement.innerHTML;
            // 使用textContent获取纯文本
            const originalText = noteElement.textContent.trim();
            
            if (!originalText) return;
            
            // 检查是否包含HTML标签
            const containsHTML = /<[a-z][\s\S]*>/i.test(originalHTML);
            
            // 隐藏原始元素 - 但保留在DOM中
            noteElement.style.display = 'none';
            
            // 创建新的容器
            const container = createStyledElement('div', styles.container);
            container.className = 'formatted-note-container';
            
            // 如果原始内容包含HTML，则直接使用原始HTML
            if (containsHTML) {
                const htmlElement = document.createElement('div');
                applyStyles(htmlElement, styles.formattedText);
                htmlElement.innerHTML = originalHTML;
                container.appendChild(htmlElement);
            } else {
                // 检查是否是英文文本
                const isEnglish = isEnglishText(originalText);
                
                // 获取格式化后的文本行
                const formattedLines = getFormattedLines(originalText, isEnglish);
                
                // 创建格式化文本元素
                const formattedElement = createStyledElement('div', styles.formattedText);
                
                // 添加每一行，用<br>分隔
                formattedLines.forEach((line, index) => {
                    formattedElement.appendChild(document.createTextNode(line));
                    if (index < formattedLines.length - 1) {
                        formattedElement.appendChild(document.createElement('br'));
                    }
                });
                
                container.appendChild(formattedElement);
            }
            
            // 将新容器插入到原始元素之后
            noteElement.parentNode.insertBefore(container, noteElement.nextSibling);
        });
    }
    
    // 监听窗口大小变化
    function handleResize() {
        if (window.innerWidth <= config.maxScreenWidth) {
            // 小屏幕模式 - 应用格式化
            formatNoteText();
        } else {
            // 大屏幕模式 - 恢复原始样式
            document.querySelectorAll('.item-note[data-formatted="true"]').forEach(element => {
                element.style.display = '';
                const originalStyle = element.getAttribute('data-original-style');
                if (originalStyle) element.setAttribute('style', originalStyle);
            });
            
            // 移除格式化容器
            document.querySelectorAll('.formatted-note-container').forEach(container => {
                container.remove();
            });
            
            // 重置格式化标记
            document.querySelectorAll('.item-note').forEach(element => {
                element.removeAttribute('data-formatted');
            });
        }
    }
    
    // 设置定期检查新添加的内容
    function setupPeriodicCheck() {
        setInterval(() => {
            // 只在小屏幕下检查
            if (window.innerWidth <= config.maxScreenWidth) {
                formatNoteText();
            }
        }, config.checkInterval);
    }
    
    // 初始化函数
    function init() {
        // 添加DOM加载完成后的初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                handleResize();
                setupPeriodicCheck();
            });
        } else {
            handleResize();
            setupPeriodicCheck();
        }
        
        // 添加窗口大小变化监听
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(handleResize, 100);
        });
        
        // 添加Ajax成功回调监听
        if (window.jQuery) {
            // 使用jQuery的全局Ajax事件处理器，更安全
            $(document).ajaxSuccess(function(event, xhr, settings) {
                // 延迟执行以确保DOM已更新
                setTimeout(formatNoteText, 100);
            });

            // 备用方案：劫持ajaxSettings.success（如果存在的话）
            const originalAjaxSuccess = jQuery.ajaxSettings.success;
            if (originalAjaxSuccess && typeof originalAjaxSuccess === 'function') {
                jQuery.ajaxSettings.success = function() {
                    const result = originalAjaxSuccess.apply(this, arguments);
                    // 延迟执行以确保DOM已更新
                    setTimeout(formatNoteText, 100);
                    return result;
                };
            }
        }
    }
    
    // 启动初始化
    init();
})(); 