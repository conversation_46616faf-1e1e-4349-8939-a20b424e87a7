// ========== 常量定义 ==========
const DEFAULT_TEXTS = {
    en: {
        title: 'Company Profile',
        content: 'Xiamen Beiqi technology co., LTD., with registered capital of 10 million RMB, is a high-tech enterprise focusing on the research and development of AI artificial intelligence products, mobile Internet products and digital multimedia electronic products.'
    },
    zh: {
        title: '公司简介',
        content: '厦门贝启科技有限公司注册资本壹仟万元人民币，是一家专注于AI人工智能产品、移动互联产品、数字多媒体电子产品的研发的高科技企业。'
    }
};

const CAROUSEL_CONFIG = {
    loop: true,
    margin: 0,
    nav: true,
    dots: true,
    autoplay: true,
    autoplayTimeout: 5000,
    autoplayHoverPause: true,
    items: 1,
    navText: [
        '<i class="fa fa-angle-left"></i>',
        '<i class="fa fa-angle-right"></i>'
    ],
    responsive: {
        0: { items: 1 },
        768: { items: 1 }
    }
};

// ========== 工具函数 ==========
function isEnglishSite() {
    return window.location.pathname.includes('_en.html') || 
           window.location.pathname.includes('/en/') ||
           window.location.hostname.includes('en.') || 
           window.location.search.includes('lang=en') ||
           document.documentElement.lang === 'en' ||
           document.body.classList.contains('english-version') ||
           document.querySelector('.index_en') !== null;
}

function normalizeImagePath(imgSrc) {
    if (!imgSrc) {
        console.log('图片路径为空');
        return '';
    }
    
    console.log('原始图片路径:', imgSrc);
    
    if (imgSrc.includes('C:')) {
        const fileName = imgSrc.split('\\').pop().split('/').pop();
        const result = '/uploads/' + fileName;
        console.log('处理Windows路径为:', result);
        return result;
    } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
        const result = '/' + imgSrc;
        console.log('处理相对路径为:', result);
        return result;
    }
    
    return imgSrc;
}

function sortByDisplayOrder(items) {
    return [...items].sort((a, b) => 
        Number(a.display_order || 100) - Number(b.display_order || 100)
    );
}

function setImageWithFallback(imgElement, src, fallbackSrc) {
    imgElement.onerror = function() {
        this.onerror = null;
        this.src = fallbackSrc;
    };
    imgElement.src = src;
}

function withErrorHandling(fn) {
    return function(...args) {
        try {
            return fn.apply(this, args);
        } catch (error) {
            // 如果发生错误，返回null，不输出任何日志到控制台
            return null;
        }
    };
}

// ========== 主逻辑 ==========
document.addEventListener('DOMContentLoaded', () => {
    // 检测是否为英文页面，并添加相应的类
    if (isEnglishSite()) {
        document.body.classList.add('english-version');
        document.documentElement.lang = 'en';
    }
    
    // 监听导航栏加载完成事件
    document.addEventListener('navbarLoaded', () => {
        // 如果在产品详情页面
        if (window.location.pathname.includes('product.html')) {
            document.querySelector('.product-template').classList.add('active');
            document.querySelector('.loading').classList.remove('active');
            document.querySelector('.error-message').classList.remove('active');
        }

        // 从API获取产品数据
        fetchProducts();
        
        // 获取公司信息数据
        fetchCompanyInfo();
        
        // 获取产品系列数据
        fetchProductRecommend();
        
        // 获取关于贝启科技数据
        fetchAboutCompany();
        
        // 确保jQuery和owl carousel已加载
        if (typeof jQuery !== 'undefined' && typeof jQuery.fn.owlCarousel !== 'undefined') {
        } else {
            console.error('jQuery或Owl Carousel未正确加载');
        }

        // 添加轮播图数据自动刷新功能
        setupCarouselRefresh();
        
        // 添加调试按钮
        if (window.location.search.includes('debug=true')) {
            addDebugTools();
        }
    });
});

// 添加调试工具
function addDebugTools() {
    // 创建调试面板
    const debugPanel = document.createElement('div');
    debugPanel.style.position = 'fixed';
    debugPanel.style.bottom = '10px';
    debugPanel.style.right = '10px';
    debugPanel.style.backgroundColor = 'rgba(0,0,0,0.7)';
    debugPanel.style.color = 'white';
    debugPanel.style.padding = '10px';
    debugPanel.style.borderRadius = '5px';
    debugPanel.style.zIndex = '9999';
    debugPanel.style.maxHeight = '300px';
    debugPanel.style.overflowY = 'auto';
    debugPanel.style.width = '300px';
    debugPanel.id = 'debug-panel';
    
    // 添加标题
    const title = document.createElement('h3');
    title.textContent = '轮播图调试面板';
    title.style.margin = '0 0 10px 0';
    debugPanel.appendChild(title);
    
    // 添加刷新按钮
    const refreshBtn = document.createElement('button');
    refreshBtn.textContent = '刷新轮播图';
    refreshBtn.style.marginRight = '10px';
    refreshBtn.style.padding = '5px';
    refreshBtn.addEventListener('click', () => {
        fetchProducts();
    });
    debugPanel.appendChild(refreshBtn);
    
    // 添加检查API按钮
    const checkApiBtn = document.createElement('button');
    checkApiBtn.textContent = '检查API数据';
    checkApiBtn.style.padding = '5px';
    checkApiBtn.addEventListener('click', () => {
        debugApiData();
    });
    debugPanel.appendChild(checkApiBtn);
    
    // 添加日志区域
    const logArea = document.createElement('div');
    logArea.id = 'debug-log';
    logArea.style.marginTop = '10px';
    logArea.style.borderTop = '1px solid #555';
    logArea.style.paddingTop = '10px';
    logArea.style.fontSize = '12px';
    debugPanel.appendChild(logArea);
    
    // 添加到页面
    document.body.appendChild(debugPanel);
}

// 检查API数据
async function debugApiData() {
    try {
        const response = await fetch('/apis/get_home_pic/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json',
                'X-Client-Type': 'frontend'
            },
            body: 'page=1&page_size=20'
        });
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
        }
        
        const result = await response.json();
        
        // 显示数据详情
        if (result.data_list && result.data_list.length > 0) {
            result.data_list.forEach(item => {
            });
            
            // 计算应该显示的轮播图
            const visible = result.data_list.filter(item => 
                item.show === true || item.show === 1 || item.show === "1"
            );
            
            // 按排序显示
            const sorted = [...visible].sort((a, b) => 
                parseInt(a.pic_idx || 0, 10) - parseInt(b.pic_idx || 0, 10)
            );
        }
    } catch (error) {
    }
}

// 设置轮播图数据自动刷新
function setupCarouselRefresh() {
    try {
        // 监听刷新缓存事件
        const eventSource = new EventSource('/refresh-carousel-cache');
        
        // 连接成功事件
        eventSource.onopen = function() {
        };
        
        // 接收消息事件
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                
                if (data.status === 'refresh') {
                    fetchProducts();
                }
            } catch (error) {
            }
        };
        
        // 错误处理
        eventSource.onerror = function(err) {
            eventSource.close();
            
            // 如果连接失败，改为定时轮询
            setInterval(checkForUpdates, 30000); // 每30秒检查一次
        };
    } catch (error) {
        // 降级为定时轮询
        setInterval(checkForUpdates, 30000); // 每30秒检查一次
    }
}

// 定时检查更新
function checkForUpdates() {
    fetchProducts();
}

// 轮播图控制
async function initCarousel() {
    try {
        // 判断当前是否为英文网站
        const isEnglishSite = window.location.pathname.includes('/en/') || 
                             window.location.pathname.includes('index_en.html') ||
                             window.location.hostname.includes('en.') || 
                             window.location.search.includes('lang=en');
        
        // 根据当前网站类型确定lang参数
        const lang = isEnglishSite ? 1 : 0;

        console.log("初始化轮播图...");
        
        // 直接调用fetchProducts函数获取和处理轮播图数据
        await fetchProducts();
        
    } catch (error) {
        console.error("轮播图初始化失败:", error);
        // 在出错时也使用空轮播图
        const isEnglishSite = window.location.pathname.includes('/en/') || 
                             window.location.pathname.includes('index_en.html') ||
                             window.location.hostname.includes('en.') || 
                             window.location.search.includes('lang=en');
        useDefaultSlides(isEnglishSite ? 1 : 0);
    }
}

function setupCarousel(slides) {
    const carousel = document.querySelector('.carousel-container');
    if (!carousel) return;

    // 如果没有轮播项，则不进行初始化
    if (!slides || slides.length === 0) {
        console.log("轮播图数据为空，不进行初始化");
        return;
    }

    let currentIndex = 0;
    let interval;

    // 更新轮播内容
    function updateSlide(index) {
        const slide = slides[index];
        
        // 更新文字内容
        const titleMain = document.querySelector('.carousel-title-main');
        const titleSub = document.querySelector('.carousel-title-sub');
        const featureList = document.querySelector('.carousel-feature-list');
        const productImg = document.querySelector('.carousel-product-img');
        
        // 确保DOM元素存在
        if (!titleMain || !titleSub || !featureList || !productImg) {
            console.error("轮播图DOM元素不完整");
            return;
        }
        
        // 更新标题（从后台数据中获取）
        if (slide.title) {
            // 不再分割标题，整个标题作为主标题
                titleMain.textContent = slide.title;
            titleSub.textContent = ''; // 副标题置空
        } else {
            titleMain.textContent = '';
            titleSub.textContent = '';
        }
        
        // 更新特性列表（从后台数据中获取）
        if (slide.description) {
            // 按换行符分割描述文本，每行作为一个特性项
            const features = slide.description.split('\n').filter(feature => feature.trim() !== '');
            featureList.innerHTML = features.map(feature => `<li>${feature.trim()}</li>`).join('');
        } else {
            featureList.innerHTML = '';
        }
        
        // 更新图片
        if (slide.pic) {
            let imgUrl = slide.pic;
            if (imgUrl.startsWith('/admin/uploads/')) {
                imgUrl = imgUrl.replace('/admin/uploads/', './uploads/');
            } else if (imgUrl.startsWith('/uploads/')) {
                imgUrl = '.' + imgUrl;
            }
            productImg.src = imgUrl;
            productImg.alt = slide.title || '';
        } else {
            productImg.src = './images/home/<USER>';
            productImg.alt = 'No image';
        }

        // 更新指示器状态
        const indicators = document.querySelectorAll('.carousel-indicators .indicator');
        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === currentIndex);
        });
        
        // 更新按钮链接
        const productPageBtn = document.querySelector('.carousel-btn-main');
        const shopBtn = document.querySelector('.carousel-btn-sub');
        
        // 确保按钮元素存在
        if (!productPageBtn || !shopBtn) {
            console.error("轮播图按钮元素不存在");
            return;
        }
        
        // 检查是否有关联的按钮数据
        if (slide.buttons && slide.buttons.length > 0) {
            // 按钮始终显示，只是链接可能不同
            
            // 处理第一个按钮（产品单页）
            if (slide.buttons[0]) {
                productPageBtn.href = slide.buttons[0].url || '#';
                console.log(`设置产品单页按钮链接: ${slide.buttons[0].url}`);
            } else {
                productPageBtn.href = '#';
            }
            
            // 处理第二个按钮（淘宝购买）
            if (slide.buttons[1]) {
                shopBtn.href = slide.buttons[1].url || '#';
                console.log(`设置淘宝购买按钮链接: ${slide.buttons[1].url}`);
            } else {
                shopBtn.href = '#';
            }
        } else if (slide.url) {
            // 向后兼容：如果没有按钮数据但有URL字段
            if (slide.url.includes(',')) {
                const urls = slide.url.split(',');
                productPageBtn.href = urls[0].trim();
                shopBtn.href = urls[1].trim();
            } else {
                productPageBtn.href = slide.url;
                shopBtn.href = '#';
            }
        } else {
            // 没有链接数据，设置为默认值
            productPageBtn.href = '#';
            shopBtn.href = '#';
        }
    }

    // 切换到指定轮播项
    function goToSlide(index) {
        currentIndex = index;
        if (currentIndex >= slides.length) currentIndex = 0;
        if (currentIndex < 0) currentIndex = slides.length - 1;
        
        updateSlide(currentIndex);
    }

    // 下一张
    function nextSlide() {
        goToSlide(currentIndex + 1);
    }

    // 上一张
    function prevSlide() {
        goToSlide(currentIndex - 1);
    }

    // 设置自动轮播
    function startAutoSlide() {
        if (interval) {
            clearInterval(interval);
        }
        interval = setInterval(nextSlide, 3000); // 改为3秒
    }

    // 停止自动轮播
    function stopAutoSlide() {
        if (interval) {
            clearInterval(interval);
            interval = null;
        }
    }

    // 创建指示器
    const indicators = document.querySelector('.carousel-indicators');
    if (indicators) {
        indicators.innerHTML = slides.map((_, i) => 
            `<span class="indicator${i === 0 ? ' active' : ''}" data-index="${i}"></span>`
        ).join('');

        // 绑定指示器点击事件
        indicators.querySelectorAll('.indicator').forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                goToSlide(index);
                stopAutoSlide();
                startAutoSlide();
            });
        });
    }

    // 绑定箭头按钮事件
    const prevBtn = carousel.querySelector('.prev-btn');
    const nextBtn = carousel.querySelector('.next-btn');

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            prevSlide();
            stopAutoSlide();
            startAutoSlide();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            nextSlide();
            stopAutoSlide();
            startAutoSlide();
        });
    }

    // 鼠标悬停时停止自动轮播
    carousel.addEventListener('mouseenter', stopAutoSlide);
    carousel.addEventListener('mouseleave', startAutoSlide);

    // 初始化第一张轮播图
    updateSlide(0);
    startAutoSlide();
}

// 使用默认数据（当API请求失败时）
function useDefaultSlides(lang) {
    // 不再提供默认内容，而是创建空轮播图
    console.log("没有找到有效的轮播图数据，创建空轮播图");
    
    const emptySlides = [];
    
    // 如果需要至少显示一个空项，可以添加一个空白项
    // emptySlides.push({
    //     title: '',
    //     description: '',
    //     pic: './images/home/<USER>',
    //     url: '#,#'
    // });
    
    setupCarousel(emptySlides);
    
    // 显示错误提示或占位内容
    const carousel = document.querySelector('.carousel-container');
    if (carousel) {
        // 添加无数据提示
        const noDataMsg = document.createElement('div');
        noDataMsg.className = 'carousel-no-data';
        noDataMsg.style.textAlign = 'center';
        noDataMsg.style.padding = '50px 0';
        noDataMsg.style.color = '#999';
        noDataMsg.textContent = lang === 1 ? 'No carousel data available' : '暂无轮播图数据';
        
        // 清空现有内容并添加提示
        carousel.innerHTML = '';
        carousel.appendChild(noDataMsg);
    }
}

// 页面加载完成后初始化轮播图
document.addEventListener('DOMContentLoaded', initCarousel);

// 从API获取产品数据
async function fetchProducts() {
    try {
        // 判断当前是否为英文网站
        const isEnglishSite = window.location.pathname.includes('/en/') || 
                              window.location.pathname.includes('index_en.html') ||
                              window.location.hostname.includes('en.') || 
                              window.location.search.includes('lang=en');
        
        // 根据当前网站类型确定lang参数
        const lang = isEnglishSite ? 1 : 0;
        
        console.log(`正在获取轮播图数据, 语言: ${lang === 1 ? '英文' : '中文'}`);
        
        // 直接从服务器获取数据
        const response = await fetch('/apis/get_home_pic/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json',
                'X-Client-Type': 'frontend'
            },
            body: `page=1&page_size=100&filters=${encodeURIComponent(JSON.stringify({lang: lang}))}`
        });
        
        if (!response.ok) {
            throw new Error(`服务器响应错误: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.status === 'ok' && result.data_list && result.data_list.length > 0) {
            console.log("获取到轮播图数据:", result.data_list.length, "条");
            
            // 分别存储内容项和按钮项
            const contentItems = [];
            const buttonItemsMap = {};
            
            // 分离内容项和按钮项
            result.data_list.forEach(item => {
                // 确保有标题和显示状态为true
                if (!item.title || !(item.show === true || item.show === 1 || item.show === "1")) {
                    let reason = !item.title ? "无标题" : "不显示";
                    console.log(`跳过项目 ID=${item.id}: ${reason}`);
                    return;
                }
                
                // 检查是否为按钮项
                const isButton = item.title.includes('按钮') || // 中文按钮
                               item.title.toLowerCase().includes('button') || // 英文按钮
                               item.title.toLowerCase().includes('shop'); // Shop按钮
                
                if (isButton) {
                    // 提取基础标题（移除按钮相关文字）
                    let baseTitle = item.title
                        .replace(/按钮.*$/, '')
                        .replace(/\s+button.*$/i, '')
                        .replace(/\s+shop.*$/i, '')
                        .trim();
                    
                    // 按基础标题分组存储按钮项
                    if (!buttonItemsMap[baseTitle]) {
                        buttonItemsMap[baseTitle] = [];
                    }
                    
                    buttonItemsMap[baseTitle].push({
                        id: item.id,
                        title: item.title,
                        url: item.url || '#',
                        pic_idx: parseInt(item.pic_idx || 0, 10),
                        buttonText: item.description || '' // 使用description作为按钮文本
                    });
                    
                    console.log(`找到按钮项: ID=${item.id}, 标题="${item.title}", 基础标题="${baseTitle}", URL=${item.url || '#'}, 按钮文本="${item.description || ''}"`);
                } 
                // 内容项处理保持不变
                else if (item.pic) {
                    contentItems.push({
                        id: item.id,
                        title: item.title,
                        pic: item.pic,
                        description: item.description || '',
                        url: item.url || '#',
                        pic_idx: parseInt(item.pic_idx || 0, 10),
                        lang: item.lang
                    });
                    
                    console.log(`找到内容项: ID=${item.id}, 标题="${item.title}", 排序=${item.pic_idx || 0}`);
                } else {
                    // 记录被跳过的项目
                    console.log(`跳过无效内容项 ID=${item.id}, 标题="${item.title}": 无图片`);
                }
            });
            
            // 按pic_idx排序内容项
            contentItems.sort((a, b) => a.pic_idx - b.pic_idx);
            console.log(`有效轮播内容项: ${contentItems.length}个`);
            
            // 对每个按钮组也进行排序
            for (const baseTitle in buttonItemsMap) {
                buttonItemsMap[baseTitle].sort((a, b) => a.pic_idx - b.pic_idx);
                console.log(`按钮组"${baseTitle}"排序完成，共${buttonItemsMap[baseTitle].length}个按钮`);
                
                // 输出排序后的按钮顺序
                buttonItemsMap[baseTitle].forEach((btn, index) => {
                    console.log(`  按钮${index+1}: "${btn.title}", 排序=${btn.pic_idx}, URL=${btn.url}`);
                });
            }
            
            if (contentItems.length > 0) {
                // 生成轮播图HTML
                generateCarouselHTML(contentItems, buttonItemsMap);
                return;
            } else {
                console.log("没有找到有效的轮播图内容项");
            }
        } else {
            console.log("API返回无数据或状态不为ok");
        }
        
        // 如果没有数据或请求失败，使用空轮播图
        useDefaultSlides(lang);
    } catch (error) {
        console.error("获取轮播图数据失败:", error);
        // 判断当前是否为英文网站
        const isEnglishSite = window.location.pathname.includes('/en/') || 
                              window.location.pathname.includes('index_en.html') ||
                              window.location.hostname.includes('en.') || 
                              window.location.search.includes('lang=en');
        useDefaultSlides(isEnglishSite ? 1 : 0);
    }
}

// 生成轮播图HTML并插入到页面
function generateCarouselHTML(contentItems, buttonItemsMap) {
    const carouselContainer = document.querySelector('.carousel-container');
    if (!carouselContainer) {
        console.error("找不到轮播图容器");
        return;
    }
    
    // 判断是否为英文界面
    const isEnglish = document.documentElement.lang === 'en' || 
                     window.location.pathname.includes('index_en.html') ||
                     window.location.hostname.includes('en.') || 
                     window.location.search.includes('lang=en');
    
    // 设置默认按钮文本
    const defaultBtnTexts = isEnglish ? 
        ['View Details', 'Buy Now'] : 
        ['产品单页', '淘宝购买'];
    
    // 检查是否有内容项
    if (!contentItems || contentItems.length === 0) {
        console.log("没有有效的轮播图内容项");
        
        // 显示无数据提示
        carouselContainer.innerHTML = `
            <div class="carousel-no-data" style="text-align: center; padding: 50px 0; color: #999;">
                暂无轮播图数据
            </div>
        `;
        return;
    }
    
    // 清空现有内容
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'carousel-items';
    
    // 创建指示器容器
    const indicators = document.querySelector('.carousel-indicators');
    if (indicators) {
        indicators.innerHTML = '';
    }
    
    // 生成每个轮播项的HTML
    contentItems.forEach((item, index) => {
        // 跳过无效项
        if (!item.title || !item.pic) {
            console.log(`跳过无效轮播项 #${index+1}: 缺少标题或图片`);
            return;
        }
    
        // 创建轮播项
        const carouselItem = document.createElement('div');
        carouselItem.className = `carousel-item${index === 0 ? ' active' : ''}`;
        carouselItem.dataset.index = index;
        
        // 处理标题
        let titleMain = item.title;
        let titleSub = '';
        
        // 处理描述（按换行符分割）
        const features = (item.description || '').split('\n')
            .map(line => line.trim())
            .filter(line => line !== '');
            
        // 查找匹配的按钮
        const buttons = buttonItemsMap[item.title] || [];
        
        // 获取按钮文本
        const buttonTexts = buttons.map(btn => btn.buttonText || '').filter(text => text);
        
        // 构建轮播项内容HTML - 将按钮组移出卡片，放在整个轮播项内
        const contentHTML = `
            <div class="carousel-content">
                <div class="carousel-card-left">
                    <div class="carousel-title-box">
                        <div class="carousel-title-main">${titleMain}</div>
                        <div class="carousel-title-sub">${titleSub}</div>
                    </div>
                    <ul class="carousel-feature-list">
                        ${features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>
                <div class="carousel-card-right">
                    <img src="${normalizeImagePath(item.pic)}" alt="${item.title}" class="carousel-product-img">
                </div>
            </div>
                    <div class="carousel-btn-group">
                        <a href="${buttons[0]?.url || '#'}" class="carousel-btn-main">
                            ${buttons[0]?.buttonText || defaultBtnTexts[0]}
                        </a>
                        <a href="${buttons[1]?.url || '#'}" class="carousel-btn-sub">
                            ${buttons[1]?.buttonText || defaultBtnTexts[1]}
                        </a>
            </div>
        `;
        
        carouselItem.innerHTML = contentHTML;
        itemsContainer.appendChild(carouselItem);
        
        // 添加指示器
        if (indicators) {
            const indicator = document.createElement('span');
            indicator.className = `indicator${index === 0 ? ' active' : ''}`;
            indicator.dataset.index = index;
            indicators.appendChild(indicator);
        }
        
        console.log(`生成轮播项 #${index+1}: "${item.title}", 关联按钮数: ${buttons.length}`);
    });
    
    // 检查是否有有效的轮播项被添加
    if (itemsContainer.children.length === 0) {
        console.log("没有生成任何有效的轮播项");
        carouselContainer.innerHTML = `
            <div class="carousel-no-data" style="text-align: center; padding: 50px 0; color: #999;">
                暂无有效轮播图数据
            </div>
        `;
        return;
    }
    
    // 替换现有内容
    carouselContainer.innerHTML = '';
    carouselContainer.appendChild(itemsContainer);
    
    // 恢复指示器和导航按钮
    if (indicators && indicators.children.length > 0) {
        carouselContainer.appendChild(indicators);
    }
    
    // 恢复左右箭头导航
    const arrowsContainer = document.createElement('div');
    arrowsContainer.className = 'carousel-arrows';
    arrowsContainer.innerHTML = `
        <div class="carousel-arrow-container carousel-arrow-left">
            <div class="carousel-arrow-btn prev-btn"></div>
        </div>
        <div class="carousel-arrow-container carousel-arrow-right">
            <div class="carousel-arrow-btn next-btn"></div>
        </div>
    `;
    carouselContainer.appendChild(arrowsContainer);
    
    // 添加事件监听
    setupCarouselEvents(itemsContainer.children.length);
}

// 设置轮播图事件
function setupCarouselEvents(slideCount) {
    const carousel = document.querySelector('.carousel-container');
    if (!carousel) return;
    
    let currentIndex = 0;
    let interval;
    
    // 获取元素
    const indicators = carousel.querySelectorAll('.carousel-indicators .indicator');
    const prevBtn = carousel.querySelector('.prev-btn');
    const nextBtn = carousel.querySelector('.next-btn');
    const items = carousel.querySelectorAll('.carousel-item');
    const buttons = carousel.querySelectorAll('.carousel-btn-main, .carousel-btn-sub');
    
    // 切换到指定轮播项
    function goToSlide(index) {
        currentIndex = index;
        if (currentIndex >= slideCount) currentIndex = 0;
        if (currentIndex < 0) currentIndex = slideCount - 1;
        
        // 更新轮播项状态
        items.forEach((item, i) => {
            item.classList.toggle('active', i === currentIndex);
        });
        
        // 更新指示器状态
        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === currentIndex);
        });
    }
    
    // 下一张
    function nextSlide() {
        goToSlide(currentIndex + 1);
    }
    
    // 上一张
    function prevSlide() {
        goToSlide(currentIndex - 1);
    }
    
    // 设置自动轮播 - 间隔改为3秒
    function startAutoSlide() {
        // 先清除可能存在的定时器
        if (interval) {
            clearInterval(interval);
        }
        // 设置新的定时器，间隔为3秒
        interval = setInterval(nextSlide, 3000);
    }
    
    // 停止自动轮播
    function stopAutoSlide() {
        if (interval) {
            clearInterval(interval);
            interval = null;
        }
    }
    
    // 绑定指示器点击事件
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            goToSlide(index);
            stopAutoSlide();
            startAutoSlide();
        });
        
        // 鼠标悬停在指示器上时暂停轮播
        indicator.addEventListener('mouseenter', stopAutoSlide);
        indicator.addEventListener('mouseleave', startAutoSlide);
    });
    
    // 绑定箭头按钮事件
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            prevSlide();
            stopAutoSlide();
            startAutoSlide();
        });
        
        // 鼠标悬停在上一张按钮上时暂停轮播
        prevBtn.addEventListener('mouseenter', stopAutoSlide);
        prevBtn.addEventListener('mouseleave', startAutoSlide);
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            nextSlide();
            stopAutoSlide();
            startAutoSlide();
        });
        
        // 鼠标悬停在下一张按钮上时暂停轮播
        nextBtn.addEventListener('mouseenter', stopAutoSlide);
        nextBtn.addEventListener('mouseleave', startAutoSlide);
    }
    
    // 为所有按钮添加鼠标悬停事件
    buttons.forEach(button => {
        button.addEventListener('mouseenter', stopAutoSlide);
        button.addEventListener('mouseleave', startAutoSlide);
    });
    
    // 鼠标悬停时停止自动轮播
    carousel.addEventListener('mouseenter', stopAutoSlide);
    carousel.addEventListener('mouseleave', startAutoSlide);
    
    // 启动自动轮播
    startAutoSlide();
}

// 标准化图片路径
function normalizeImagePath(imgPath) {
    if (!imgPath) return '';
    
    if (imgPath.includes('C:')) {
        const fileName = imgPath.split('\\').pop().split('/').pop();
        const result = '/uploads/' + fileName;
        console.log('处理Windows路径为:', result);
        return result;
    } else if (!imgPath.startsWith('http') && !imgPath.startsWith('/')) {
        const result = '/' + imgPath;
        console.log('处理相对路径为:', result);
        return result;
    }
    
    return imgPath;
}

// 更新产品排序
async function updateProductOrder(id, newOrder) {
    try {
        const response = await fetch(`http://localhost:3002/api/products/${id}/sort`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ sort_order: newOrder })
        });

        if (!response.ok) {
            throw new Error('更新排序失败');
        }

        return await response.json();
    } catch (error) {
        throw error;
    }
}

// 修改添加产品函数
async function addProduct(formData) {
  try {
        const response = await fetch('http://localhost:3002/api/products', {
      method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('添加产品失败');
        }

        return await response.json();
  } catch (error) {
        throw error;
  }
}

/**
 * 获取公司详情信息
 */
function fetchCompanyInfo() {
    try {
        // 获取公司简介数据
        $.ajax({
            url: '/apis/company_info/list',
            type: 'GET',
            data: {
                page: 1,
                page_size: 10,
                filters: JSON.stringify({
                    info_type: 'company_profile',  // 修正为正确的info_type
                    show: true,
                    lang: isEnglishSite() ? 1 : 0
                })
            },
            success: function(result) {
                if (result.status === 'ok' && result.data) {
                    // 按显示顺序排序
                    const sortedData = sortByDisplayOrder(result.data);
                    updateCompanyInfo(sortedData);
                } else {
                    useDefaultCompanyInfo();
                }
            },
            error: function(error) {
                useDefaultCompanyInfo();
            }
        });
    } catch (error) {
        useDefaultCompanyInfo();
    }
}

// 更新updateCompanyInfo函数以支持更丰富的内容显示
function updateCompanyInfo(data) {
    const aboutUsSection = document.querySelector('.aboutUs .container');
    if (!aboutUsSection) return;

    const langConfig = isEnglishSite() ? DEFAULT_TEXTS.en : DEFAULT_TEXTS.zh;
    
    if (data && data.length > 0) {
        let html = '<div class="row">';
        
        // 处理第一个内容（一般是图片）
        if (data[0]) {
            html += '<div class="col-md-6">';
            if (data[0].image_path) {
                html += `<img src="${normalizeImagePath(data[0].image_path)}" 
                             alt="${data[0].title || '公司简介'}" 
                             class="img-fluid">`;
            } else if (data[0].content) {
                html += `
                    <div class="about-logo">
                        <h3>${data[0].title || langConfig.title}</h3>
                        <p>${data[0].content}</p>
                    </div>
                `;
            }
            html += '</div>';
        }
        
        // 处理第二个内容（一般是文字）
        if (data[1]) {
            html += '<div class="col-md-6">';
            if (data[1].image_path) {
                html += `<img src="${normalizeImagePath(data[1].image_path)}" 
                             alt="${data[1].title || '公司简介'}" 
                             class="img-fluid">`;
            } else if (data[1].content) {
                html += `
                    <div class="about-logo">
                        <h3>${data[1].title || langConfig.title}</h3>
                        <p>${data[1].content}</p>
                    </div>
                `;
            }
            html += '</div>';
        }
        
        html += '</div>';
        aboutUsSection.innerHTML = html;
    } else {
        useDefaultCompanyInfo();
    }
}

function useDefaultCompanyInfo() {
    const aboutUsSection = document.querySelector('.aboutUs .container');
    if (!aboutUsSection) return;

    const langConfig = isEnglishSite() ? DEFAULT_TEXTS.en : DEFAULT_TEXTS.zh;
    
    aboutUsSection.innerHTML = `
        <div class="row">
            <div class="col-md-12">
                <div class="about-logo">
                    <h3>${langConfig.title}</h3>
                    <p>${langConfig.content}</p>
                </div>
            </div>
        </div>
    `;
}

async function fetchCompanyInfoByType(infoType) {
    try {
        const lang = isEnglishSite() ? 1 : 0;
        logInfo('当前网站类型: ' + (lang === 1 ? '英文' : '中文'));
        logInfo('正在请求API: /apis/company_info/list 获取' + infoType + '数据');

        const response = await $.ajax({
            url: '/apis/company_info/list',
            type: 'GET',
            data: {
                page: 1,
                page_size: 10,
                filters: JSON.stringify({
                    info_type: infoType,
                    show: true,
                    lang: lang
                })
            }
        });

        if (response.status === 'ok' && response.data) {
            logInfo('获取到的' + infoType + '数据:');
            response.data.forEach(item => {
                logInfo('添加' + infoType + '项: ' + (item.title || '未命名'));
            });
            return response.data;
        }
    } catch (error) {
    }

    logInfo('======= ' + infoType + '数据获取和处理完成 =======');
    return null;
}

/**
 * 根据显示顺序调整公司简介布局
 */
const updateCompanyProfileLayout = withErrorHandling(function(profiles) {
    logInfo('======= 开始调整公司简介布局 =======');
    
    // 1. 初始化检查
    const aboutUsSection = document.querySelector('section.aboutUs');
    const container = aboutUsSection?.querySelector('.container');
    if (!container) {
        return;
    }

    // 2. 环境检查
    const isEnglishSite = window.location.pathname.includes('_en.html');
    logInfo(`当前是否为英文网站: ${isEnglishSite}`);

    // 3. 数据筛选和排序
    const companyProfileItems = profiles.filter(item => 
        item.info_type === 'company_profile' && 
        (isEnglishSite ? item.lang === 1 : item.lang === 0)
    );

    // 4. 记录数据状态
    companyProfileItems.forEach((item, index) => {
        logInfo(`公司简介[${index}]: ID=${item.id}, 显示顺序=${item.display_order}, 标题=${item.title}`);
        logInfo(`    有图片=${!!item.image_path}, 有内容=${!!item.content}`);
        if (item.image_path) {
            logInfo(`    图片路径: ${item.image_path}`);
        }
    });

    // 5. 按display_order排序
    const sortedProfiles = sortByDisplayOrder(companyProfileItems);
    
    // 6. 生成布局HTML
    let newHTML = '<div class="row">';

    // 7. 处理左侧内容（display_order较小的项目）
    if (sortedProfiles.length > 0) {
        const leftItem = sortedProfiles[0]; // display_order = 2
        newHTML += '<div class="col-md-6">';
        
        // 根据内容类型生成左侧HTML
        if (leftItem.image_path) {
            newHTML += `<img src="${normalizeImagePath(leftItem.image_path)}" 
                             alt="${leftItem.title || '公司简介'}" 
                             width="463" height="325" 
                             class="img-center">`;
        } else if (leftItem.content) {
            const title = leftItem.title || DEFAULT_TEXTS[isEnglishSite ? 'en' : 'zh'].title;
            newHTML += `
                <div>
                    <h2>${title}</h2>
                    <p style="font-size: 16px">${leftItem.content}</p>
                </div>
            `;
        }
        newHTML += '</div>';
    }

    // 8. 处理右侧内容（display_order较大的项目）
    if (sortedProfiles.length > 1) {
        const rightItem = sortedProfiles[1]; // display_order = 100
        newHTML += '<div class="col-md-6">';
        
        // 根据内容类型生成右侧HTML
        if (rightItem.image_path) {
            newHTML += `<img src="${normalizeImagePath(rightItem.image_path)}" 
                             alt="${rightItem.title || '公司简介'}" 
                             width="463" height="325" 
                             class="img-center">`;
        }
        if (rightItem.content) {
            const title = rightItem.title || DEFAULT_TEXTS[isEnglishSite ? 'en' : 'zh'].title;
            newHTML += `
                <div>
                    <h2>${title}</h2>
                    <p style="font-size: 16px">${rightItem.content}</p>
                </div>
            `;
        }
        newHTML += '</div>';
    }

    newHTML += '</div>';

    // 9. 更新DOM
    container.innerHTML = newHTML;
    logInfo('公司简介布局更新完成');
});

/**
 * 更新图片元素
 */
function updateImage(imgElement, data) {
    if (!imgElement || !data) return;
    
    // 获取图片路径
    let imgSrc = '';
    if (data.image_path) {
        imgSrc = data.image_path;
    } else if (data.content && (data.content.includes('/uploads/') || data.content.startsWith('http'))) {
        imgSrc = data.content;
    }
    
    if (imgSrc) {
        // 处理绝对路径和相对路径
        if (imgSrc.includes('C:')) {
            const fileName = imgSrc.split('\\').pop().split('/').pop();
            imgSrc = '/uploads/' + fileName;
        } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
            imgSrc = '/' + imgSrc;
        }
        
        imgElement.src = imgSrc;
        imgElement.onerror = function() {
            this.onerror = null;
            this.src = '/images/home/<USER>'; // 兜底默认图片
        };
    }
}

/**
 * 更新文本元素
 */
function updateText(textElement1, textElement2, data) {
    if (!textElement1 || !data || !data.content) return;
    
    // 如果内容较长，分成两段
    const content = data.content;
    
    if (content.length > 300 && textElement2) {
        const midPoint = content.indexOf('。', 250);
        if (midPoint > 0) {
            textElement1.textContent = content.substring(0, midPoint + 1);
            textElement2.textContent = content.substring(midPoint + 1);
        } else {
            textElement1.textContent = content;
            if (textElement2) textElement2.textContent = '';
        }
    } else {
        textElement1.textContent = content;
        if (textElement2) textElement2.textContent = '';
    }
}

/**
 * 更新公司Logo
 */
function updateCompanyLogo(data) {
    try {
        // 更新顶部导航Logo
        const navbarLogo = document.getElementById('navbar_logo');
        if (!navbarLogo) {
            return;
        }
        
        // 获取图片路径
        let imgSrc = '';
        
        // 优先使用image_path字段，如果没有则使用content字段
        if (data.image_path) {
            imgSrc = data.image_path;
        } else if (data.content) {
            imgSrc = data.content;
        }
        
        if (imgSrc) {
            // 处理绝对路径和相对路径
            if (imgSrc.includes('C:')) {
                const fileName = imgSrc.split('\\').pop().split('/').pop();
                imgSrc = '/uploads/' + fileName;
            } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
                imgSrc = '/' + imgSrc;
            }
            
            // 更新导航Logo
            navbarLogo.onerror = function() {
                this.onerror = null;
                this.src = '/images/home/<USER>';
            };
            navbarLogo.src = imgSrc;
        } else {
        }
    } catch (error) {
    }
}

/**
 * 获取并显示产品系列数据
 */
async function fetchProductRecommend() {
    try {
        const lang = isEnglishSite() ? 1 : 0;
        const response = await fetch('/apis/get_product_recommend/', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `page=1&page_size=100&filters=${encodeURIComponent(JSON.stringify({lang: lang}))}`
        });

        if (!response.ok) {
            throw new Error('获取产品系列数据失败');
        }

        const result = await response.json();
        
        if (result.status === 'ok' && result.data_list && result.data_list.length > 0) {
            // 过滤出当前语言的显示项目
            const products = result.data_list.filter(item => 
                (item.show === true || item.show === 1 || item.show === "1") && 
                item.lang === lang
            );
            
            // 按显示顺序排序
            const sortedProducts = products.sort((a, b) => 
                parseInt(a.sort_order || 0, 10) - parseInt(b.sort_order || 0, 10)
            );

            updateProductRecommend(sortedProducts);
        } else {
            console.log('没有获取到产品系列数据');
        }
    } catch (error) {
        console.error('获取产品系列数据失败:', error);
    }
}

/**
 * 更新产品系列展示
 */
function updateProductRecommend(products) {
    const productGrid = document.querySelector('.product-grid');
    if (!productGrid) return;

    // 获取所有现有的产品项
    const existingItems = productGrid.querySelectorAll('.product-item');
    
    // 遍历产品数据，更新或添加到现有项
    products.forEach((product, index) => {
        // 如果有对应的现有项，则更新它
        if (index < existingItems.length) {
            const item = existingItems[index];
            const imageContainer = item.querySelector('.product-image-placeholder');
            const titleLink = item.querySelector('.product-title a');
            
            // 更新图片
            if (imageContainer) {
                // 清空现有内容
                imageContainer.innerHTML = '';
                
                // 添加图片（使用icon字段作为图片路径）
                if (product.icon) {
                    const img = document.createElement('img');
                    img.src = normalizeImagePath(product.icon);
                    img.alt = product.title || '';
                    imageContainer.appendChild(img);
                }
            }
            
            // 更新标题和链接
            if (titleLink) {
                titleLink.textContent = product.title || titleLink.textContent;
                
                // 构建目标URL，添加filter参数
                const targetPage = isEnglishSite() ? 'product_en.html' : 'product.html';
                const filter = product.title;
                const targetUrl = `${targetPage}?filter=${encodeURIComponent(filter)}`;
                
                // 设置链接
                titleLink.href = targetUrl;
                
                // 添加点击事件
                titleLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = targetUrl;
                });
            }
        } else {
            // 如果没有对应的现有项，则创建新项
            const productItem = document.createElement('div');
            productItem.className = 'product-item';

            const imageContainer = document.createElement('div');
            imageContainer.className = 'product-image-placeholder';

            // 创建图片元素
            if (product.icon) {
                const img = document.createElement('img');
                img.src = normalizeImagePath(product.icon);
                img.alt = product.title || '';
                imageContainer.appendChild(img);
            }

            const title = document.createElement('h3');
            title.className = 'product-title';
            
            const link = document.createElement('a');
            // 构建目标URL，添加filter参数
            const targetPage = isEnglishSite() ? 'product_en.html' : 'product.html';
            const filter = product.title;
            const targetUrl = `${targetPage}?filter=${encodeURIComponent(filter)}`;
            
            // 设置链接属性
            link.href = targetUrl;
            link.textContent = product.title || '';
            
            // 添加点击事件
            link.addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = targetUrl;
            });
            
            title.appendChild(link);
            productItem.appendChild(imageContainer);
            productItem.appendChild(title);
            
            productGrid.appendChild(productItem);
        }
    });
}

/**
 * 获取并显示热门产品数据
 */
async function fetchHotProducts() {
    try {
        const lang = isEnglishSite() ? 1 : 0;
        
        // 构建查询参数
        const params = new URLSearchParams({
            page: '1',
            page_size: '100',
            filters: JSON.stringify({
                info_type: 'hot_products',
                show: true,
                lang: lang
            })
        });

        const response = await fetch(`/apis/company_info/list?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取热门产品数据失败');
        }

        const result = await response.json();
        
        if (result.status === 'ok' && result.data) {
            // 确保只处理hot_products类型的数据
            const hotProducts = result.data.filter(item => item.info_type === 'hot_products');
            console.log(`获取到${hotProducts.length}个热门产品数据`);
            
            // 按显示顺序排序
            const sortedProducts = sortByDisplayOrder(hotProducts);
            updateHotProducts(sortedProducts);
        } else {
            console.log('没有获取到热门产品数据');
        }
    } catch (error) {
        console.error('获取热门产品数据失败:', error);
    }
}

/**
 * 更新热门产品展示
 */
function updateHotProducts(products) {
    const hotProductsGrid = document.querySelector('.hot-products-grid');
    if (!hotProductsGrid) return;

    // 清空现有内容
    hotProductsGrid.innerHTML = '';
    
    // 确保products是数组
    let validProducts = Array.isArray(products) ? products : [];
    
    // 过滤掉无效项目
    validProducts = validProducts.filter(product => 
        product && product.title && (product.image_path || product.content)
    );
    
    console.log(`从后台获取到${validProducts.length}个有效热门产品`);
    
    // 如果没有足够的产品数据，添加默认产品
    const defaultProducts = [
        {
            title: "RK3588开发板",
            image_path: "./images/products/RK3588.png",
            url: "/"
        },
        {
            title: "RK3399开发板",
            image_path: "./images/products/RK3399.png",
            url: "/"
        },
        {
            title: "RK3566开发板",
            image_path: "./images/products/RK3566.png",
            url: "/"
        },
        {
            title: "RK3308开发板",
            image_path: "./images/products/RK3308.png",
            url: "/"
        },
        {
            title: "RK3568数据采集网关",
            image_path: "./images/products/RK3568.png",
            url: "/"
        },
        {
            title: "RK3576数据采集网关",
            image_path: "./images/products/RK3576.png",
            url: "/"
        }
    ];
    
    // 只在产品数量不足8个时添加默认产品
    if (validProducts.length < 8) {
        const neededDefaultProducts = defaultProducts.slice(0, 8 - validProducts.length);
        validProducts = validProducts.concat(neededDefaultProducts);
    }
    
    console.log(`总共${validProducts.length}个产品，${Math.ceil(validProducts.length/2)}组`);
    
    // 将产品按每组2个分组
    const groupedProducts = [];
    for (let i = 0; i < validProducts.length; i += 2) {
        groupedProducts.push(validProducts.slice(i, i + 2));
    }

    // 创建第一组产品（默认显示）
    if (groupedProducts.length > 0) {
        createProductGroup(groupedProducts[0]);
    }

    // 更新指示器
    updateHotProductsIndicators(groupedProducts.length);

    // 设置轮播控制
    setupHotProductsCarousel(groupedProducts);
}

/**
 * 创建产品组
 */
function createProductGroup(products) {
    const hotProductsGrid = document.querySelector('.hot-products-grid');
    if (!hotProductsGrid) return;

    products.forEach(product => {
        const productItem = document.createElement('div');
        productItem.className = 'hot-product-item';
        
        let imgSrc = product.image_path || product.content;
        imgSrc = normalizeImagePath(imgSrc);
        
        productItem.innerHTML = `
            <div class="hot-product-image">
                <img src="${imgSrc}" alt="${product.title}">
            </div>
            <h3 class="hot-product-title">
                <a href="${product.url || '#'}">${product.title}</a>
            </h3>
        `;
        
        // 添加鼠标悬停事件，确保在产品项上也能暂停轮播
        productItem.addEventListener('mouseenter', function() {
            // 触发自定义事件，通知父容器停止轮播
            const stopEvent = new CustomEvent('productItemHover', { detail: { action: 'stop' } });
            hotProductsGrid.dispatchEvent(stopEvent);
        });
        
        productItem.addEventListener('mouseleave', function() {
            // 触发自定义事件，通知父容器恢复轮播
            const resumeEvent = new CustomEvent('productItemHover', { detail: { action: 'resume' } });
            hotProductsGrid.dispatchEvent(resumeEvent);
        });
        
        hotProductsGrid.appendChild(productItem);
    });
}

/**
 * 设置热门产品轮播控制
 */
function setupHotProductsCarousel(groupedProducts) {
    if (groupedProducts.length <= 1) return;

    let currentGroupIndex = 0;
    let isAnimating = false;
    let autoPlayInterval;
    const AUTOPLAY_DELAY = 3000; // 3秒

    const hotProductsGrid = document.querySelector('.hot-products-grid');
    const indicators = document.querySelectorAll('.hot-products-indicator');
    const prevBtn = document.querySelector('.hot-products-side-box.left');
    const nextBtn = document.querySelector('.hot-products-side-box.right');
    const hotProductsSection = document.querySelector('.hot-products');

    // 切换到指定组
    function switchToGroup(index, direction = 'next') {
        if (isAnimating) return;
        isAnimating = true;

        // 更新指示器
        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === index);
        });

        // 保存当前内容的克隆
        const oldContent = hotProductsGrid.innerHTML;
        
        // 清空并添加新内容
        hotProductsGrid.innerHTML = '';
        createProductGroup(groupedProducts[index]);
        
        // 设置过渡效果
        hotProductsGrid.style.opacity = '0';
        
        // 触发重排后显示新内容
        setTimeout(() => {
            hotProductsGrid.style.opacity = '1';
            isAnimating = false;
        }, 50);

        currentGroupIndex = index;
    }

    // 下一组
    function nextGroup() {
        const nextIndex = (currentGroupIndex + 1) % groupedProducts.length;
        switchToGroup(nextIndex, 'next');
    }

    // 上一组
    function prevGroup() {
        const prevIndex = currentGroupIndex === 0 ? groupedProducts.length - 1 : currentGroupIndex - 1;
        switchToGroup(prevIndex, 'prev');
    }

    // 自动播放
    function startAutoPlay() {
        stopAutoPlay(); // 先清除可能存在的定时器
        autoPlayInterval = setInterval(nextGroup, AUTOPLAY_DELAY);
        console.log("热门产品轮播：开始自动播放");
    }

    function stopAutoPlay() {
        if (autoPlayInterval) {
            clearInterval(autoPlayInterval);
            autoPlayInterval = null;
            console.log("热门产品轮播：停止自动播放");
        }
    }

    // 重置自动播放（用于用户交互后）
    function resetAutoPlay() {
        stopAutoPlay();
        // 延迟100ms后重新开始自动播放，避免与点击事件冲突
        setTimeout(startAutoPlay, 100);
    }

    // 绑定事件
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            console.log("点击左箭头");
            prevGroup();
            stopAutoPlay(); // 点击后暂停
        });
        
        // 鼠标离开箭头后重新开始自动播放
        prevBtn.addEventListener('mouseleave', resetAutoPlay);
        // 鼠标悬停在箭头上暂停自动播放
        prevBtn.addEventListener('mouseenter', stopAutoPlay);
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            console.log("点击右箭头");
            nextGroup();
            stopAutoPlay(); // 点击后暂停
        });
        
        // 鼠标离开箭头后重新开始自动播放
        nextBtn.addEventListener('mouseleave', resetAutoPlay);
        // 鼠标悬停在箭头上暂停自动播放
        nextBtn.addEventListener('mouseenter', stopAutoPlay);
    }

    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            console.log(`点击指示器 ${index}`);
            if (index !== currentGroupIndex) {
                switchToGroup(index);
                stopAutoPlay(); // 点击后暂停
            }
        });
        
        // 鼠标离开指示器后重新开始自动播放
        indicator.addEventListener('mouseleave', resetAutoPlay);
        // 鼠标悬停在指示器上暂停自动播放
        indicator.addEventListener('mouseenter', stopAutoPlay);
    });

    // 鼠标悬停在产品区域时暂停自动播放
    if (hotProductsSection) {
        // 整个热门产品区域的悬停事件
        hotProductsSection.addEventListener('mouseenter', () => {
            console.log("鼠标进入热门产品区域");
            stopAutoPlay();
        });
        hotProductsSection.addEventListener('mouseleave', () => {
            console.log("鼠标离开热门产品区域");
            resetAutoPlay();
        });
        
        // 产品网格的悬停事件，确保在产品项上也能正确暂停
        hotProductsGrid.addEventListener('mouseenter', stopAutoPlay);
        hotProductsGrid.addEventListener('mouseleave', resetAutoPlay);
    }

    // 开始自动播放
    startAutoPlay();
    
    // 添加可见性变化检测，当页面不可见时暂停轮播
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            stopAutoPlay();
        } else {
            resetAutoPlay();
        }
    });

    // 监听产品项悬停事件
    hotProductsGrid.addEventListener('productItemHover', function(e) {
        if (e.detail.action === 'stop') {
            console.log("产品项悬停：暂停轮播");
            stopAutoPlay();
        } else if (e.detail.action === 'resume') {
            console.log("产品项离开：恢复轮播");
            resetAutoPlay();
        }
    });
}

/**
 * 更新热门产品指示器
 */
function updateHotProductsIndicators(productCount) {
    const indicators = document.querySelector('.hot-products-indicators');
    if (!indicators) return;

    // 清空现有指示器
    indicators.innerHTML = '';
    
    // 创建新的指示器
    for (let i = 0; i < productCount; i++) {
        const indicator = document.createElement('div');
        indicator.className = `hot-products-indicator${i === 0 ? ' active' : ''}`;
        indicators.appendChild(indicator);
    }
}

// 在文档加载完成后初始化热门产品
document.addEventListener('DOMContentLoaded', () => {
    // 获取热门产品数据
    fetchHotProducts();
    
    // ... existing code ...
});

/**
 * 获取并显示关于贝启科技数据
 */
async function fetchAboutCompany() {
    try {
        const lang = isEnglishSite() ? 1 : 0;
        console.log(`开始获取关于贝启科技-首页数据，语言: ${lang === 1 ? '英文' : '中文'}`);
        
        // 构建查询参数
        const params = new URLSearchParams({
            page: '1',
            page_size: '10',
            filters: JSON.stringify({
                info_type: 'about_company_home',
                show: true,
                lang: lang
            })
        });

        console.log(`请求参数: ${params.toString()}`);
        console.log(`请求URL: /apis/company_info/list?${params.toString()}`);

        const response = await fetch(`/apis/company_info/list?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`获取关于贝启科技-首页数据失败，HTTP状态码: ${response.status}`);
        }

        const result = await response.json();
        console.log(`API响应状态: ${result.status}`);
        
        if (result.status === 'ok' && result.data) {
            console.log(`API返回数据条数: ${result.data.length}`);
            
            // 确保只处理about_company_home类型的数据
            const aboutItems = result.data.filter(item => item.info_type === 'about_company_home');
            console.log(`过滤后的about_company_home数据条数: ${aboutItems.length}`);
            
            // 输出每条数据的基本信息，便于调试
            aboutItems.forEach((item, index) => {
                console.log(`数据项[${index}] - ID: ${item.id}, 标题: ${item.title}, 显示顺序: ${item.display_order}`);
            });
            
            // 按显示顺序排序
            const sortedAbout = sortByDisplayOrder(aboutItems);
            console.log('数据按显示顺序排序完成');
            
            // 调用更新函数
            updateAboutCompany(sortedAbout);
        } else {
            console.log('没有获取到关于贝启科技-首页数据或API状态不为ok');
            if (result.msg) {
                console.log(`API返回消息: ${result.msg}`);
            }
        }
    } catch (error) {
        console.error('获取关于贝启科技-首页数据失败:', error);
    }
}

/**
 * 更新关于贝启科技显示
 */
function updateAboutCompany(aboutItems) {
    console.log('开始更新关于贝启科技-首页数据');
    
    const aboutSection = document.querySelector('.about-section');
    if (!aboutSection) {
        console.error('找不到关于贝启科技部分的DOM元素(.about-section)');
        return;
    }
    
    // 找到关于贝启科技内容容器
    const aboutContent = aboutSection.querySelector('.about-content');
    if (!aboutContent) {
        console.error('找不到关于贝启科技内容容器(.about-content)');
        return;
    }
    
    // 找到"了解更多"按钮
    const learnMoreBtn = aboutSection.querySelector('.learn-more');
    if (learnMoreBtn && aboutItems.length > 0 && aboutItems[0].url) {
        // 设置第一个项的URL作为"了解更多"按钮的链接
        learnMoreBtn.href = aboutItems[0].url;
        console.log(`设置"了解更多"按钮链接为: ${aboutItems[0].url}`);
    }
    
    // 如果没有获取到数据，则不更新内容
    if (!aboutItems || aboutItems.length === 0) {
        console.log('没有关于贝启科技-首页数据，保留默认内容');
        return;
    }
    
    console.log(`共获取到 ${aboutItems.length} 个关于贝启科技-首页项目`);
    
    // 清空现有内容
    aboutContent.innerHTML = '';
    
    // 查找第一个有内容的文本项目
    const textItem = aboutItems.find(item => item.content && typeof item.content === 'string' && !item.content.includes('/uploads/') && !item.content.startsWith('http'));
    
    // 特殊查找：标题为"移动端"或"Mobile"的数据项（用于左侧小图）
    const mobileItem = aboutItems.find(item => 
        (item.title === "移动端" || item.title === "Mobile") && 
        (item.image_path || (item.content && (item.content.includes('/uploads/') || item.content.startsWith('http'))))
    );
    
    // 如果在当前语言环境中找不到，尝试从所有items中查找
    const mobileItemAnyLang = !mobileItem ? aboutItems.find(item => 
        (item.title === "移动端" || item.title === "Mobile") && 
        (item.image_path || (item.content && (item.content.includes('/uploads/') || item.content.startsWith('http'))))
    ) : null;
    
    // 查找第一个有图片的项目（用于右侧大图，以及在没有"移动端"项目时用于左侧小图）
    const imageItem = aboutItems.find(item => 
        item.image_path || 
        (item.content && (item.content.includes('/uploads/') || item.content.startsWith('http')))
    );
    
    console.log('找到的文本项目:', textItem ? `ID=${textItem.id}, 标题=${textItem.title}` : '无');
    console.log('找到的移动端项目:', mobileItem ? `ID=${mobileItem.id}, 标题=${mobileItem.title}` : '无');
    console.log('找到的图片项目:', imageItem ? `ID=${imageItem.id}, 标题=${imageItem.title}` : '无');
    
    // 处理文本项目
    if (textItem) {
        console.log('处理文本项目');
        const aboutText = document.createElement('p');
        aboutText.className = 'about-text';
        aboutText.textContent = textItem.content;
        aboutContent.appendChild(aboutText);
    }
    
    // 处理左侧小图（优先使用标题为"移动端"的项目，如果没有则使用第一个有图片的项目）
    const leftImageItem = mobileItem || mobileItemAnyLang || imageItem;
    if (leftImageItem) {
        console.log('处理左侧小图，使用项目:', leftImageItem.title);
        // 获取图片路径 - 优先使用image_path，如果没有则尝试使用content
        let imgSrc = '';
        
        if (leftImageItem.image_path) {
            imgSrc = leftImageItem.image_path;
            console.log(`使用image_path字段作为图片路径: ${imgSrc}`);
        } else if (leftImageItem.content && (leftImageItem.content.includes('/uploads/') || leftImageItem.content.startsWith('http'))) {
            imgSrc = leftImageItem.content;
            console.log(`使用content字段作为图片路径: ${imgSrc}`);
        }
        
        if (imgSrc) {
            const aboutImage = document.createElement('img');
            aboutImage.className = 'about-text-image';
            // 处理图片路径
            const normalizedPath = normalizeImagePath(imgSrc);
            console.log(`左侧小图 - 原始路径: ${imgSrc}`);
            console.log(`左侧小图 - 处理后路径: ${normalizedPath}`);
            
            aboutImage.src = normalizedPath;
            aboutImage.alt = leftImageItem.title || '关于贝启科技';
            
            // 添加错误处理
            aboutImage.onerror = function() {
                console.error(`左侧小图加载失败: ${normalizedPath}`);
                this.onerror = null;
                this.src = '/images/home/<USER>';
                this.style.opacity = 0.5;
            };
            
            aboutContent.appendChild(aboutImage);
        }
    }
    
    // 更新右侧大图（始终使用第一个有图片的项目）
    const rightImage = aboutSection.querySelector('.about-image');
    if (rightImage && imageItem) {
        console.log('处理右侧大图');
        // 使用第一个图片项目的图片路径
        let imgSrc = '';
        
        if (imageItem.image_path) {
            imgSrc = imageItem.image_path;
            console.log(`使用image_path字段作为右侧大图路径: ${imgSrc}`);
        } else if (imageItem.content && (imageItem.content.includes('/uploads/') || imageItem.content.startsWith('http'))) {
            imgSrc = imageItem.content;
            console.log(`使用content字段作为右侧大图路径: ${imgSrc}`);
        }
        
        if (imgSrc) {
            // 处理图片路径
            const normalizedPath = normalizeImagePath(imgSrc);
            console.log(`右侧大图 - 原始路径: ${imgSrc}`);
            console.log(`右侧大图 - 处理后路径: ${normalizedPath}`);
            
            rightImage.src = normalizedPath;
            rightImage.alt = imageItem.title || '贝启科技';
            
            // 添加错误处理
            rightImage.onerror = function() {
                console.error(`右侧大图加载失败: ${normalizedPath}`);
                this.onerror = null;
                this.src = '/images/home/<USER>';
                this.style.opacity = 0.5;
            };
        }
    }
    
    console.log('关于贝启科技-首页数据更新完成');
}

/**
 * 获取并显示方案定制数据
 */
async function fetchSolutionCustomize() {
    try {
        const lang = isEnglishSite() ? 1 : 0;
        console.log(`开始获取方案定制数据，语言: ${lang === 1 ? '英文' : '中文'}`);
        
        const params = new URLSearchParams({
            page: '1',
            page_size: '10',
            filters: JSON.stringify({
                info_type: 'solution_customize',
                show: true,
                lang: lang
            })
        });

        const response = await fetch(`/apis/company_info/list?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`获取方案定制数据失败，HTTP状态码: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.status === 'ok' && result.data) {
            // 确保只处理solution_customize类型的数据
            const solutionItems = result.data.filter(item => item.info_type === 'solution_customize');
            
            // 按显示顺序排序
            const sortedSolutions = sortByDisplayOrder(solutionItems);
            
            // 更新方案定制内容
            updateSolutionCustomize(sortedSolutions);
        } else {
            // 如果没有数据，保持默认内容
            console.log('没有获取到方案定制数据，保持默认内容');
        }
    } catch (error) {
        console.error('获取方案定制数据失败:', error);
    }
}

/**
 * 更新方案定制显示
 */
function updateSolutionCustomize(solutionItems) {
    const solutionSection = document.querySelector('.solution-customize');
    if (!solutionSection) return;

    // 如果没有数据，保持默认内容
    if (!solutionItems || solutionItems.length === 0) return;

    // 查找第一个有效的数据项
    const mainItem = solutionItems[0];
    if (!mainItem) return;

    // 更新标题（如果有）
    const titleElement = solutionSection.querySelector('.solution-customize-title');
    if (titleElement && mainItem.title) {
        titleElement.textContent = mainItem.title;
    }

    // 更新图片（如果有）
    const imageElement = solutionSection.querySelector('.solution-customize-image');
    if (imageElement && mainItem.image_path) {
        const imgSrc = normalizeImagePath(mainItem.image_path);
        imageElement.src = imgSrc;
        imageElement.onerror = function() {
            this.onerror = null;
            this.src = './images/home/<USER>'; // 使用原有的默认图片
        };
    }

    // 更新文本内容（如果有）
    const textElement = solutionSection.querySelector('.solution-customize-text');
    if (textElement && mainItem.content) {
        textElement.textContent = mainItem.content;
    }

    // 更新按钮链接（如果有）
    const buttonElement = solutionSection.querySelector('.customize-button');
    if (buttonElement && mainItem.url) {
        buttonElement.href = mainItem.url;
    }
}

// 在文档加载完成后初始化方案定制
document.addEventListener('DOMContentLoaded', () => {
    // 获取方案定制数据
    fetchSolutionCustomize();
    
    // ... existing code ...
});

 