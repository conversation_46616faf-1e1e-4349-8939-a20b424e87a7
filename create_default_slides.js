const fs = require('fs');
const path = require('path');

// 创建默认轮播图目录和HTML文件
function createDefaultSlides() {
    console.log('开始创建默认轮播图...');
    
    try {
        // 创建目录
        const slidesDir = path.join(__dirname, 'public', 'images', 'slides');
        if (!fs.existsSync(path.join(__dirname, 'public', 'images'))) {
            fs.mkdirSync(path.join(__dirname, 'public', 'images'), { recursive: true });
        }
        if (!fs.existsSync(slidesDir)) {
            console.log(`创建目录: ${slidesDir}`);
            fs.mkdirSync(slidesDir, { recursive: true });
        }
        
        // 默认轮播图数据
        const slides = [
            {
                name: 'slide1.html',
                color: '#3498db', // 蓝色
                text: '轮播图1'
            },
            {
                name: 'slide2.html',
                color: '#2ecc71', // 绿色
                text: '轮播图2'
            },
            {
                name: 'slide3.html',
                color: '#e74c3c', // 红色
                text: '轮播图3'
            }
        ];
        
        // 创建简单的HTML文件
        slides.forEach(slide => {
            const filePath = path.join(slidesDir, slide.name);
            
            // 如果文件不存在，创建一个简单的HTML文件
            if (!fs.existsSync(filePath)) {
                console.log(`创建默认轮播图HTML: ${filePath}`);
                
                // 创建一个简单的HTML文件
                const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${slide.text}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100vh;
            background-color: ${slide.color};
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
        }
        
        .text {
            color: white;
            font-size: 72px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="text">${slide.text}</div>
</body>
</html>
                `;
                
                // 将HTML写入文件
                fs.writeFileSync(filePath, html);
                console.log(`已创建HTML轮播图: ${filePath}`);
            }
        });
        
        // 创建一个index.html文件，说明如何使用这些HTML文件
        const indexPath = path.join(slidesDir, 'index.html');
        if (!fs.existsSync(indexPath)) {
            const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>默认轮播图说明</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        p {
            line-height: 1.6;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>默认轮播图说明</h1>
    <p>这个目录包含了默认的轮播图HTML文件。由于没有实际的图片文件，您可以：</p>
    <ol>
        <li>将这些HTML文件转换为图片（使用浏览器截图或其他工具）</li>
        <li>替换为您自己的图片文件</li>
        <li>在index.html中直接使用iframe引用这些HTML文件</li>
    </ol>
    <p>示例：</p>
    <ul>
        <li><a href="slide1.html" target="_blank">轮播图1</a></li>
        <li><a href="slide2.html" target="_blank">轮播图2</a></li>
        <li><a href="slide3.html" target="_blank">轮播图3</a></li>
    </ul>
</body>
</html>
            `;
            fs.writeFileSync(indexPath, indexHtml);
            console.log(`已创建说明文件: ${indexPath}`);
        }
        
        console.log('默认轮播图创建完成');
    } catch (error) {
        console.error('创建默认轮播图失败:', error);
    }
}

// 执行创建函数
try {
    createDefaultSlides();
} catch (error) {
    console.error('执行创建默认轮播图脚本失败:', error);
} 