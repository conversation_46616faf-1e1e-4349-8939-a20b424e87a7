// 工具栏数据加载和处理
class ToolbarDataManager {
    constructor() {
        // 检测是否在子目录中
        this.isInSubdir = window.location.pathname.split('/').length > 2;
        this.pathPrefix = this.isInSubdir ? '../' : '';
        
        this.qrcodeData = null;
        this.serviceData = null;
        this.phoneData = null;
        this.mobilePhoneData = null;
        this.hideTimeout = null; // 用于存储隐藏定时器
    }

    // 从数据库获取工具栏数据
    async fetchToolbarData() {
        try {
            const response = await fetch('/apis/company_info/list?info_type=toolbar');
            const data = await response.json();
            
            if (data.status === 'ok' && data.data) {
                // 处理获取到的数据
                this.processToolbarData(data.data);
                
                // 初始化小屏幕下的工具栏交互
                this.initSmallScreenToolbar();
            } else {
                console.error('获取工具栏数据失败:', data.msg || '未知错误');
            }
        } catch (error) {
            console.error('获取工具栏数据出错:', error);
        }
    }

    // 初始化小屏幕下的工具栏交互
    initSmallScreenToolbar() {
        // 检查是否是小屏幕
        const isSmallScreen = window.innerWidth <= 491;
        if (!isSmallScreen) return;
        
        // 获取工具栏元素
        const toolbar = document.querySelector('.floating-toolbar');
        if (!toolbar) return;
        
        // 添加收起类
        toolbar.classList.add('toolbar-collapsed');
        
        // 添加CSS样式
        this.addToolbarStyles();
        
        // 添加触摸和鼠标事件
        toolbar.addEventListener('mouseenter', () => this.showToolbar(toolbar));
        toolbar.addEventListener('touchstart', () => this.showToolbar(toolbar));
        toolbar.addEventListener('mouseleave', () => this.hideToolbarWithDelay(toolbar));
        toolbar.addEventListener('touchend', () => this.hideToolbarWithDelay(toolbar));
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            const isSmallScreen = window.innerWidth <= 491;
            if (isSmallScreen) {
                toolbar.classList.add('toolbar-collapsed');
            } else {
                toolbar.classList.remove('toolbar-collapsed');
            }
        });
    }
    
    // 添加工具栏样式
    addToolbarStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            @media screen and (max-width: 491px) {
                .floating-toolbar {
                    transition: transform 0.3s ease;
                }
                
                .floating-toolbar.toolbar-collapsed {
                    transform: translateX(80%);
                }
                
                .floating-toolbar:before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 20%;
                    height: 100%;
                    background: transparent;
                    z-index: 1;
                }
            }
        `;
        document.head.appendChild(styleElement);
    }
    
    // 显示工具栏
    showToolbar(toolbar) {
        // 清除之前的隐藏定时器
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
            this.hideTimeout = null;
        }
        
        // 显示工具栏
        toolbar.classList.remove('toolbar-collapsed');
    }
    
    // 延迟隐藏工具栏
    hideToolbarWithDelay(toolbar) {
        // 设置3秒后隐藏
        this.hideTimeout = setTimeout(() => {
            toolbar.classList.add('toolbar-collapsed');
        }, 3000);
    }

    // 处理工具栏数据
    processToolbarData(data) {
        // 查找企业微信相关的数据
        const qrcodeItems = data.filter(item => 
            item.title && 
            item.title.includes('企业微信') && 
            (item.image_path || item.content)
        );

        // 查找微信客服相关的数据
        const serviceItem = data.find(item => 
            item.title && 
            item.title.includes('微信客服') && 
            (item.image_path || item.content)
        );

        // 查找电话咨询相关的数据
        const phoneItem = data.find(item => 
            item.title && 
            item.title.includes('电话咨询') && 
            item.content
        );

        // 查找移动端电话咨询相关的数据
        const mobilePhoneItem = data.find(item => 
            item.title && 
            item.title.includes('移动端电话咨询') && 
            (item.image_path || item.content)
        );

        if (qrcodeItems && qrcodeItems.length > 0) {
            // 按显示顺序排序
            qrcodeItems.sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
            this.qrcodeData = qrcodeItems;
            
            // 更新二维码图片
            this.updateQrcodeImages();
        }

        if (serviceItem) {
            this.serviceData = serviceItem;
            // 更新客服信息
            this.updateServiceInfo();
        }

        if (phoneItem) {
            this.phoneData = phoneItem;
            // 更新电话咨询信息
            this.updatePhoneInfo();
        }

        if (mobilePhoneItem) {
            this.mobilePhoneData = mobilePhoneItem;
            // 更新移动端电话咨询信息
            this.updateMobilePhoneInfo();
        }
    }

    // 更新电话咨询信息
    updatePhoneInfo() {
        if (!this.phoneData || !this.phoneData.content) return;

        // 获取工作时间容器
        const workTimeContainer = document.querySelector('.phone-popup .work-time');
        if (!workTimeContainer) return;

        // 清空现有内容
        workTimeContainer.innerHTML = '';

        // 将内容按换行符分割
        const lines = this.phoneData.content.split('\n');
        
        // 为每一行创建一个p标签
        lines.forEach(line => {
            if (line.trim()) {  // 只处理非空行
                const p = document.createElement('p');
                p.textContent = line.trim();
                workTimeContainer.appendChild(p);
            }
        });

        // 根据show字段控制可见性
        if (this.phoneData.show === true || this.phoneData.show === 1 || this.phoneData.show === "1") {
            workTimeContainer.style.visibility = 'visible';
        } else {
            workTimeContainer.style.visibility = 'hidden';
        }
    }

    // 更新二维码图片
    updateQrcodeImages() {
        // 获取所有二维码图片元素
        const qrcodeImages = document.querySelectorAll('.qrcode-popup .qrcode-image');
        
        if (!qrcodeImages || qrcodeImages.length === 0) {
            console.warn('未找到二维码图片元素');
            return;
        }

        // 更新每个二维码图片
        qrcodeImages.forEach((img, index) => {
            if (this.qrcodeData[index]) {
                const item = this.qrcodeData[index];
                let imgSrc = '';

                // 优先使用image_path，如果没有则使用content
                if (item.image_path) {
                    imgSrc = this.processImagePath(item.image_path);
                } else if (item.content) {
                    imgSrc = this.processImagePath(item.content);
                }

                if (imgSrc) {
                    img.src = imgSrc;
                    // 设置alt文本
                    img.alt = item.title || '企业微信二维码';
                    
                    // 根据show字段控制可见性
                    const container = img.closest('.qrcode-item');
                    if (container) {
                        if (item.show === true || item.show === 1 || item.show === "1") {
                            container.style.visibility = 'visible';
                            img.style.visibility = 'visible';
                        } else {
                            container.style.visibility = 'hidden';
                            img.style.visibility = 'hidden';
                        }
                    }
                }

                // 添加错误处理
                img.onerror = function() {
                    this.onerror = null;
                    this.src = 'data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'104\' height=\'104\' viewBox=\'0 0 104 104\'%3E%3Crect width=\'104\' height=\'104\' fill=\'%23fff\'/%3E%3C/svg%3E';
                    console.warn(`二维码图片 ${index + 1} 加载失败`);
                };
            }
        });
    }

    // 更新客服信息
    updateServiceInfo() {
        if (!this.serviceData) return;

        // 更新客服标题
        const titleElement = document.querySelector('.phone-popup .phone-title');
        if (titleElement && this.serviceData.content) {
            titleElement.textContent = this.serviceData.content;
        }

        // 更新客服二维码图片
        const serviceImage = document.querySelector('.phone-popup .qrcode-image');
        if (serviceImage && this.serviceData.image_path) {
            const imgSrc = this.processImagePath(this.serviceData.image_path);
            serviceImage.src = imgSrc;
            serviceImage.alt = this.serviceData.title || '微信客服';
            
            // 设置图片样式以确保适应容器
            serviceImage.style.maxWidth = '100%';
            serviceImage.style.maxHeight = '100%';
            serviceImage.style.objectFit = 'contain';

            // 根据show字段控制可见性
            if (this.serviceData.show === true || this.serviceData.show === 1 || this.serviceData.show === "1") {
                serviceImage.style.visibility = 'visible';
            } else {
                serviceImage.style.visibility = 'hidden';
            }

            // 添加错误处理
            serviceImage.onerror = function() {
                this.onerror = null;
                this.src = 'data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'93\' height=\'93\' viewBox=\'0 0 93 93\'%3E%3Crect width=\'93\' height=\'93\' fill=\'%23f0f0f0\'/%3E%3C/svg%3E';
                console.warn('客服二维码图片加载失败');
            };
        }
    }

    // 更新移动端电话咨询信息
    updateMobilePhoneInfo() {
        if (!this.mobilePhoneData) return;

        // 获取电话咨询弹窗
        const phonePopup = document.querySelector('.phone-popup');
        if (!phonePopup) return;

        // 检查是否是移动设备
        const isMobile = window.innerWidth <= 491;
        if (!isMobile) return;

        // 处理移动端图片
        if (this.mobilePhoneData.image_path) {
            const imgSrc = this.processImagePath(this.mobilePhoneData.image_path);
            
            // 创建一个style标签来修改after伪元素的背景图
            const styleElement = document.createElement('style');
            styleElement.textContent = `
                .phone-popup:after {
                    background-image: url('${imgSrc}') !important;
                }
            `;
            document.head.appendChild(styleElement);
        }

        // 根据show字段控制可见性
        if (this.mobilePhoneData.show === true || this.mobilePhoneData.show === 1 || this.mobilePhoneData.show === "1") {
            phonePopup.style.visibility = 'visible';
        } else {
            phonePopup.style.visibility = 'hidden';
        }
    }

    // 处理图片路径
    processImagePath(path) {
        if (!path) return '';
        
        // 处理Windows路径
        if (path.includes('C:')) {
            const fileName = path.split('\\').pop().split('/').pop();
            return this.pathPrefix + 'uploads/' + fileName;
        }
        
        // 处理相对路径
        if (!path.startsWith('http') && !path.startsWith('/')) {
            return this.pathPrefix + path;
        }
        
        return path;
    }
}

// 创建工具栏数据管理器实例
const toolbarManager = new ToolbarDataManager();

// 当DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待floating-toolbar.js创建二维码元素后再获取数据
    setTimeout(() => {
        toolbarManager.fetchToolbarData();
    }, 500);
});

// 导出工具栏管理器以供其他模块使用
window.toolbarManager = toolbarManager; 