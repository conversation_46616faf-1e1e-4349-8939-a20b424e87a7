<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>新闻资讯-Bearkey-官网</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    
    <!-- css -->
    <link href="./css/bootstrap.min.css" rel="stylesheet"/>
    <link href="./css/style.css" rel="stylesheet"/>
    <link href="./css/footer.css" rel="stylesheet"/>
    <link href="./css/news.css" rel="stylesheet"/>
    <link href="./css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="./css/flexslider.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body>
<div id="wrapper" style="display: none;">
    <!-- 导航栏 -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header>

    <!-- 页面标题背景板 -->
    <div class="page-title-banner">
        <div class="container">
            <h1 class="page-title">新闻资讯</h1>
        </div>
    </div>

    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">贝启科技官网</a>
            <span class="separator">/</span>
            <span class="current">贝启科技动态</span>
        </div>

        <!-- 新闻列表区域 -->
        <div class="news-list" id="newsList">
            <!-- 示例新闻项，实际内容将由JS动态加载 -->
            <div class="news-item">
                <div class="news-date">2024.11.20</div>
                <div class="news-content-wrapper">
                    <div class="news-image-container">
                        <img src="./images/news/news1.svg" alt="OpenHarmony人才生态大会2024" class="news-image">
                    </div>
                    <div class="news-info">
                        <h2 class="news-title">
                            <a href="/news-detail.html?id=1">OpenHarmony人才生态大会2024</a>
                        </h2>
                        <div class="news-desc">4月13日，厦门通信科技装备理工大学举进行RK3568 OpenHarmony教学实践互动。在校园中，我们技术团队与一群充满好奇与探索精神的学子们展开了一场关于RK3568 OpenHarmony开发板的深度互动之旅。</div>
                        <a href="/news-detail.html?id=1" class="news-more">了解详情</a>
                    </div>
                </div>
            </div>

            <!-- 示例新闻项 -->
            <div class="news-item">
                <div class="news-date">2024.10.12</div>
                <div class="news-content-wrapper">
                    <div class="news-image-container">
                        <img src="./images/news/news2.svg" alt="贝启科技深耕鸿蒙生态，亮相第三届OpenHarmony技术大会" class="news-image">
                    </div>
                    <div class="news-info">
                        <h2 class="news-title">
                            <a href="/news-detail.html?id=2">贝启科技深耕鸿蒙生态，亮相第三届OpenHarmony技术大会</a>
                        </h2>
                        <div class="news-desc">10月12日，在深圳科技馆举办的第三届OpenHarmony技术大会上，贝启科技作为鸿蒙生态的重要贡献者受邀参展，展示了最新的鸿蒙系统开发板及解决方案。</div>
                        <a href="/news-detail.html?id=2" class="news-more">了解详情</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer>
        <!-- 页脚将通过footer.js动态加载 -->
    </footer>
</div>
<a href="#" class="scrollup" style="display: none;"><i class="fa fa-angle-up active"></i></a>

<!-- javascript -->
<script src="js/jquery.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.fancybox.pack.js"></script>
<script src="js/jquery.fancybox-media.js"></script>
<script src="js/jquery.flexslider.js"></script>
<script src="js/nav.js"></script>
<script src="js/floating-toolbar.js"></script>
<script src="js/toolbar-data.js"></script>
<script src="js/footer.js"></script>
<script src="js/news-data.js"></script>
<script src="js/custom.js"></script>
</body>
</html> 