---
title: External Libs
subTitle: Development
nav: docs
description: Owl Carousel Documentation

sort: 4
tags:
- Development
---

{{#markdown }}
## External Libraries
> Why not try Owl with other fantastic open source libraries? Check the list of plugins I've used in some of the demos.


### animate.css
This is an awesome library created by <PERSON> of CSS3 animations that perfectly works with animate functions.
* [Visit Animate.css website](https://daneden.github.io/animate.css/)
* [See Owl animate demo](/demos/animate.html)

-----

### mouse.wheel.js
A jQuery plugin created by <PERSON> that adds cross-browser mouse wheel support with delta normalization.

* [Visit jQuery Mousewheel website](https://github.com/brandonaaron/jquery-mousewheel)
* [See Owl mousewheel demo](/demos/mousewheel.html)

-----
Let me know if you find any useful plugins that fits to Owl.

{{/markdown }}
