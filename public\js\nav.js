/**
 * 导航栏加载控制系统
 * 自动监听 body 的 navbar-loaded class，一旦检测到导航栏加载完成，就立即显示整个网页（#wrapper）
 */

// 导航栏加载控制器
const NavLoadController = {
    isNavbarLoaded: false,
    contentLoadCallbacks: [],
    observer: null
};

// 导航栏预加载系统 - 提升加载性能
const NavPreloader = {
    cache: {
        styles: null,
        companyInfo: null
    },
    startTime: Date.now()
};

// 立即执行初始化
(function() {
    // 立即隐藏主内容区域
    hideMainContent();

    // 开始监听 navbar-loaded class
    startNavbarLoadedObserver();

    // 预加载导航栏样式
    preloadNavStyles();

    // 当jQuery可用时，预加载公司信息
    const checkJQuery = setInterval(() => {
        if (typeof $ !== 'undefined') {
            clearInterval(checkJQuery);
            preloadCompanyInfo();
        }
    }, 50);

    // 超时保护：2秒后停止检查jQuery
    setTimeout(() => {
        clearInterval(checkJQuery);
    }, 2000);
})();

/**
 * 立即隐藏主内容区域
 * 只要页面有 <div id="wrapper" style="display:none;">，就能自动实现"导航栏加载完再显示主内容"的效果
 */
function hideMainContent() {
    // 动态添加CSS样式，隐藏主内容区域
    const style = document.createElement('style');
    style.id = 'navbar-load-control';
    style.textContent = `
        /* 隐藏主内容区域，直到导航栏加载完成 */
        #wrapper {
            display: none !important;
        }

        /* 其他可能的主内容容器 */
        .page-title-banner,
        .containert,
        main:not(header main),
        .content,
        footer {
            opacity: 0 !important;
            visibility: hidden !important;
        }

        /* 确保导航栏正常显示 */
        header {
            display: block !important;
            min-height: 80px;
            background: #ffffff;
            position: relative;
            z-index: 1000;
        }

        /* 导航栏加载完成后立即显示所有内容 */
        body.navbar-loaded #wrapper {
            display: block !important;
        }

        body.navbar-loaded .page-title-banner,
        body.navbar-loaded .containert,
        body.navbar-loaded main,
        body.navbar-loaded .content,
        body.navbar-loaded footer {
            opacity: 1 !important;
            visibility: visible !important;
        }
    `;

    // 立即添加到head
    document.head.appendChild(style);
}

/**
 * 开始监听 body 的 navbar-loaded class
 * 使用 MutationObserver 实时监听 class 变化
 */
function startNavbarLoadedObserver() {
    // 如果浏览器不支持 MutationObserver，使用轮询方式
    if (!window.MutationObserver) {
        console.log('浏览器不支持 MutationObserver，使用轮询方式监听');
        startPollingObserver();
        return;
    }

    // 创建 MutationObserver 实例
    NavLoadController.observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const target = mutation.target;
                if (target === document.body && target.classList.contains('navbar-loaded')) {
                    console.log('检测到 navbar-loaded class，立即显示主内容');
                    showMainContent();
                }
            }
        });
    });

    // 开始观察 body 元素的 class 属性变化
    NavLoadController.observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
    });

    console.log('开始监听 body 的 navbar-loaded class');
}

/**
 * 轮询方式监听 navbar-loaded class（兼容旧浏览器）
 */
function startPollingObserver() {
    const checkInterval = setInterval(() => {
        if (document.body.classList.contains('navbar-loaded')) {
            console.log('轮询检测到 navbar-loaded class，立即显示主内容');
            clearInterval(checkInterval);
            showMainContent();
        }
    }, 50); // 每50ms检查一次

    // 超时保护：10秒后停止轮询
    setTimeout(() => {
        clearInterval(checkInterval);
    }, 10000);
}

/**
 * 显示主内容区域
 * 一旦检测到导航栏加载完成，就立即显示整个网页
 */
function showMainContent() {
    // 标记导航栏已加载
    NavLoadController.isNavbarLoaded = true;

    // 停止观察器
    if (NavLoadController.observer) {
        NavLoadController.observer.disconnect();
        NavLoadController.observer = null;
    }

    // 立即显示主内容区域
    const wrapper = document.getElementById('wrapper');
    if (wrapper) {
        wrapper.style.display = 'block';
        console.log('#wrapper 已显示');
    }

    // 确保 body 有 navbar-loaded class
    if (!document.body.classList.contains('navbar-loaded')) {
        document.body.classList.add('navbar-loaded');
    }

    // 执行所有回调
    NavLoadController.contentLoadCallbacks.forEach(callback => {
        try {
            callback();
        } catch (error) {
            console.error('内容加载回调执行失败:', error);
        }
    });

    // 清空回调数组
    NavLoadController.contentLoadCallbacks = [];

    console.log('导航栏加载完成，主内容已立即显示');
}

/**
 * 手动触发显示主内容（供外部调用）
 */
function triggerShowMainContent() {
    if (!NavLoadController.isNavbarLoaded) {
        document.body.classList.add('navbar-loaded');
        showMainContent();
    }
}

/**
 * 预加载导航栏样式
 * 立即开始加载CSS，不等待DOM
 */
function preloadNavStyles() {
    // 检查是否已经加载了nav-style.css
    const existingLink = document.querySelector('link[href*="nav-style.css"]');
    if (existingLink) {
        NavPreloader.cache.styles = true;
        return;
    }

    // 创建link元素预加载样式，优先使用绝对路径
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/css/nav-style.css';

    link.onload = function() {
        NavPreloader.cache.styles = true;
        console.log('预加载CSS成功:', link.href);
    };

    link.onerror = function() {
        console.warn('预加载CSS失败，尝试相对路径:', link.href);

        // 如果绝对路径失败，尝试相对路径
        const isInSubdir = window.location.pathname.split('/').length > 2;
        const fallbackLink = document.createElement('link');
        fallbackLink.rel = 'stylesheet';
        fallbackLink.href = `${isInSubdir ? '../' : './'}css/nav-style.css`;

        fallbackLink.onload = function() {
            NavPreloader.cache.styles = true;
            console.log('预加载CSS成功（备选路径）:', fallbackLink.href);
        };

        fallbackLink.onerror = function() {
            NavPreloader.cache.styles = false;
            console.error('预加载CSS失败（备选路径）:', fallbackLink.href);
        };

        document.head.appendChild(fallbackLink);
    };

    // 立即添加到head
    document.head.appendChild(link);
}

/**
 * 预加载公司信息
 * 提前获取logo等公司信息，缓存起来供后续使用
 */
function preloadCompanyInfo() {
    // 获取当前页面信息
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const isEnglish = currentPage.includes('_en');
    const lang = isEnglish ? 1 : 0;

    // 预加载公司logo信息
    $.ajax({
        url: '/apis/company_info/list',
        type: 'GET',
        data: {
            page: 1,
            page_size: 10,
            filters: JSON.stringify({
                info_type: 'company_logo',
                show: true,
                lang: lang
            })
        },
        success: function(response) {
            if (response && response.status === 'ok' && response.data && response.data.length > 0) {
                NavPreloader.cache.companyInfo = response.data[0];
            }
        },
        error: function() {
            NavPreloader.cache.companyInfo = null;
        }
    });
}

// 当文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 先加载样式，加载完成后再初始化导航栏
    loadNavStyles().then(() => {
        // 获取当前页面名称
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const isEnglish = currentPage.includes('_en');

        // 设置HTML的lang属性
        document.documentElement.setAttribute('lang', isEnglish ? 'en' : 'zh-CN');

        // 先加载logo
        loadCompanyLogo(isEnglish, currentPage);

        // 立即触发一次窗口大小检测
        setTimeout(function() {
            handleWindowResize();
        }, 600);
    });
});

/**
 * 获取产品URL，兼容不同的字段名称
 * @param {Object} product 产品对象
 * @returns {string} 产品URL
 */
function getProductUrl(product) {
    // 尝试不同的可能URL字段名
    return product.url || product.detail_url || product.link || '#';
}

/**
 * 处理产品URL路径，确保格式正确
 * @param {string} url 原始URL
 * @returns {string} 处理后的URL
 */
function normalizeProductUrl(url) {
    if (!url) return '#';
    
    // 已经是完整URL的情况
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }
    
    // 如果是相对路径但没有/开头
    if (!url.startsWith('/')) {
        return '/' + url;
    }
    
    return url;
}

// 加载公司logo（优化版：优先使用预加载的数据）
function loadCompanyLogo(isEnglish, currentPage) {
    // 根据当前网站类型确定lang参数
    const lang = isEnglish ? 1 : 0;

    // 优先使用预加载的数据
    if (NavPreloader.cache.companyInfo) {
        console.log('使用预加载的公司信息，加载速度提升');
        const logoData = NavPreloader.cache.companyInfo;
        let logoPath = logoData.image_path || '';

        // 处理图片路径
        if (logoPath) {
            if (logoPath.includes('C:')) {
                const fileName = logoPath.split('\\').pop().split('/').pop();
                logoPath = '/uploads/' + fileName;
            } else if (!logoPath.startsWith('http') && !logoPath.startsWith('/')) {
                logoPath = '/' + logoPath;
            }
        }

        // 加载导航栏数据
        loadNavbarItems(isEnglish, logoPath, currentPage);

        // 设置favicon
        setFavicon(logoPath);
        return;
    }

    // 如果预加载数据不可用，使用原有的API请求方式
    $.ajax({
        url: '/apis/company_info/list',
        type: 'GET',
        data: {
            page: 1,
            page_size: 10,
            filters: JSON.stringify({
                info_type: 'company_logo',
                show: true,
                lang: lang
            })
        },
        success: function(result) {
            if (result.status === 'ok' && result.data && result.data.length > 0) {
                // 获取logo数据
                const logoData = result.data[0];
                let logoPath = logoData.image_path || '';

                // 处理图片路径
                if (logoPath) {
                    if (logoPath.includes('C:')) {
                        const fileName = logoPath.split('\\').pop().split('/').pop();
                        logoPath = '/uploads/' + fileName;
                    } else if (!logoPath.startsWith('http') && !logoPath.startsWith('/')) {
                        logoPath = '/' + logoPath;
                    }
                }

                // 加载导航栏数据
                loadNavbarItems(isEnglish, logoPath, currentPage);

                // 设置favicon
                setFavicon(logoPath);
            } else {
                // 如果没有找到logo数据，使用默认logo
                const defaultLogoPath = 'images/home/<USER>';
                loadNavbarItems(isEnglish, defaultLogoPath, currentPage);

                // 设置默认favicon
                setFavicon(defaultLogoPath);
            }
        },
        error: function() {
            console.error('获取logo数据失败');
            // 发生错误时使用默认logo
            const defaultLogoPath = 'images/home/<USER>';
            loadNavbarItems(isEnglish, defaultLogoPath, currentPage);

            // 设置默认favicon
            setFavicon(defaultLogoPath);
        }
    });
}

// 加载导航栏项目
function loadNavbarItems(isEnglish, logoPath, currentPage) {
    // 根据当前网站类型确定lang参数
    const lang = isEnglish ? 1 : 0;
    
    // 从后端API获取导航栏数据
    $.ajax({
        url: '/apis/company_info/list',
        type: 'GET',
        data: {
            page: 1,
            page_size: 20,
            filters: JSON.stringify({
                info_type: 'navbar',
                show: true,
                lang: lang
            })
        },
        success: function(result) {
            if (result.status === 'ok' && result.data) {
                // 按display_order排序
                const navData = result.data.sort((a, b) => a.display_order - b.display_order);
                // 创建导航栏
                createNavbar(isEnglish, logoPath, currentPage, navData);
            } else {
                // 如果没有数据，使用默认导航项
                createNavbar(isEnglish, logoPath, currentPage, []);
            }
        },
        error: function() {
            console.error('获取导航栏数据失败');
            // 发生错误时使用默认导航项
            createNavbar(isEnglish, logoPath, currentPage, []);
        }
    });
}

// 设置favicon
function setFavicon(logoPath) {
    // 查找favicon元素
    let faviconElement = document.getElementById('favicon');
    
    // 如果没有找到favicon元素，创建一个
    if (!faviconElement) {
        faviconElement = document.createElement('link');
        faviconElement.id = 'favicon';
        faviconElement.rel = 'icon';
        document.head.appendChild(faviconElement);
    }
    
    // 设置favicon的href属性
    faviconElement.href = logoPath;
    
    console.log('Favicon已设置为:', logoPath);
}

// 创建导航栏
function createNavbar(isEnglish, logoPath, currentPage, navData) {
    // 检测是否在子目录中
    const isInSubdir = window.location.pathname.split('/').length > 2;
    // 根据是否在子目录中设置路径前缀
    const pathPrefix = isInSubdir ? '../' : '';
    
    // 检测当前屏幕宽度
    const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    
    // 创建导航项
    let navItems = '';
    
    // 如果有从数据库获取的导航项，使用数据库中的内容
    if (navData && navData.length > 0) {
        navData.forEach(item => {
            const isActive = item.url && currentPage === item.url.split('/').pop();
            const url = item.url || '#';
            const isExternal = url.startsWith('http') || url.startsWith('https');
            
            navItems += `
                <li ${isActive ? 'class="active"' : ''}>
                    <a href="${isExternal ? url : pathPrefix + url}" ${isExternal ? 'target="_blank"' : ''}>
                        ${item.title || '导航项'}
                    </a>
                </li>
            `;
        });
    } else {
        // 使用默认导航项
        navItems += `
            <li ${currentPage === (isEnglish ? 'index_en.html' : 'index.html') ? 'class="active"' : ''}>
                <a href="${pathPrefix}${isEnglish ? 'index_en.html' : 'index.html'}">${isEnglish ? 'Home' : '首页'}</a>
            </li>
            <li ${isInSubdir || currentPage === (isEnglish ? 'product_en.html' : 'product.html') ? 'class="active"' : ''}>
                <a href="${pathPrefix}${isEnglish ? 'product_en.html' : 'product.html'}">${isEnglish ? 'Products' : '产品方案'}</a>
            </li>
            <li ${currentPage === (isEnglish ? 'download_en.html' : 'download.html') ? 'class="active"' : ''}>
                <a href="${pathPrefix}${isEnglish ? 'download_en.html' : 'download.html'}">${isEnglish ? 'Downloads' : '资料下载'}</a>
            </li>
        `;
        
        // 开源社区导航项 - 仅在中文版显示
        if (!isEnglish) {
            navItems += `
                <li>
                    <a href="https://www.bearkey.net/" target="_blank">开源社区</a>
                </li>
                <li ${currentPage === 'wiki.html' ? 'class="active"' : ''}>
                    <a href="${pathPrefix}wiki.html">维基教程</a>
                </li>
            `;
        }
        
        navItems += `
            <li>
                <a href="https://shop467163226.taobao.com/" target="_blank">${isEnglish ? 'Online Shop' : '在线商城'}</a>
            </li>
            <li ${currentPage === (isEnglish ? 'about_en.html' : 'about.html') ? 'class="active"' : ''}>
                <a href="${pathPrefix}${isEnglish ? 'about_en.html' : 'about.html'}">${isEnglish ? 'About Us' : '关于我们'}</a>
            </li>
        `;
    }
    
    // 添加语言切换模块 - 同时添加移动端和桌面端版本，由CSS控制显示/隐藏
    navItems += `
        <li class="mobile-menu-lang">
            <a href="javascript:void(0)" onclick="navJsChgLang()">
                <span class="globe-icon">🌐</span>
                <span class="lang-text">${isEnglish ? 'EN/中文' : '中文/EN'}</span>
            </a>
        </li>
        <li class="lang-switcher-container">
            <div class="lang-switcher">
                <img src="${pathPrefix}images/home/<USER>" alt="language" class="lang-icon">
                <span class="lang-text">${isEnglish ? 'English' : '中文'}</span>
                <i class="lang-arrow ${isEnglish ? 'lang-arrow-en' : ''}"></i>
                <div class="lang-switch">
                    <span class="lang-option">${isEnglish ? '中文' : 'English'}</span>
                </div>
            </div>
        </li>
    `;
    
    // 创建导航栏HTML
    const navbarHtml = `
        <div class="navbar navbar-default navbar-static-top ${screenWidth >= 1920 ? 'navbar-fullscreen' : 'navbar-responsive'}">
            <div class="container">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="${pathPrefix}${isEnglish ? 'index_en.html' : 'index.html'}">
                        <img id="navbar_logo" src="${isInSubdir ? '../' : ''}${logoPath}" alt="logo" onerror="this.src='${isInSubdir ? '../' : ''}images/home/<USER>'"/>
                    </a>
                </div>
                <div class="navbar-collapse collapse">
                    <ul class="nav navbar-nav">
                        ${navItems}
                    </ul>
                </div>
            </div>
        </div>
    `;
    
    // 将导航栏插入到header标签中
    const headerElement = document.querySelector('header');
    if (headerElement) {
        if (headerElement.children.length === 0 || 
            (headerElement.children.length === 1 && headerElement.firstChild.nodeType === Node.COMMENT_NODE)) {
            headerElement.innerHTML = navbarHtml;
            
            // 根据语言环境设置下拉箭头位置
            setTimeout(() => {
                const arrowStyle = document.createElement('style');
                if (isEnglish) {
                    arrowStyle.innerHTML = `
                        .lang-arrow-en {
                            left: 1670px !important;
                        }
                    `;
                }
                document.head.appendChild(arrowStyle);
                
                // 添加响应式样式，根据屏幕宽度调整布局
                addResponsiveStyles(screenWidth);
                
                // 增强导航项的点击和悬停效果
                enhanceNavItems();
                setupLangSwitchEvents();
            }, 500);
            
            // 触发导航栏加载完成，显示主内容
            triggerShowMainContent();

            // 触发自定义事件，通知导航栏已加载完成
            const event = new CustomEvent('navbarLoaded');
            document.dispatchEvent(event);
            console.log('导航栏加载完成，触发navbarLoaded事件');

            // 添加窗口尺寸变化监听，实时调整布局
            window.addEventListener('resize', handleWindowResize);
        }
    }
}

// 窗口尺寸变化处理函数
function handleWindowResize() {
    const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    const navbar = document.querySelector('.navbar');
    
    if (navbar) {
        if (screenWidth >= 1912) {
            navbar.classList.remove('navbar-responsive');
            navbar.classList.add('navbar-fullscreen');
        } else {
            navbar.classList.remove('navbar-fullscreen');
            navbar.classList.add('navbar-responsive');
        }

        // 打印 logo 的样式
        const logo = document.querySelector('.navbar .navbar-header .navbar-brand');
        if (logo) {
            const style = getComputedStyle(logo);
            console.log('logo computed style:', {
                left: style.left,
                position: style.position,
                top: style.top,
                transform: style.transform
            });
        }
    }
}

// 强制应用响应式样式
function applyResponsiveStyles(screenWidth) {
    // 获取所有导航项
    const navItems = document.querySelectorAll('.navbar .nav > li > a');
    // 使用更精确的选择器获取logo，与CSS选择器匹配
    const logo = document.querySelector('.navbar .navbar-header .navbar-brand');
    
    // 根据屏幕宽度添加或移除响应式类
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        if (screenWidth >= 1920) {
            navbar.classList.remove('navbar-responsive');
            navbar.classList.add('navbar-fullscreen');
        } else {
            navbar.classList.remove('navbar-fullscreen');
            navbar.classList.add('navbar-responsive');
        }
    }
}

// 添加响应式样式函数 - 只处理类名切换
function addResponsiveStyles(screenWidth) {
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        if (screenWidth >= 1920) {
            navbar.classList.remove('navbar-responsive');
            navbar.classList.add('navbar-fullscreen');

            // 确保logo也获取正确的样式
            const logo = document.querySelector('.navbar .navbar-header .navbar-brand');
            if (logo) {
                console.log('Full screen mode: Logo found and styled');
            }
        } else {
            navbar.classList.remove('navbar-fullscreen');
            navbar.classList.add('navbar-responsive');

            // 确保logo也获取正确的样式
            const logo = document.querySelector('.navbar .navbar-header .navbar-brand');
            if (logo) {
                console.log('Responsive mode: Logo found and styled');
            }
        }
    }
}

// 设置语言切换按钮的特殊交互
function setupLangSwitchEvents() {
    console.log('开始设置语言切换事件');
    
    // 使用正确的选择器
    const langSwitcherContainer = document.querySelector('.lang-switcher-container');
    if (!langSwitcherContainer) {
        console.error('未找到语言切换容器');
        return;
    }
    
    const langSwitcher = langSwitcherContainer.querySelector('.lang-switcher');
    const langSwitch = langSwitcherContainer.querySelector('.lang-switch');
    const langArrow = langSwitcherContainer.querySelector('.lang-arrow');
    const langOption = langSwitcherContainer.querySelector('.lang-option');
    
    console.log('找到的元素:', {
        container: !!langSwitcherContainer,
        switcher: !!langSwitcher,
        switch: !!langSwitch,
        arrow: !!langArrow,
        option: !!langOption
    });
    
    if (langSwitcher && langSwitch && langArrow) {
        // 移除鼠标悬停事件，改为点击事件
        
        // 点击语言切换器切换下拉菜单
        langSwitcher.addEventListener('click', (e) => {
            console.log('点击语言切换器');
            if (langSwitch.style.display === 'block') {
                langSwitch.style.display = 'none';
                langArrow.style.transform = 'rotate(0deg)';
            } else {
                langSwitch.style.display = 'block';
                langArrow.style.transform = 'rotate(180deg)';
            }
            e.preventDefault();
            e.stopPropagation();
        });
        
        // 点击选项切换语言
        if (langOption) {
            langOption.addEventListener('click', (e) => {
                console.log('点击语言选项');
                langSwitch.style.display = 'none'; // 选择后关闭下拉框
                langArrow.style.transform = 'rotate(0deg)';
                navJsChgLang();
                e.preventDefault();
                e.stopPropagation();
            });
        }
        
        // 点击其他地方关闭下拉框
        document.addEventListener('click', (e) => {
            if (!langSwitcherContainer.contains(e.target)) {
                langSwitch.style.display = 'none';
                langArrow.style.transform = 'rotate(0deg)';
            }
        });
    } else {
        console.error('未找到必要的语言切换元素');
    }
}

// 增强导航项的点击和悬停效果
function enhanceNavItems() {
    // 使用Bootstrap原生的折叠组件控制
    // 不再需要自定义JavaScript来控制菜单的展开和收起
    // Bootstrap会根据data-toggle="collapse"和data-target属性自动处理
    
    // 监听菜单展开/收起状态以控制body滚动
    const navbarToggle = document.querySelector('.navbar-toggle');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggle && navbarCollapse) {
        // 检查是否有jQuery和Bootstrap的JS可用
        if (typeof $ === 'function' && typeof $().collapse === 'function') {
            // 使用Bootstrap事件
            $(navbarCollapse).on('show.bs.collapse', function() {
                document.body.classList.add('menu-open');
            });
            
            $(navbarCollapse).on('hide.bs.collapse', function() {
                document.body.classList.remove('menu-open');
            });
        } else {
            // 兼容方案：使用点击事件监听
            navbarToggle.addEventListener('click', function() {
                setTimeout(function() {
                    if (navbarCollapse.classList.contains('in')) {
                        document.body.classList.add('menu-open');
                    } else {
                        document.body.classList.remove('menu-open');
                    }
                }, 50);
            });
        }
    }
    
    // 如果需要在移动端点击菜单项后自动关闭菜单，可以添加以下代码
    const navItems = document.querySelectorAll('.navbar-nav > li > a');
    
    // 在小屏幕下，点击导航项后自动关闭菜单
    if (navbarCollapse && navItems && window.innerWidth <= 768) {
        navItems.forEach(item => {
            // 只为非语言切换器的常规链接添加点击事件
            if (!item.closest('.lang-switcher-container') && item.getAttribute('href') && !item.getAttribute('href').startsWith('#')) {
                item.addEventListener('click', function() {
                    // 使用Bootstrap的方法关闭菜单
                    // 如果引入了完整的Bootstrap JS，可以使用以下方法
                    if (typeof $().collapse === 'function') {
                        $('.navbar-collapse').collapse('hide');
                    } else {
                        // 如果没有引入完整Bootstrap JS，则使用DOM操作
                        navbarCollapse.classList.remove('in');
                    }
                    
                    // 同时移除body上的menu-open类
                    document.body.classList.remove('menu-open');
                });
            }
        });
    }
}

// 定义特殊页面映射关系
const specialPageMappings = {
    // 中文 -> 英文
    'AI边缘工作站.html': 'AI Edge Workstation_en.html',
    '视频优化盒子.html': 'Video Optimization Box_en.html',
    'RK3399Pro核心板.html': 'RK3399Pro-core-board_en.html',
    'RK3568数据采集网关.html': 'RK3568-data-gateway_en.html',
    'RK3568工业级核心板.html': 'RK3568 industrial-grade core board_en.html',
    'RK3568主板.html': 'RK3568 motherboard_en.html',
    'RK3588主板.html': 'RK3588 motherboard_en.html',
    'RK3576数据采集网关.html': 'RK3576 Data Acquisition Gateway_en.html',
    'RK3588边缘控制网关.html': 'RK3588 Edge Control Gateway_en.html',
    'RK3568工业控制主板.html': 'RK3568 industrial control motherboard_en.html',
    'RK3576核心板.html': 'RK3576 core board_en.html',
    '8英寸平板.html': '8-inch tablet_en.html',
    'RK3576工业控制主板.html': 'RK3576 industrial control motherboard_en.html',
    'RK3588核心板.html': 'RK3588 core board_en.html',
    '10.6英寸平板.html': '10.6-inch tablet_en.html',
    'RK3576商业显示主板.html': 'RK3576 commercial display board_en.html',
    'RK3588工业控制主板.html': 'RK3588 industrial control motherboard_en.html',
    '11英寸平板.html': '11-inch tablet_en.html',
    '智能家居中控屏.html': 'Smart home central control screen_en.html',
    '车载中控屏.html': 'In-vehicle central control screen_en.html',
    '工控屏.html': 'Industrial control screen_en.html',
    '拼接屏处理器.html': 'Splicing screen processor_en.html',
    // 英文 -> 中文
    'AI Edge Workstation_en.html': 'AI边缘工作站.html',
    'Video Optimization Box_en.html': '视频优化盒子.html',
    'RK3399Pro-core-board_en.html': 'RK3399Pro核心板.html',
    'RK3568-data-gateway_en.html': 'RK3568数据采集网关.html',
    'RK3568 industrial-grade core board_en.html': 'RK3568工业级核心板.html',
    'RK3568 motherboard_en.html': 'RK3568主板.html',
    'RK3588 motherboard_en.html': 'RK3588主板.html',
    'RK3576 Data Acquisition Gateway_en.html': 'RK3576数据采集网关.html',
    'RK3588 Edge Control Gateway_en.html': 'RK3588边缘控制网关.html',
    'RK3568 industrial control motherboard_en.html': 'RK3568工业控制主板.html',
    'RK3576 core board_en.html': 'RK3576核心板.html',
    '8-inch tablet_en.html': '8英寸平板.html',
    'RK3576 industrial control motherboard_en.html': 'RK3576工业控制主板.html',
    'RK3588 core board_en.html': 'RK3588核心板.html',
    '10.6-inch tablet_en.html': '10.6英寸平板.html',
    'RK3576 commercial display board_en.html': 'RK3576商业显示主板.html',
    'RK3588 industrial control motherboard_en.html': 'RK3588工业控制主板.html',
    '11-inch tablet_en.html': '11英寸平板.html',
    'Smart home central control screen_en.html': '智能家居中控屏.html',
    'In-vehicle central control screen_en.html': '车载中控屏.html',
    'Industrial control screen_en.html': '工控屏.html',
    'Splicing screen processor_en.html': '拼接屏处理器.html'
};

// 定义语言切换函数
function navJsChgLang() {
    // 获取当前页面的完整URL路径
    const currentPath = window.location.pathname;
    const currentPage = decodeURIComponent(currentPath.split('/').pop() || 'index.html');
    const dirPath = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);
    
    console.log('【语言切换】当前页面详情:', {
        path: currentPath,
        page: currentPage,
        dir: dirPath,
        fullUrl: window.location.href,
        hostname: window.location.hostname
    });
    
    // 检查是否有自定义的chg_lang函数
    // 修复无限递归问题 - 确保不会调用自身或导致循环引用
    if (typeof window.chg_lang === 'function' && 
        window.chg_lang !== navJsChgLang && 
        window.chg_lang.toString().indexOf('navJsChgLang') === -1) {
        console.log('【语言切换】使用页面自定义的语言切换函数');
        window.chg_lang();
        return;
    }

    // 检测当前页面语言
    const isEnglish = currentPage.includes('_en') || 
                       currentPage.includes('-en') || 
                       currentPage.includes(' en') || 
                       currentPage.toUpperCase().includes('_EN');
    
    console.log('【语言切换】当前页面语言:', isEnglish ? '英文' : '中文');
    
    // 语言切换优先级顺序：
    // 1. 首先检查映射表
    // 2. 然后尝试简单的 _en 后缀转换 (index.html <-> index_en.html)
    // 3. 然后进行目录扫描查找匹配页面
    // 4. 最后使用备用方法
    
    // 1. 首先检查映射表
    console.log('【语言切换】步骤1: 检查映射表');
    console.log('【语言切换】当前页面:', currentPage);
    console.log('【语言切换】映射表中是否存在:', specialPageMappings.hasOwnProperty(currentPage));
    
    if (specialPageMappings && specialPageMappings[currentPage]) {
        console.log('【语言切换】找到特殊映射:', currentPage, '->', specialPageMappings[currentPage]);
        const targetPage = specialPageMappings[currentPage];
        const targetUrl = dirPath + encodeURIComponent(targetPage);
        console.log('【语言切换】跳转到:', targetUrl);
        window.location.href = targetUrl;
        return;
    }
    
    // 2. 尝试简单的 _en 后缀转换
    console.log('【语言切换】步骤2: 尝试简单的_en后缀转换');
    let simpleTargetPage;
    
    if (isEnglish) {
        // 从英文到中文：移除 _en 等后缀
        simpleTargetPage = currentPage.replace(/(_en|_EN|-en|-EN|\sen|\(en\))/i, '');
    } else {
        // 特殊处理主页
        if (currentPage === 'index.html') {
            simpleTargetPage = 'index_en.html';
        } else {
            // 从中文到英文：添加 _en 后缀
            simpleTargetPage = currentPage.replace(/\.html$/, '_en.html');
        }
    }
    
    // 确保没有双重扩展名
    if (simpleTargetPage.endsWith('.html.html')) {
        simpleTargetPage = simpleTargetPage.replace(/\.html\.html$/, '.html');
    }
    
    console.log('【语言切换】简单转换结果:', {
        from: currentPage,
        to: simpleTargetPage,
        检查URL: dirPath + encodeURIComponent(simpleTargetPage)
    });
    
    // 检查简单转换后的页面是否存在
    console.log('【语言切换】检查简单转换页面是否存在...');
    checkPageExists(dirPath + encodeURIComponent(simpleTargetPage))
        .then(exists => {
            console.log('【语言切换】简单转换页面存在性检查结果:', exists);
            
            if (exists) {
                // 简单转换的页面存在，直接跳转
                console.log('【语言切换】简单转换的目标页面存在，跳转到:', dirPath + encodeURIComponent(simpleTargetPage));
                window.location.href = dirPath + encodeURIComponent(simpleTargetPage);
            } else {
                console.log('【语言切换】简单转换的目标页面不存在，尝试目录扫描');
                // 3. 尝试目录扫描查找匹配页面
                console.log('【语言切换】步骤3: 开始目录扫描');
                findMatchingLanguagePage(dirPath, currentPage, isEnglish)
                    .then(targetPage => {
                        console.log('【语言切换】目录扫描结果:', targetPage);
                        
                        if (targetPage) {
                            // 找到匹配的页面，跳转
                            console.log('【语言切换】目录扫描找到匹配页面，跳转到:', dirPath + encodeURIComponent(targetPage));
                            window.location.href = dirPath + encodeURIComponent(targetPage);
                        } else {
                            // 4. 最后使用备用方法
                            console.log('【语言切换】步骤4: 使用备用方法');
                            console.log('【语言切换】目录扫描未找到匹配页面，使用备用方法');
                            backupLanguageSwitch(dirPath, currentPage, isEnglish);
                        }
                    })
                    .catch(error => {
                        console.error('【语言切换】目录扫描出错:', error);
                        // 出错时使用备用方法
                        console.log('【语言切换】扫描出错，使用备用方法');
                        backupLanguageSwitch(dirPath, currentPage, isEnglish);
                    });
            }
        })
        .catch(error => {
            console.error('【语言切换】检查页面存在性出错:', error);
            // 出错时使用备用方法
            console.log('【语言切换】检查出错，使用备用方法');
            backupLanguageSwitch(dirPath, currentPage, isEnglish);
        });
}

/**
 * 在目录中查找匹配的语言页面
 * @param {string} dirPath 目录路径
 * @param {string} currentPage 当前页面名
 * @param {boolean} isEnglish 当前是否为英文
 * @returns {Promise<string|null>} 匹配的页面名称或null
 */
function findMatchingLanguagePage(dirPath, currentPage, isEnglish) {
    return new Promise((resolve, reject) => {
        // 获取不带后缀和语言标记的基本名称
        let baseName = getBasePageName(currentPage, isEnglish);
        
        console.log('基本页面名称:', baseName);
        
        // 加载当前目录下的所有HTML文件列表
        $.ajax({
            url: dirPath + '?_=' + new Date().getTime(),
            type: 'GET',
            success: function(data) {
                try {
                    // 提取目录列表中的所有HTML文件
                    const htmlFiles = [];
                    const regex = /href="([^"]+\.html)"/g;
                    let match;
                    
                    while ((match = regex.exec(data)) !== null) {
                        const fileName = match[1];
                        // 只处理HTML文件，排除目录
                        if (fileName.endsWith('.html') && !fileName.includes('/')) {
                            htmlFiles.push(fileName);
                        }
                    }
                    
                    console.log('目录中的HTML文件:', htmlFiles);
                    
                    // 在文件列表中查找匹配的语言版本
                    const targetPage = findTargetLanguagePage(htmlFiles, baseName, currentPage, isEnglish);
                    
                    resolve(targetPage);
                } catch (error) {
                    console.error('解析目录内容失败:', error);
                    reject(error);
                }
            },
            error: function(xhr, status, error) {
                console.error('获取目录内容失败:', error);
                reject(error);
            }
        });
    });
}

/**
 * 从HTML文件列表中找到目标语言页面
 * @param {Array<string>} htmlFiles HTML文件列表
 * @param {string} baseName 基本名称
 * @param {string} currentPage 当前页面
 * @param {boolean} isEnglish 当前是否为英文
 * @returns {string|null} 目标页面名称或null
 */
function findTargetLanguagePage(htmlFiles, baseName, currentPage, isEnglish) {
    // 如果当前是英文版，查找中文版
        if (isEnglish) {
        // 按匹配度排序的可能中文版文件名模式
        const possiblePatterns = [
            // 精确匹配：使用提取的基础名称
            new RegExp('^' + escapeRegExp(baseName) + '\\.html$', 'i'),
            // 部分匹配：基础名称可能有变化
            new RegExp(escapeRegExp(baseName.replace(/[_\s-]/g, '')), 'i')
        ];
        
        // 查找最佳匹配
        for (const pattern of possiblePatterns) {
            const matches = htmlFiles.filter(file => {
                // 排除当前文件和英文文件
                return file !== currentPage && 
                       !file.includes('_en') && 
                       !file.includes('-en') && 
                       !file.includes(' en') && 
                       !file.toUpperCase().includes('_EN') &&
                       pattern.test(file);
            });
            
            if (matches.length > 0) {
                console.log('找到可能的中文匹配:', matches);
                // 如果有多个匹配，选择名称最短的（通常是最简洁的命名）
                return matches.sort((a, b) => a.length - b.length)[0];
            }
        }
        } else {
        // 当前是中文版，查找英文版
        // 英文版文件的可能格式
        const englishSuffixes = ['_en.html', '-en.html', ' en.html', '_EN.html'];
        
        // 首先查找基于当前文件名的精确英文版
        for (const suffix of englishSuffixes) {
            const exactEnglishName = baseName + suffix;
            if (htmlFiles.includes(exactEnglishName)) {
                console.log('找到精确英文匹配:', exactEnglishName);
                return exactEnglishName;
            }
        }
        
        // 查找相似名称的英文版
        const englishFiles = htmlFiles.filter(file => {
            return file !== currentPage && 
                  (file.includes('_en') || 
                   file.includes('-en') || 
                   file.includes(' en') || 
                   file.toUpperCase().includes('_EN'));
        });
        
        if (englishFiles.length > 0) {
            // 查找名称相似度最高的英文文件
            const mostSimilar = findMostSimilarFile(baseName, englishFiles);
            if (mostSimilar) {
                console.log('找到最相似的英文匹配:', mostSimilar);
                return mostSimilar;
            }
        }
    }
    
    // 没有找到匹配
    return null;
}

/**
 * 查找与基准名称最相似的文件
 * @param {string} baseName 基准名称
 * @param {Array<string>} files 文件列表
 * @returns {string|null} 最相似的文件名或null
 */
function findMostSimilarFile(baseName, files) {
    if (files.length === 0) return null;
    
    // 将基准名称转换为小写并移除特殊字符，便于比较
    const normalizedBaseName = baseName.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    // 计算每个文件的相似度分数
    const scoredFiles = files.map(file => {
        // 获取不带后缀的文件名，并转换为小写
        const fileName = file.replace(/\.html$/, '').toLowerCase();
        // 移除语言标记和特殊字符
        const normalizedName = fileName.replace(/_en|-en|_EN|-EN|\sen/g, '').replace(/[^a-z0-9]/g, '');
        
        // 计算相似度分数 (使用最长公共子序列算法)
        const similarityScore = calculateSimilarity(normalizedBaseName, normalizedName);
        
        return {
            file: file,
            score: similarityScore
        };
    });
    
    // 按相似度分数排序，取最高分
    scoredFiles.sort((a, b) => b.score - a.score);
    
    console.log('文件相似度评分:', scoredFiles);
    
    // 返回最相似的文件
    return scoredFiles[0].score > 0.3 ? scoredFiles[0].file : null; // 阈值0.3
}

/**
 * 计算两个字符串的相似度 (0-1)
 * @param {string} str1 字符串1
 * @param {string} str2 字符串2
 * @returns {number} 相似度分数 (0-1)
 */
function calculateSimilarity(str1, str2) {
    // 简单的实现：计算共同字符的比例
    const set1 = new Set(str1);
    const set2 = new Set(str2);
    
    // 计算交集大小
    const intersection = new Set([...set1].filter(char => set2.has(char)));
    
    // 计算并集大小
    const union = new Set([...set1, ...set2]);
    
    // Jaccard相似系数
    return intersection.size / union.size;
}

/**
 * 备用语言切换方法
 * @param {string} dirPath 目录路径
 * @param {string} currentPage 当前页面
 * @param {boolean} isEnglish 当前是否为英文
 */
function backupLanguageSwitch(dirPath, currentPage, isEnglish) {
    console.log('使用备用语言切换方法');
    
    // 尝试首页作为最后的备用方案
    const basePage = isEnglish ? 'index.html' : 'index_en.html';
    console.log('无法找到对应语言页面，跳转到首页:', basePage);
    
    // 处理目录结构
    const baseDir = dirPath === '/' ? '/' : dirPath.includes('../') ? '../' : './';
    window.location.href = baseDir + basePage;
}

/**
 * 获取不带后缀和语言标记的基本页面名称
 * @param {string} pageName 页面名称
 * @param {boolean} isEnglish 是否为英文
 * @returns {string} 基本页面名称
 */
function getBasePageName(pageName, isEnglish) {
    let baseName = pageName;
    
    // 移除.html后缀
    baseName = baseName.replace(/\.html$/, '');
    
    // 如果是英文，移除语言标记
    if (isEnglish) {
        baseName = baseName
            .replace(/_en$|-en$|\sen$|\(en\)$|_EN$|-EN$/i, '')
            .replace(/\s+/g, '_'); // 替换空格为下划线，便于匹配
    }
    
    return baseName;
}

/**
 * 转义正则表达式特殊字符
 * @param {string} string 要转义的字符串
 * @returns {string} 转义后的字符串
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 检查页面是否存在
 * @param {string} url 要检查的URL
 * @returns {Promise<boolean>} 页面是否存在
 */
function checkPageExists(url) {
    console.log('【检查页面】检查URL是否存在:', url);
    return new Promise(resolve => {
        try {
            fetch(url, { method: 'HEAD', cache: 'no-store' })
                .then(response => {
                    console.log('【检查页面】HTTP响应状态码:', response.status);
                    resolve(response.ok);
                })
                .catch(error => {
                    console.error('【检查页面】fetch错误:', error);
                    resolve(false);
                });
        } catch (e) {
            console.error('【检查页面】异常:', e);
            resolve(false);
        }
    });
}

// 保持原来的全局chg_lang定义，但只有在未定义时才创建
// 修复无限递归问题 - 移除对navJsChgLang的循环引用
if (typeof window.chg_lang !== 'function') {
    window.chg_lang = function() {
        // 直接实现语言切换逻辑，而不是调用navJsChgLang
        const currentPath = window.location.pathname;
        const currentPage = decodeURIComponent(currentPath.split('/').pop() || 'index.html');
        const dirPath = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);
        const isEnglish = currentPage.includes('_en') || 
                        currentPage.includes('-en') || 
                        currentPage.includes(' en') || 
                        currentPage.toUpperCase().includes('_EN');
        
        // 简单的转换，从index.html到index_en.html或相反
        let targetPage;
        if (isEnglish) {
            targetPage = currentPage.replace(/(_en|_EN|-en|-EN|\sen|\(en\))/i, '');
        } else {
            targetPage = currentPage.replace(/\.html$/, '_en.html');
        }
        
        if (targetPage.endsWith('.html.html')) {
            targetPage = targetPage.replace(/\.html\.html$/, '.html');
        }
        
        window.location.href = dirPath + encodeURIComponent(targetPage);
    }
}

// 添加切换语言下拉框的函数
function toggleLangSwitch() {
    const langSwitch = document.querySelector('.lang-switch');
    const langArrow = document.querySelector('.lang-arrow');
    
    if (langSwitch.style.display === 'block') {
        langSwitch.style.display = 'none';
        langArrow.style.transform = 'rotate(0deg)';
    } else {
        langSwitch.style.display = 'block';
        langArrow.style.transform = 'rotate(180deg)';
    }
}

// 添加点击其他地方关闭下拉框的功能
document.addEventListener('click', function(event) {
    const langSwitch = document.querySelector('.lang-switch');
    const langIcon = document.querySelector('.lang-icon');
    const langText = document.querySelector('.lang-text');
    const langArrow = document.querySelector('.lang-arrow');
    
    if (!event.target.matches('.lang-icon') && 
        !event.target.matches('.lang-text') && 
        !event.target.matches('.lang-arrow') && 
        !event.target.matches('.lang-option')) {
        langSwitch.style.display = 'none';
        langArrow.style.transform = 'rotate(0deg)';
    }
});

// 修改loadNavStyles函数，返回Promise（优化版：优先使用预加载的样式）
function loadNavStyles() {
    return new Promise((resolve, reject) => {
        // 优先检查预加载的样式是否已经完成
        if (NavPreloader.cache.styles === true) {
            console.log('使用预加载的导航栏样式，加载速度提升');
            resolve();
            return;
        }

        // 检查是否已经加载了nav-style.css
        const existingLink = document.querySelector('link[href*="nav-style.css"]');
        if (existingLink) {
            // 如果已经加载，直接返回
            resolve();
            return;
        }

        // 如果预加载失败，创建新的link元素
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';

        // 检测是否在子目录中
        const isInSubdir = window.location.pathname.split('/').length > 2;
        // 优先使用绝对路径，避免相对路径问题
        const cssPath = '/css/nav-style.css';
        link.href = cssPath;

        console.log('尝试加载CSS:', cssPath);

        // 监听样式表加载完成事件
        link.onload = () => {
            console.log('Nav styles loaded successfully from:', cssPath);
            resolve();
        };

        // 监听加载失败事件
        link.onerror = () => {
            console.error('Failed to load nav-style.css from:', cssPath);

            // 如果绝对路径失败，尝试相对路径作为备选
            const fallbackPath = `${isInSubdir ? '../' : ''}css/nav-style.css`;
            console.log('尝试备选路径:', fallbackPath);

            const fallbackLink = document.createElement('link');
            fallbackLink.rel = 'stylesheet';
            fallbackLink.type = 'text/css';
            fallbackLink.href = fallbackPath;

            fallbackLink.onload = () => {
                console.log('Nav styles loaded successfully from fallback:', fallbackPath);
                resolve();
            };

            fallbackLink.onerror = () => {
                console.error('Failed to load nav-style.css from fallback:', fallbackPath);
                // 即使加载失败也继续执行，使用默认样式
                resolve();
            };

            document.head.appendChild(fallbackLink);
        };

        // 添加到head中
        document.head.appendChild(link);
    });
}

/**
 * ========================================
 * 导航栏加载控制系统 API
 * ========================================
 *
 * 使用说明：
 * 1. 在页面中添加 <div id="wrapper" style="display:none;">包裹主内容
 * 2. nav.js 会自动监听 body 的 navbar-loaded class
 * 3. 一旦检测到导航栏加载完成，就会立即显示整个网页（#wrapper）
 *
 * 外部API：
 * - triggerShowMainContent(): 手动触发显示主内容
 * - NavLoadController.isNavbarLoaded: 检查导航栏是否已加载
 *
 * 事件：
 * - 'navbarLoaded': 导航栏加载完成时触发的自定义事件
 *
 * 示例HTML结构：
 * <body>
 *   <header>
 *     <!-- 导航栏内容会自动插入这里 -->
 *   </header>
 *   <div id="wrapper" style="display:none;">
 *     <!-- 主内容区域 -->
 *     <main>...</main>
 *     <footer>...</footer>
 *   </div>
 * </body>
 */

// 暴露API到全局作用域
window.NavLoadController = NavLoadController;
window.triggerShowMainContent = triggerShowMainContent;

/**
 * 产品导航弹窗功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 创建产品导航弹窗
    createProductPopup();
    
    // 绑定鼠标事件
    bindProductPopupEvents();
});

/**
 * 创建产品导航弹窗
 */
function createProductPopup() {
    // 创建弹窗DOM结构
    const popup = document.createElement('div');
    popup.className = 'product-popup';
    popup.id = 'productPopup';
    
    // 添加初始样式 - 只设置透明度相关属性
    popup.style.display = 'none';
    popup.style.opacity = '0';
    popup.style.transition = 'opacity 0.5s ease';
    
    // 创建网格布局
    const grid = document.createElement('div');
    grid.className = 'product-popup-grid';
    
    // 获取当前语言
    const isEnglish = document.documentElement.lang === 'en' || 
                      window.location.pathname.includes('_en.html') ||
                      window.location.hostname.includes('en.') || 
                      window.location.search.includes('lang=en');
    const lang = isEnglish ? 1 : 0;
    const navType = isEnglish ? 'navigation' : '导航';
    
    // 从API获取导航类型的数据
    $.ajax({
        type: "get",
        url: "/apis/product_list/",
        data: { info_type: navType, lang: lang },
        success: function(response) {
            if (response.status === 'ok' && response.data && response.data.length > 0) {
                // 过滤掉名称为"全部"或"ALL"的项
                let navItems = response.data.filter(item => {
                    return item.name !== '全部' && item.name !== 'ALL';
                });
                
                // 按display_order排序
                navItems.sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
                
                // 确保必需的分类存在
                navItems = ensureRequiredCategories(navItems, isEnglish);
                
                // 如果没有足够的分类，使用默认分类补充
                const defaultCategories = getDefaultProductCategories();
                while (navItems.length < 6 && defaultCategories.length > navItems.length) {
                    // 检查该标题是否已存在
                    const existingTitles = navItems.map(item => item.name);
                    const categoryToAdd = defaultCategories[navItems.length];
                    
                    if (!existingTitles.includes(categoryToAdd.title)) {
                        navItems.push({
                            name: categoryToAdd.title,
                            items: categoryToAdd.items
                        });
                    }
                }
                
                // 限制最多6个分类
                navItems = navItems.slice(0, 6);
                
                console.log('获取到的分类数据:', navItems);
                
                // 生成网格内容 - 先添加所有标题
                navItems.forEach(category => {
                    // 创建标题元素
                    const title = document.createElement('div');
                    title.className = 'product-popup-title';
                    title.innerHTML = `<span>${category.name}</span>`;
                    
                    // 添加点击事件，点击后跳转到产品页面并选中对应分类
                    title.addEventListener('click', function() {
                        // 构建目标URL，添加filter参数
                        const targetPage = isEnglish ? 'product_en.html' : 'product.html';
                        const filter = category.name;
                        
                        // 检查当前是否在子目录中
                        const currentPath = window.location.pathname;
                        const isInSubdir = currentPath.split('/').length > 2;
                        
                        // 构建正确的路径
                        let targetUrl;
                        if (isInSubdir) {
                            // 如果在子目录中，需要返回到根目录
                            targetUrl = `${window.location.origin}/${targetPage}?filter=${encodeURIComponent(filter)}`;
                        } else {
                            // 如果在主目录中，使用相对路径
                            targetUrl = `${targetPage}?filter=${encodeURIComponent(filter)}`;
                        }
                        
                        // 跳转到目标页面
                        window.location.href = targetUrl;
                    });
                    
                    // 添加鼠标悬停样式
                    title.style.cursor = 'pointer';
                    
                    grid.appendChild(title);
                });
                
                // 加载产品数据
                fetchProductsByCategories(navItems, grid, isEnglish);
            } else {
                console.error('获取分类数据失败，使用默认数据');
                // 使用默认数据
                const productCategories = getDefaultProductCategories();
                
                // 生成网格内容 - 先添加所有标题
                productCategories.forEach(category => {
                    // 创建标题元素
                    const title = document.createElement('div');
                    title.className = 'product-popup-title';
                    title.innerHTML = `<span>${category.title}</span>`;
                    grid.appendChild(title);
                });
                
                // 添加产品内容区域
                productCategories.forEach(category => {
                    // 创建内容容器
                    const content = document.createElement('div');
                    content.className = 'product-popup-content';
                    
                    // 添加产品项
                    category.items.forEach(item => {
                        const link = document.createElement('a');
                        link.href = '#'; // 默认链接
                        link.textContent = typeof item === 'string' ? item : item.name; // 使用产品名称
                        content.appendChild(link);
                    });
                    
                    grid.appendChild(content);
                });
            }
            
            // 将网格添加到弹窗
            popup.appendChild(grid);
            
            // 将弹窗添加到body
            document.body.appendChild(popup);
        },
        error: function(error) {
            console.error('获取分类数据失败:', error);
            // 使用默认数据
            const productCategories = getDefaultProductCategories();
            
            // 生成网格内容 - 先添加所有标题
            productCategories.forEach(category => {
                // 创建标题元素
                const title = document.createElement('div');
                title.className = 'product-popup-title';
                title.innerHTML = `<span>${category.title}</span>`;
                grid.appendChild(title);
            });
            
            // 添加产品内容区域
            productCategories.forEach(category => {
                // 创建内容容器
                const content = document.createElement('div');
                content.className = 'product-popup-content';
                
                // 添加产品项
                category.items.forEach(item => {
                    const link = document.createElement('a');
                    link.href = '#'; // 默认链接
                    link.textContent = typeof item === 'string' ? item : item.name; // 使用产品名称
                    content.appendChild(link);
                });
                
                grid.appendChild(content);
            });
            
            // 将网格添加到弹窗
            popup.appendChild(grid);
            
            // 将弹窗添加到body
            document.body.appendChild(popup);
        }
    });
}

/**
 * 根据分类获取产品数据
 * @param {Array} categories 分类数据
 * @param {HTMLElement} grid 网格容器
 * @param {boolean} isEnglish 是否为英文环境
 */
function fetchProductsByCategories(categories, grid, isEnglish) {
    const lang = isEnglish ? 1 : 0;
    const navType = isEnglish ? 'navigation' : '导航';
    
    // 获取所有产品数据
    $.ajax({
        type: "get",
        url: "/apis/product_list/",
        data: { 
            size: 1000,  // 获取足够多的产品数据
            info_type_not: navType, // 排除导航类型
            lang: lang // 根据当前语言获取产品数据
        },
        success: function(response) {
            if (response.status === 'ok' && response.data) {
                // 首先过滤掉导航类型的产品
                const filteredData = response.data.filter(product => 
                    product.info_type !== '导航' && 
                    product.info_type !== 'navigation'
                );
                
                console.log('过滤后的产品数据:', filteredData.length, '/', response.data.length);
                
                // 创建中英文信息类型映射表（双向映射）
                const typeMapping = {
                    '核心板': 'Core Board',
                    '主板': 'Mainboard',
                    '终端': 'Terminal',
                    '方案': 'Solution',
                    'AIOT解决方案': 'AIOT solution',
                    '智能设备': 'Smart Device',
                    '配件': 'Accessory',
                    'OpenHarmony': 'OpenHarmony',
                    'MineHarmony': 'MineHarmony'
                };
                
                // 反向映射表（英文到中文）
                const reverseMapping = {};
                for (const cnType in typeMapping) {
                    reverseMapping[typeMapping[cnType]] = cnType;
                }
                
                // 产品数据按类型分组
                const productsByCategory = {};
                
                // 初始化所有类别的产品数组
                categories.forEach(category => {
                    productsByCategory[category.name] = [];
                });
                
                // 过滤和分类产品
                filteredData.forEach(product => {
                    // 调试日志：显示产品完整数据
                    console.log('处理产品:', { 
                        id: product.id,
                        name: product.name, 
                        info_type: product.info_type,
                        url: product.url,
                        show: product.show
                    });
                    
                    // 确保产品是显示状态
                    if (product.show !== true && product.show !== 1 && product.show !== '1') {
                        return;
                    }
                    
                    // 检查产品类型是否匹配每个分类 - 不使用break，允许产品归入多个分类
                    if (product.info_type) {
                        for (const category of categories) {
                            const categoryName = category.name;
                            
                            // 如果产品info_type包含分类名称，则归入该分类
                            if (product.info_type.includes(categoryName) || 
                                product.name.includes(categoryName)) {
                                productsByCategory[categoryName].push(product);
                            }
                            
                            // 双向匹配：如果是英文环境，检查中文匹配；如果是中文环境，检查英文匹配
                            const translatedType = isEnglish ? 
                                reverseMapping[categoryName] : // 英文环境查找对应的中文类型
                                typeMapping[categoryName];     // 中文环境查找对应的英文类型
                                
                            if (translatedType && (
                                product.info_type.includes(translatedType) || 
                                product.name.includes(translatedType))) {
                                productsByCategory[categoryName].push(product);
                            }
                        }
                    }
                });
                
                // 对每个分类的产品进行去重
                for (const categoryName in productsByCategory) {
                    // 使用产品ID作为唯一标识进行去重
                    const uniqueProducts = [];
                    const seenIds = new Set();
                    
                    productsByCategory[categoryName].forEach(product => {
                        if (!seenIds.has(product.id)) {
                            seenIds.add(product.id);
                            uniqueProducts.push(product);
                        }
                    });
                    
                    // 更新为去重后的产品列表
                    productsByCategory[categoryName] = uniqueProducts;
                }
                
                console.log('分类后的产品:', productsByCategory);
                
                // 添加分类调试日志
                console.log('产品分类结果:', Object.keys(productsByCategory).map(categoryName => {
                    return {
                        category: categoryName,
                        productCount: productsByCategory[categoryName].length,
                        products: productsByCategory[categoryName].map(p => p.name).slice(0, 3) // 只显示前三个产品名称
                    };
                }));
                
                // 添加导航栏分类统计日志
                console.log('======= 导航栏分类统计 =======');
                for (const categoryName in productsByCategory) {
                    const products = productsByCategory[categoryName];
                    console.log(`${categoryName}: 共${products.length}个产品`);
                    if (products.length > 0) {
                        console.log('产品列表:', products.map(p => p.name));
                    }
                }
                console.log('==============================');

                // 为每个分类添加产品内容
                categories.forEach(category => {
                    // 创建内容容器
                    const content = document.createElement('div');
                    content.className = 'product-popup-content';
                    
                    // 获取该分类的产品
                    const products = productsByCategory[category.name] || [];
                    
                    // 如果没有产品，则使用默认产品数据
                    if (products.length === 0) {
                        const defaultCategories = getDefaultProductCategories();
                        const defaultCategory = defaultCategories.find(c => 
                            c.title === category.name || 
                            (isEnglish && reverseMapping[c.title] === category.name) ||
                            (!isEnglish && typeMapping[c.title] === category.name)
                        );
                        
                        if (defaultCategory) {
                            defaultCategory.items.forEach(item => {
                                const link = document.createElement('a');
                                // 处理URL
                                if (typeof item === 'string') {
                                    link.href = '#';
                                } else {
                                    link.href = normalizeProductUrl(item.url);
                                }
                                link.textContent = typeof item === 'string' ? item : item.name;
                                content.appendChild(link);
                            });
                        }
                    } else {
                        // 使用API获取的产品数据
                        products.forEach(product => {
                            // 调试日志：显示即将创建链接的产品数据
                            console.log('创建产品链接:', {
                                name: product.name,
                                url: getProductUrl(product),
                                category: category.name
                            });
                            
                            const link = document.createElement('a');
                            link.href = normalizeProductUrl(getProductUrl(product));
                            link.textContent = product.name || (isEnglish ? 'Unnamed Product' : '未命名产品');
                            content.appendChild(link);
                        });
                    }
                    
                    grid.appendChild(content);
                });
            } else {
                // 加载失败时使用默认数据
                loadDefaultProductContent(categories, grid, isEnglish);
            }
        },
        error: function(error) {
            console.error('获取产品数据失败:', error);
            // 加载失败时使用默认数据
            loadDefaultProductContent(categories, grid, isEnglish);
        }
    });
}

/**
 * 加载默认产品内容
 * @param {Array} categories 分类数据
 * @param {HTMLElement} grid 网格容器
 * @param {boolean} isEnglish 是否为英文环境
 */
function loadDefaultProductContent(categories, grid, isEnglish) {
    const defaultCategories = getDefaultProductCategories();
    
    categories.forEach(category => {
        // 创建内容容器
        const content = document.createElement('div');
        content.className = 'product-popup-content';
        
        // 查找对应的默认分类
        const defaultCategory = defaultCategories.find(c => 
            c.title === category.name || 
            (c.title.toLowerCase() === category.name.toLowerCase())
        );
        
        if (defaultCategory) {
            defaultCategory.items.forEach(item => {
                const link = document.createElement('a');
                // 处理URL
                if (typeof item === 'string') {
                    link.href = '#';
                } else {
                    link.href = normalizeProductUrl(item.url);
                }
                link.textContent = typeof item === 'string' ? item : item.name;
                content.appendChild(link);
            });
        } else {
            // 如果找不到对应的默认分类，添加一个提示
            const notice = document.createElement('span');
            notice.textContent = isEnglish ? 'No products available' : '暂无相关产品';
            notice.style.color = '#999';
            content.appendChild(notice);
        }
        
        grid.appendChild(content);
    });
}

/**
 * 绑定产品弹窗相关的鼠标事件
 */
function bindProductPopupEvents() {
    // 等待弹窗创建完成后再绑定事件
    const checkPopupInterval = setInterval(() => {
        const popup = document.getElementById('productPopup');
        if (popup) {
            clearInterval(checkPopupInterval);
            
            // 定义弹窗隐藏延时器
            let hideTimeout;
            
            // 显示弹窗的函数 - 只处理透明度
            const showPopup = () => {
                clearTimeout(hideTimeout);
                // 先显示元素
                popup.style.display = 'block';
                // 强制浏览器重排后再设置透明度，确保过渡效果生效
                popup.offsetHeight;
                popup.style.opacity = '1';
            };
            
            // 隐藏弹窗的函数 - 只处理透明度
            const hidePopup = () => {
                popup.style.opacity = '0';
                // 等待渐出动画完成后再隐藏元素
                setTimeout(() => {
                    popup.style.display = 'none';
                }, 500); // 与transition时间匹配
            };
            
            // 查找所有产品导航项
            const productNavItems = document.querySelectorAll('.navbar .nav > li > a');
            productNavItems.forEach(item => {
                // 检查是否为产品中心或Products导航项
                if (item.textContent.includes('产品') || item.textContent.includes('Products')) {
                    // 鼠标进入导航项显示弹窗
                    item.addEventListener('mouseenter', showPopup);
                    
                    // 鼠标离开导航项，延迟隐藏弹窗
                    item.addEventListener('mouseleave', function(e) {
                        // 检查鼠标是否移向弹窗
                        if (!isMouseMovingToPopup(e, popup)) {
                            hideTimeout = setTimeout(hidePopup, 300);
                        }
                    });
                }
            });
            
            // 鼠标进入弹窗时清除隐藏计时器并确保完全显示
            popup.addEventListener('mouseenter', () => {
                clearTimeout(hideTimeout);
                popup.style.opacity = '1';
            });
            
            // 鼠标离开弹窗时设置隐藏计时器
            popup.addEventListener('mouseleave', function() {
                hideTimeout = setTimeout(hidePopup, 300);
            });

            // 添加点击事件监听器到document
            document.addEventListener('click', function(e) {
                // 检查点击是否在弹窗外部和导航项外部
                const isClickInsidePopup = popup.contains(e.target);
                const isClickOnNavItem = Array.from(productNavItems).some(item => 
                    item.contains(e.target) && (item.textContent.includes('产品') || item.textContent.includes('Products'))
                );
                
                if (!isClickInsidePopup && !isClickOnNavItem) {
                    hidePopup();
                }
            });

            // 添加滚轮事件监听器
            window.addEventListener('wheel', hidePopup);

            // 添加触摸滚动事件监听器（用于移动设备）
            window.addEventListener('touchmove', hidePopup);
        }
    }, 100);
}

/**
 * 从API获取产品分类数据
 * @returns {Promise<Array>} 产品分类数据
 */
async function fetchProductCategories() {
    try {
        // 判断当前是否为英文网站
        const isEnglish = document.documentElement.lang === 'en' || 
                          window.location.pathname.includes('_en.html') ||
                          window.location.hostname.includes('en.') || 
                          window.location.search.includes('lang=en');
        
        // 获取产品分类数据
        const response = await fetch('/apis/product_categories/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            },
            body: `lang=${isEnglish ? 1 : 0}`
        });
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.status === 'ok' && result.data) {
            // 处理API返回的数据格式，转换为我们需要的结构
            return formatProductCategories(result.data);
        } else {
            throw new Error('API返回数据格式不正确');
        }
    } catch (error) {
        console.error('获取产品分类数据失败:', error);
        return null;
    }
}

/**
 * 格式化API返回的产品分类数据
 * @param {Array} apiData API返回的原始数据
 * @returns {Array} 格式化后的产品分类数据
 */
function formatProductCategories(apiData) {
    // 这里根据实际API返回的数据结构进行转换
    // 示例格式：
    // [
    //   {
    //     title: '核心板',
    //     items: [
    //       { name: 'RK3399 Pro核心板', url: '/product-detail.html?id=1' },
    //       { name: 'RK3568工业核心板', url: '/product-detail.html?id=2' }
    //     ]
    //   },
    //   ...
    // ]
    
    // 如果API已经返回了正确的格式，直接返回
    if (apiData[0] && apiData[0].title && Array.isArray(apiData[0].items)) {
        return apiData;
    }
    
    // 否则进行转换
    // 以下代码需要根据实际API返回的数据结构进行调整
    const categories = [];
    
    // 假设API返回的是扁平结构的产品数据，需要按类别分组
    const groupedData = {};
    
    apiData.forEach(item => {
        const categoryName = item.category || '未分类';
        
        if (!groupedData[categoryName]) {
            groupedData[categoryName] = [];
        }
        
        groupedData[categoryName].push({
            name: item.title || item.name,
            url: item.url || `/product-detail.html?id=${item.id}`
        });
    });
    
    // 转换为数组格式
    Object.keys(groupedData).forEach(categoryName => {
        categories.push({
            title: categoryName,
            items: groupedData[categoryName]
        });
    });
    
    // 限制最多6个分类
    return categories.slice(0, 6);
}

/**
 * 获取默认产品分类数据
 * @returns {Array} 默认产品分类数据
 */
function getDefaultProductCategories() {
    // 判断当前是否为英文网站
    const isEnglish = document.documentElement.lang === 'en' || 
                      window.location.pathname.includes('_en.html') ||
                      window.location.hostname.includes('en.') || 
                      window.location.search.includes('lang=en');
    
    // 返回对应语言的默认数据
    if (isEnglish) {
        return [
            {
                title: 'Core Board',
                items: ['RK3399 Pro Core Board', 'RK3568 Industrial Core Board', 'RK3576 Core Board', 'RK3588 Core Board']
            },
            {
                title: 'Main Board',
                items: ['RK3568 Main Board', 'RK3588 Main Board', 'RK3568 Industrial Control Board', 'RK3576 Industrial Control Board', 'RK3576 Commercial Display Board']
            },
            {
                title: 'Terminal',
                items: ['RK3568 Data Collection Gateway', 'RK3576 Data Collection Gateway', 'RK3588 Edge Control Gateway', '8-inch Tablet', '10.6-inch Tablet', '11-inch Tablet', 'Smart Home Control Screen', 'Vehicle Control Screen', 'Industrial Control Screen']
            },
            {
                title: 'AIOT Solutions',
                items: ['Smart Home Control Screen', 'Vehicle Control Screen', 'Industrial Control Screen', 'Splicing Screen Processor', 'AI Edge Workstation', 'Video Optimization Box']
            },
            {
                title: 'OpenHarmony',
                items: ['RK3568 Main Board', 'RK3588 Main Board']
            },
            {
                title: 'MineHarmony',
                items: ['RK3576 Data Collection Gateway', 'Industrial Control Screen']
            }
        ];
    } else {
        return [
            {
                title: '核心板',
                items: ['RK3399 Pro核心板', 'RK3568工业核心板', 'RK3576核心板', 'RK3588核心板']
            },
            {
                title: '主板',
                items: ['RK3568主板', 'RK3588主板', 'RK3568工控制主板', 'RK3576工控制主板', 'RK3576商业显示主板']
            },
            {
                title: '终端',
                items: ['RK3568数据采集网关', 'RK3576数据采集网关', 'RK3588边缘控制网关', '8英寸平板', '10.6英寸平板', '11英寸平板', '智能家居中控屏', '车载中控屏', '工控屏']
            },
            {
                title: 'AIOT解决方案',
                items: ['智能家居中控屏', '车载中控屏', '工控屏', '拼接屏处理器', 'AI边缘工作站', '视频优化盒子']
            },
            {
                title: 'OpenHarmony',
                items: ['RK3568主板', 'RK3588主板']
            },
            {
                title: 'MineHarmony',
                items: ['RK3576数据采集网关', '工控屏']
            }
        ];
    }
}

/**
 * 确保必需的分类存在，如果不存在则添加
 * @param {Array} categories 当前分类数组
 * @param {boolean} isEnglish 是否为英文环境
 * @returns {Array} 处理后的分类数组
 */
function ensureRequiredCategories(categories, isEnglish) {
    // 定义必需的分类
    const requiredCategories = isEnglish ? 
        ['Core Board', 'Main Board', 'Terminal', 'AIOT solution'] : 
        ['核心板', '主板', '终端', 'AIOT解决方案'];
    
    // 获取当前分类名列表
    const currentCategoryNames = categories.map(cat => cat.name);
    
    // 检查每个必需分类是否存在
    requiredCategories.forEach(requiredName => {
        if (!currentCategoryNames.includes(requiredName)) {
            console.log(`添加缺失的必需分类: ${requiredName}`);
            // 查找默认分类中的对应项
            const defaultCategories = getDefaultProductCategories();
            const defaultCategory = defaultCategories.find(cat => 
                cat.title === requiredName
            );
            
            if (defaultCategory) {
                // 添加到分类列表
                categories.push({
                    name: requiredName,
                    items: defaultCategory.items
                });
            } else {
                // 如果在默认分类中也找不到，创建一个空分类
                categories.push({
                    name: requiredName,
                    items: []
                });
            }
        }
    });
    
    return categories;
}

/**
 * 判断鼠标是否正在移向弹窗
 * @param {MouseEvent} event - 鼠标事件
 * @param {HTMLElement} popup - 弹窗元素
 * @returns {boolean} - 是否移向弹窗
 */
function isMouseMovingToPopup(event, popup) {
    const popupRect = popup.getBoundingClientRect();
    
    // 获取鼠标当前位置
    const mouseX = event.clientX;
    const mouseY = event.clientY;
    
    // 计算鼠标与弹窗的相对位置关系
    const isMovingDown = mouseY < popupRect.top && mouseX >= popupRect.left && mouseX <= popupRect.right;
    
    return isMovingDown;
} 