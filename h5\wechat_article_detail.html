<!DOCTYPE html>
<html lang="en">
<head>
    <title>微信文章详情编辑</title>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="/admin/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="/admin/css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="/admin/css/colorpicker.css"/>
    <link rel="stylesheet" href="/admin/css/uniform.css"/>
    <link rel="stylesheet" href="/admin/css/fullcalendar.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-style.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-media.css"/>
    <link rel="stylesheet" href="/admin/css/bootstrap-wysihtml5.css"/>
    <link href="/admin/font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/admin/css/jquery.gritter.css"/>
    <script src="/admin/js/jquery.min.js"></script>
    <script src="/admin/js/jquery.cookie.js"></script>
    <script src="tinymce.min.js"></script>
</head>
<body>

<script src="/admin/js/head.js"></script>

<!--main-container-part-->
<div id="content">
    <!--breadcrumbs-->
    <div id="content-header">
        <div id="breadcrumb">
            <a href="#" class="current" id="article-name">微信文章详情</a>
        </div>
    </div>
    <!--End-breadcrumbs-->

    <!--Action boxes-->
    <div class="container-fluid">
        <div class="widget-box">
            <div class="widget-content tab-content">
                <!-- 工具栏 -->
                <div style="margin-bottom: 15px;">
                    <a class="btn btn-primary" href="wechat_articles.html">
                        <i class="icon-arrow-left"></i> 返回列表
                    </a>
                    <a class="btn btn-primary" href="javascript:update_detail()">
                        <i class="icon-save"></i> 保存
                    </a>
                    <select class="input-medium" style="margin-bottom: 0;width: auto" id="sel_paib">
                        <option value="1">排版方式：全屏显示（全图片推荐）</option>
                        <option value="0">排版方式：居中显示（旧版本，图文混编推荐）</option>
                    </select>
                    <a class="btn btn-success" href="#makeHtml" data-toggle="modal">
                        <i class="icon icon-th"></i> 生成产品页面
                    </a>
                </div>

                <!-- 文章信息显示 -->
                <div id="article-info" class="alert alert-info" style="margin-bottom: 15px; display: none;">
                    <h4 id="article-title">文章标题</h4>
                    <p><strong>文章ID:</strong> <span id="article-id"></span></p>
                    <p><strong>段落数:</strong> <span id="sections-count"></span></p>
                    <p><strong>创建时间:</strong> <span id="created-time"></span></p>
                </div>

                <!-- 隐藏字段 -->
                <input id="current-article-id" style="display: none">
                <input id="old_content" style="display: none">
                
                <!-- TinyMCE编辑器 -->
                <div id="tinymce_demo"></div>
            </div>
        </div>
    </div>

    <!-- 生成产品页面模态框 -->
    <div id="makeHtml" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h3 id="myModalLabel">生成产品页面</h3>
        </div>
        <div class="modal-body">
            <div class="control-group">
                <label class="control-label">产品标题</label>
                <div class="controls">
                    <input type="text" id="product-title" class="input-xlarge" placeholder="输入产品标题">
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">产品类型</label>
                <div class="controls">
                    <input type="text" id="product-type" class="input-xlarge" value="微信文章" placeholder="输入产品类型">
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">语言</label>
                <div class="controls">
                    <select id="product-lang" class="input-medium">
                        <option value="0">中文</option>
                        <option value="1">英文</option>
                    </select>
                </div>
            </div>
            <p style="color: #666; margin-top: 15px;">
                <i class="icon-info-sign"></i> 
                将根据当前编辑的内容生成产品详情页面
            </p>
        </div>
        <div class="modal-footer">
            <a class="btn btn-danger" href="javascript:make_product_html()">确认生成</a>
            <a data-dismiss="modal" class="btn">取消</a>
        </div>
    </div>
</div>

<!--end-main-container-part-->
<script src="/admin/js/footer.js"></script>
<script src="/admin/js/jquery.ui.custom.js"></script>
<script src="/admin/js/bootstrap.min.js"></script>
<script src="/admin/js/jquery.gritter.min.js"></script>
<script src="/admin/js/matrix.js"></script>

<script>
// 全局变量
let currentArticleId = null;
let currentArticleData = null;
let editorInitialized = false;

// 页面加载完成后初始化
$(document).ready(function() {
    // 从URL参数或Cookie获取文章ID
    const urlParams = new URLSearchParams(window.location.search);
    const articleId = urlParams.get('article_id') || $.cookie('wechat_article_id');

    if (articleId) {
        currentArticleId = articleId;
        // 保存到Cookie
        $.cookie('wechat_article_id', articleId, { expires: 1 });
        loadArticleData();
    } else {
        show_gitter('提示信息', '缺少文章ID参数，请从文章列表进入', 2);
        setTimeout(() => {
            window.location.href = 'wechat_articles.html';
        }, 2000);
    }
});

// 完全按照product_detail.html的TinyMCE配置
tinymce.init({
    selector: '#tinymce_demo', //容器，可使用css选择器
    language: 'zh_CN', //调用放在langs文件夹内的语言包
    toolbar: true, //工具栏
    menubar: true, //菜单栏
    branding: false, //右下角技术支持
    inline: false, //开启内联模式
    elementpath: false,
    min_height: 400, //最小高度
    height: 800,  //高度
    skin: 'oxide',
    theme: 'silver',
    theme_url: './theme.min.js', // 直接使用当前目录的theme.min.js文件
    toolbar_sticky: true,
    visualchars_default_state: true, //显示不可见字符
    image_caption: true,
    paste_data_images: true,
    relative_urls: false,
    // remove_script_host : false,
    removed_menuitems: 'newdocument',  //清除"文件"菜单
    plugins: "lists,hr, advlist,anchor,autolink,autoresize,charmap,code,codesample,emoticons,fullscreen,image,media,insertdatetime,link,pagebreak,paste,preview,print,searchreplace,table,toc,visualchars,wordcount", //依赖lists插件，移除textcolor因为已内置
    toolbar: 'bullist numlist anchor charmap emoticons fullscreen hr image insertdatetime link media pagebreak paste preview print searchreplace forecolor backcolor wordcount',
    //选中时出现的快捷工具，与插件有依赖关系
    images_upload_url: '/apis/upload_pic/', /*后图片上传接口*/ /*返回值为json类型 {'location':'uploads/jpg'}*/
    init_instance_callback: 'initData',
    // 直接指定每个插件的路径
    external_plugins: {
        'lists': './plugins/lists/plugin.min.js',
        'hr': './plugins/hr/plugin.min.js',
        'advlist': './plugins/advlist/plugin.min.js',
        'anchor': './plugins/anchor/plugin.min.js',
        'autolink': './plugins/autolink/plugin.min.js',
        'autoresize': './plugins/autoresize/plugin.min.js',
        'charmap': './plugins/charmap/plugin.min.js',
        'code': './plugins/code/plugin.min.js',
        'codesample': './plugins/codesample/plugin.min.js',
        'emoticons': './plugins/emoticons/plugin.min.js',
        'fullscreen': './plugins/fullscreen/plugin.min.js',
        'image': './plugins/image/plugin.min.js',
        'media': './plugins/media/plugin.min.js',
        'insertdatetime': './plugins/insertdatetime/plugin.min.js',
        'link': './plugins/link/plugin.min.js',
        'pagebreak': './plugins/pagebreak/plugin.min.js',
        'paste': './plugins/paste/plugin.min.js',
        'preview': './plugins/preview/plugin.min.js',
        'print': './plugins/print/plugin.min.js',
        'searchreplace': './plugins/searchreplace/plugin.min.js',
        'table': './plugins/table/plugin.min.js',

        'toc': './plugins/toc/plugin.min.js',
        'visualchars': './plugins/visualchars/plugin.min.js',
        'wordcount': './plugins/wordcount/plugin.min.js'
    },
    setup: function (editor) {
        editor.on('change', function () {
            editor.save();
        });
    }
});

// 加载文章数据（仿照product_detail.html的方式）
function loadArticleData() {
    if (!currentArticleId) return;

    // 方式1：使用原有的API获取段落信息
    $.ajax({
        url: '/apis/wechat_article_detail/',
        type: 'GET',
        data: { article_id: currentArticleId },
        success: function(response) {
            if (response.status === 'ok') {
                currentArticleData = response.data;
                displayArticleInfo(response.data);
            } else {
                show_gitter('错误', response.msg || '加载文章失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，加载文章失败', 2);
        }
    });
}

/*初始化数据 - 完全按照product_detail.html的方式*/
function initData(instance) {
    //
    var article_id = $.cookie('wechat_article_id');
    if (article_id === undefined || article_id === '') {
        show_gitter('提示信息', '信息过期，请返回重新进入', 2);
        self.location = 'wechat_articles.html'
        return
    }

    var filters = {article_id: article_id};
    var fileds = ['html_content'];
    var html_content = '';
    $.ajax({
        type: "post",
        url: "/apis/get_wechat_article/",
        async: false,
        data: {page: 1, page_size:10, order_by:JSON.stringify(['-id']),filters:JSON.stringify(filters),fileds:JSON.stringify(fileds)},
        success: function (data) {
            if (data.status === 'ok') {
                var data_list = data.data;
                if (data_list.length>0){
                    html_content = data_list[0].html_content;
                }
            } else {
                var err_msg  = data.msg ? data.msg : '内部服务错误';
                show_gitter('错误提示', err_msg, 2);
            }
        }
    });
    if (instance != null) {
        tinyMCE.activeEditor.setContent(html_content);
    }
    $('#old_content').val(html_content);
}

// 显示文章信息
function displayArticleInfo(sections) {
    if (sections.length === 0) return;
    
    const firstSection = sections[0];
    $('#article-name').text(firstSection.title || '无标题');
    $('#article-title').text(firstSection.title || '无标题');
    $('#article-id').text(currentArticleId);
    $('#sections-count').text(sections.length);
    $('#created-time').text(new Date(firstSection.created_at).toLocaleString());
    $('#product-title').val(firstSection.title || '');
    
    $('#article-info').show();
}



/*保存文章内容 - 完全按照product_detail.html的update_detail函数*/
function update_detail() {
    var article_id = $.cookie('wechat_article_id');
    if (article_id === undefined || article_id === '') {
        show_gitter('提示信息', '信息过期，请返回重新进入', 2);
        return;
    }

    var html_content = tinyMCE.activeEditor.getContent();
    var old_content = $('#old_content').val();

    if (!html_content.trim()) {
        show_gitter('提示信息', '内容不能为空', 2);
        return;
    }

    var filters = JSON.stringify({article_id: article_id});

    $.ajax({
        type: "POST",
        url: "/apis/update_wechat_article/",
        async: false,
        data: {
            filters: filters,
            html_content: html_content
        },
        success: function (data) {
            if (data.status === 'ok') {
                $('#old_content').val(html_content);
                show_gitter('成功', '文章内容保存成功！', 1);
            } else {
                show_gitter('错误', data.msg || '保存失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，保存失败', 2);
        }
    });
}

// 生成产品页面
function make_product_html() {
    const productTitle = $('#product-title').val();
    const productType = $('#product-type').val();
    const productLang = $('#product-lang').val();
    const sel_paib = $('#sel_paib').val();

    if (!productTitle.trim()) {
        show_gitter('提示信息', '请输入产品标题', 2);
        return;
    }

    if (!currentArticleId) {
        show_gitter('提示信息', '缺少文章ID', 2);
        return;
    }

    $.ajax({
        type: "POST",
        url: "/apis/generate_product_from_wechat/",
        data: {
            article_id: currentArticleId,
            product_title: productTitle,
            product_type: productType,
            lang: productLang,
            paib: sel_paib
        },
        success: function (data) {
            if (data.status === 'ok') {
                show_gitter('成功', `产品页面生成成功！产品ID: ${data.product_id}`, 1);
                $('#makeHtml').modal('hide');

                // 询问是否跳转到产品管理页面
                setTimeout(() => {
                    if (confirm('是否跳转到产品管理页面查看生成的产品？')) {
                        window.open('/admin1/product.html', '_blank');
                    }
                }, 1500);
            } else {
                show_gitter('错误', data.msg || '生成产品页面失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，生成产品页面失败', 2);
        }
    });
}

// 显示消息提示
function show_gitter(title, text, type) {
    $.gritter.add({
        title: title,
        text: text,
        sticky: false,
        time: type === 1 ? 3000 : 5000,
        class_name: type === 1 ? 'gritter-success' : 'gritter-error'
    });
}

/*填入初始数据*/
//tinyMCE.activeEditor.setContent("<h1>测试</h1><hr><h2>这是测试的数据<h2>");
/*
1、如果当前页面只有一个编辑器：
    获取内容：tinyMCE.activeEditor.getContent()
    设置内容：tinyMCE.activeEditor.setContent("需要设置的编辑器内容")
2、如果当前页面有多个编辑器（下面的"[0]"表示第一个编辑器，以此类推）：
    获取内容：tinyMCE.editors[0].getContent()
    设置内容：tinyMCE.editors[0].setContent("需要设置的编辑器内容")
*/
function setcontent() {
    tinyMCE.activeEditor.setContent("<h1>设置内容1</h1>");
    //tinyMCE.editors[0].setContent("<h1>设置内容2</h1>");
}

function getcontent() {
    alert(tinyMCE.activeEditor.getContent());
}

/*3、获取不带HTML标记的纯文本内容：
 var activeEditor = tinymce.activeEditor;
 var editBody = activeEditor.getBody();
 activeEditor.selection.select(editBody);
 var text = activeEditor.selection.getContent( {'format' : 'text' } );*/
function getbody() {
    var activeEditor = tinymce.activeEditor;
    var editBody = activeEditor.getBody();
    activeEditor.selection.select(editBody);
    var text = activeEditor.selection.getContent({'format': 'text'});
    alert(text);
}
</script>
</body>
</html>
