<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Lazy Load carousel">
    <meta name="author" content="<PERSON>">
    <title>
      Lazy Load Demo | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/demos/demos.html">Demos</a> 
              </li>
              <li> <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a>  </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>Lazy Load</h1>
        </div>
      </div>
    </section>

    <!--  Demos -->
    <section id="demos">
      <div class="row">
        <div class="large-12 columns">
          <div class="owl-carousel owl-theme">
            <img class="owl-lazy" data-src="https://placehold.it/350x250&text=1" data-src-retina="https://placehold.it/350x250&text=1-retina" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x350&text=2" data-src-retina="https://placehold.it/350x250&text=2-retina" alt="">
            <picture>
              <source class="owl-lazy" media="(min-width: 650px)" data-srcset="https://placehold.it/350x250&text=3-large">
              <source class="owl-lazy" media="(min-width: 350px)" data-srcset="https://placehold.it/350x250&text=3-medium">
              <img class="owl-lazy" data-src="https://placehold.it/350x250&text=3-fallback" alt="">
            </picture>
            <img class="owl-lazy" data-src="https://placehold.it/350x250&text=4" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x250&text=5" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x250&text=6" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x250&text=7" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x250&text=8" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x400&text=9" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x400&text=10" alt="">
            <img class="owl-lazy" data-src="https://placehold.it/350x450&text=11" alt="">
          </div>
          <h3 id="overview">Overview</h3>
          <p>Add lazyLoad to plugin setup:</p>
          <pre><code>lazyLoad: true</code></pre>
          <p>LazyLoad HTML structure requires <code>class=&quot;owl-lazy&quot;</code> and <code>data-src=&quot;url_to_img&quot;</code> or/and <code>data-src-retina=&quot;url_to_highres_img&quot;</code>. If you set above settings not on <code>&lt;img&gt;</code>            but on other DOM element then Owl will load an image into css inline background style. You can even use picture tags with different sources by adding the <code>owl-lazy</code> class to the source tag and a data-srcset attribute.</p>
          <h3 id="setup">Setup</h3>
          <pre><code>$(&#39;.owl-carousel&#39;).owlCarousel({
    items:4,
    lazyLoad:true,
    loop:true,
    margin:10
});</code></pre>
          <h3 id="option">Option</h3>
          <p>If you want to preload images you can also add the option <code>lazyLoadEager</code> to the settings object, where the value indicates how many items to the right (and left, when loop is <code>true</code>) will be pre-loaded.</p>
          <h3 id="html-">HTML:</h3>
          <pre><code>&lt;div class=&quot;owl-carousel owl-theme&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x450&amp;text=1&quot; data-src-retina=&quot;https://placehold.it/350x250&amp;text=1-retina&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x650&amp;text=2&quot; data-src-retina=&quot;https://placehold.it/350x250&amp;text=2-retina&quot; alt=&quot;&quot;&gt;
  &lt;picture&gt;
      &lt;source class=&quot;owl-lazy&quot; media=&quot;(min-width: 650px)&quot; data-srcset=&quot;https://placehold.it/350x250&amp;text=3-large&quot;&gt;
      &lt;source class=&quot;owl-lazy&quot; media=&quot;(min-width: 350px)&quot; data-srcset=&quot;https://placehold.it/350x250&amp;text=3-medium&quot;&gt;
      &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x250&amp;text=3-fallback&quot; alt=&quot;&quot;&gt;
  &lt;/picture&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x250&amp;text=4&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x250&amp;text=5&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x250&amp;text=6&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x250&amp;text=7&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x250&amp;text=8&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x400&amp;text=9&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x400&amp;text=10&quot; alt=&quot;&quot;&gt;
    &lt;img class=&quot;owl-lazy&quot; data-src=&quot;https://placehold.it/350x450&amp;text=11&quot; alt=&quot;&quot;&gt;
&lt;/div&gt;</code></pre>
          <script>
            jQuery(document).ready(function($) {
              $('.owl-carousel').owlCarousel({
                items: 1,
                lazyLoad: true,
                lazyLoadEager: 1,
                loop: true,
                margin: 10,
                autoHeight: true
              });
            });
          </script>
        </div>
      </div>
    </section>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>