const fs = require('fs');
const path = require('path');
const https = require('https');

// 确保目录存在
const iconDir = path.join(__dirname, 'login', 'img');
if (!fs.existsSync(iconDir)) {
    fs.mkdirSync(iconDir, { recursive: true });
    console.log(`创建目录: ${iconDir}`);
}

// 下载图标文件
const iconUrl = 'https://raw.githubusercontent.com/twbs/bootstrap/v2.3.2/img/glyphicons-halflings.png';
const iconPath = path.join(iconDir, 'glyphicons-halflings.png');

console.log(`开始下载图标文件: ${iconUrl}`);
const file = fs.createWriteStream(iconPath);

https.get(iconUrl, (response) => {
    if (response.statusCode !== 200) {
        console.error(`下载失败，状态码: ${response.statusCode}`);
        fs.unlinkSync(iconPath);
        return;
    }

    response.pipe(file);

    file.on('finish', () => {
        file.close();
        console.log(`图标文件下载完成: ${iconPath}`);
    });
}).on('error', (err) => {
    fs.unlinkSync(iconPath);
    console.error(`下载出错: ${err.message}`);
}); 