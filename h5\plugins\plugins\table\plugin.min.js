/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.1 (2020-07-08)
 */
!function(h){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager"),w=function(){},b=function(n){return function(){return n}},u=function(n){return n};function y(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var e,t,r,o,d=function(e){return function(n){return!e(n)}},f=b(!1),C=b(!0),i=function(){return c},c=(e=function(n){return n.isNone()},{fold:function(n,e){return n()},is:f,isSome:f,isNone:C,getOr:r=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(undefined),or:r,orThunk:t,map:i,each:w,bind:i,exists:f,forall:C,filter:i,equals:e,equals_:e,toArray:function(){return[]},toString:b("none()")}),a=function(t){var n=b(t),e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:C,isNone:f,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return a(n(t))},each:function(n){n(t)},bind:r,exists:r,forall:r,filter:function(n){return n(t)?o:c},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(f,function(n){return e(t,n)})}};return o},S={some:a,none:i,from:function(n){return null===n||n===undefined?c:a(n)}},l=function(r){return function(n){return t=typeof(e=n),(null===e?"null":"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t)===r;var e,t}},s=function(e){return function(n){return typeof n===e}},m=l("string"),g=l("object"),p=l("array"),v=s("boolean"),x=function(n){return!(null===(e=n)||e===undefined);var e},T=s("function"),R=s("number"),O=Array.prototype.slice,D=Array.prototype.indexOf,A=Array.prototype.push,B=function(n,e){return t=n,r=e,-1<D.call(t,r);var t,r},I=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1},P=function(n,e){for(var t=[],r=0;r<n;r++)t.push(e(r));return t},k=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o)}return r},E=function(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}},M=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var u=n[r];e(u,r)&&t.push(u)}return t},N=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t},_=function(n,e,t){return E(n,function(n){t=e(t,n)}),t},W=function(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var u=n[r];if(e(u,r))return S.some(u);if(t(u,r))break}return S.none()}(n,e,f)},j=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return S.some(t)}return S.none()},z=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!p(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);A.apply(e,n[t])}return e},F=function(n,e){return z(k(n,e))},L=function(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0},H=function(n){return 0===n.length?S.none():S.some(n[n.length-1])},q=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return S.none()},V=Object.keys,U=Object.hasOwnProperty,K=function(n,e){for(var t=V(n),r=0,o=t.length;r<o;r++){var u=t[r];e(n[u],u)}},$=function(n,t){return X(n,function(n,e){return{k:e,v:t(n,e)}})},X=function(n,r){var o={};return K(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},G=function(n,e){var t,r,o,u,i={};return t=e,u=i,r=function(n,e){u[e]=n},o=w,K(n,function(n,e){(t(n,e)?r:o)(n,e)}),i},Y=function(n,e){return J(n,e)?S.from(n[e]):S.none()},J=function(n,e){return U.call(n,e)},Q=("undefined"!=typeof h.window?h.window:Function("return this;")(),function(n){return n.dom().nodeName.toLowerCase()}),Z=function(n){return n.dom().nodeType},nn=function(e){return function(n){return Z(n)===e}},en=function(n){return 8===Z(n)||"#comment"===Q(n)},tn=nn(1),rn=nn(3),on=nn(9),un=nn(11),cn=function(n,e,t){if(!(m(t)||v(t)||R(t)))throw h.console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},an=function(n,e,t){cn(n.dom(),e,t)},ln=function(n,e){var t=n.dom();K(e,function(n,e){cn(t,e,n)})},fn=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},sn=function(n,e){return S.from(fn(n,e))},dn=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},mn=function(n,e){n.dom().removeAttribute(e)},gn=function(n){return _(n.dom().attributes,function(n,e){return n[e.name]=e.value,n},{})},pn=function(n,e,t){return""===e||n.length>=e.length&&n.substr(t,t+e.length)===e},hn=function(n,e){return-1!==n.indexOf(e)},vn=function(n,e){return pn(n,e,n.length-e.length)},bn=(o=/^\s+|\s+$/g,function(n){return n.replace(o,"")}),wn=function(n){return 0<n.length},yn=function(n){return n.style!==undefined&&T(n.style.getPropertyValue)},Cn=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:b(n)}},Sn={fromHtml:function(n,e){var t=(e||h.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw h.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return Cn(t.childNodes[0])},fromTag:function(n,e){var t=(e||h.document).createElement(n);return Cn(t)},fromText:function(n,e){var t=(e||h.document).createTextNode(n);return Cn(t)},fromDom:Cn,fromPoint:function(n,e,t){var r=n.dom();return S.from(r.elementFromPoint(e,t)).map(Cn)}},xn=function(){return(xn=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)},Tn=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},Rn=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return Dn(r(1),r(2))},On=function(){return Dn(0,0)},Dn=function(n,e){return{major:n,minor:e}},An={nu:Dn,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?On():Rn(n,t)},unknown:On},Bn="Firefox",In=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:r("Edge"),isChrome:r("Chrome"),isIE:r("IE"),isOpera:r("Opera"),isFirefox:r(Bn),isSafari:r("Safari")}},Pn={unknown:function(){return In({current:undefined,version:An.unknown()})},nu:In,edge:b("Edge"),chrome:b("Chrome"),ie:b("IE"),opera:b("Opera"),firefox:b(Bn),safari:b("Safari")},kn="Windows",En="Android",Mn="Solaris",Nn="FreeBSD",_n="ChromeOS",Wn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:r(kn),isiOS:r("iOS"),isAndroid:r(En),isOSX:r("OSX"),isLinux:r("Linux"),isSolaris:r(Mn),isFreeBSD:r(Nn),isChromeOS:r(_n)}},jn={unknown:function(){return Wn({current:undefined,version:An.unknown()})},nu:Wn,windows:b(kn),ios:b("iOS"),android:b(En),linux:b("Linux"),osx:b("OSX"),solaris:b(Mn),freebsd:b(Nn),chromeos:b(_n)},zn=function(n,e){var t=String(e).toLowerCase();return W(n,function(n){return n.search(t)})},Fn=function(n,t){return zn(n,t).map(function(n){var e=An.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Ln=function(n,t){return zn(n,t).map(function(n){var e=An.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Hn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,qn=function(e){return function(n){return hn(n,e)}},Vn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return hn(n,"edge/")&&hn(n,"chrome")&&hn(n,"safari")&&hn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Hn],search:function(n){return hn(n,"chrome")&&!hn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return hn(n,"msie")||hn(n,"trident")}},{name:"Opera",versionRegexes:[Hn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:qn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:qn("firefox")},{name:"Safari",versionRegexes:[Hn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(hn(n,"safari")||hn(n,"mobile/"))&&hn(n,"applewebkit")}}],Un=[{name:"Windows",search:qn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return hn(n,"iphone")||hn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:qn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:qn("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:qn("linux"),versionRegexes:[]},{name:"Solaris",search:qn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:qn("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:qn("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Kn={browsers:b(Vn),oses:b(Un)},$n=function(n,e){var t,r,o,u,i,c,a,l,f,s,d,m,g=Kn.browsers(),p=Kn.oses(),h=Fn(g,n).fold(Pn.unknown,Pn.nu),v=Ln(p,n).fold(jn.unknown,jn.nu);return{browser:h,os:v,deviceType:(r=h,o=n,u=e,i=(t=v).isiOS()&&!0===/ipad/i.test(o),c=t.isiOS()&&!i,a=t.isiOS()||t.isAndroid(),l=a||u("(pointer:coarse)"),f=i||!c&&a&&u("(min-device-width:768px)"),s=c||a&&!f,d=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!s&&!f&&!d,{isiPad:b(i),isiPhone:b(c),isTablet:b(f),isPhone:b(s),isTouch:b(l),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:b(d),isDesktop:b(m)})}},Xn=function(n){return h.window.matchMedia(n).matches},Gn=Tn(function(){return $n(h.navigator.userAgent,Xn)}),Yn=function(){return Gn()},Jn=function(n,e){var t=n.dom();if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},Qn=function(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount},Zn=function(n,e){return n.dom()===e.dom()},ne=function(n,e){return t=n.dom(),r=e.dom(),o=t,u=r,i=h.Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(o.compareDocumentPosition(u)&i);var t,r,o,u,i},ee=function(n,e){return Yn().browser.isIE()?ne(n,e):(t=e,r=n.dom(),o=t.dom(),r!==o&&r.contains(o));var t,r,o},te=Jn,re=function(n){return Sn.fromDom(n.dom().ownerDocument)},oe=function(n){return S.from(n.dom().parentNode).map(Sn.fromDom)},ue=function(n,e){for(var t=T(e)?e:f,r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var u=r.parentNode,i=Sn.fromDom(u);if(o.push(i),!0===t(i))break;r=u}return o},ie=function(n){return S.from(n.dom().previousSibling).map(Sn.fromDom)},ce=function(n){return S.from(n.dom().nextSibling).map(Sn.fromDom)},ae=function(n){return k(n.dom().childNodes,Sn.fromDom)},le=function(n,e){var t=n.dom().childNodes;return S.from(t[e]).map(Sn.fromDom)},fe=T(h.Element.prototype.attachShadow)&&T(h.Node.prototype.getRootNode),se=b(fe),de=fe?function(n){return Sn.fromDom(n.dom().getRootNode())}:function(n){return on(n)?n:re(n)},me=function(n){var e=de(n);return un(e)?S.some(e):S.none()},ge=function(n){return Sn.fromDom(n.dom().host)},pe=function(n){if(se()&&x(n.target)){var e=Sn.fromDom(n.target);if(tn(e)&&he(Sn.fromDom(n.target))){if(n.composed&&n.composedPath){var t=n.composedPath();if(t)return 0===(r=t).length?S.none():S.some(r[0])}}}var r;return S.from(n.target)},he=function(n){return x(n.dom().shadowRoot)},ve=function(n){var e,t,r=rn(n)?n.dom().parentNode:n.dom();return r!==undefined&&null!==r&&null!==r.ownerDocument&&me(Sn.fromDom(r)).fold(function(){return r.ownerDocument.body.contains(r)},(e=ve,t=ge,function(n){return e(t(n))}))},be=function(n){var e=n.dom().body;if(null===e||e===undefined)throw new Error("Body is not available yet");return Sn.fromDom(e)},we=function(n,e,t){if(!m(t))throw h.console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);yn(n)&&n.style.setProperty(e,t)},ye=function(n,e,t){var r=n.dom();we(r,e,t)},Ce=function(n,e){var t=n.dom();K(e,function(n,e){we(t,e,n)})},Se=function(n,e){var t=n.dom(),r=h.window.getComputedStyle(t).getPropertyValue(e);return""!==r||ve(n)?r:xe(t,e)},xe=function(n,e){return yn(n)?n.style.getPropertyValue(e):""},Te=function(n,e){var t=n.dom(),r=xe(t,e);return S.from(r).filter(function(n){return 0<n.length})},Re=function(n,e){var t,r,o=n.dom();r=e,yn(t=o)&&t.style.removeProperty(r),sn(n,"style").map(bn).is("")&&mn(n,"style")},Oe=function(e,t){oe(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})},De=function(n,e){ce(n).fold(function(){oe(n).each(function(n){Be(n,e)})},function(n){Oe(n,e)})},Ae=function(e,t){le(e,0).fold(function(){Be(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})},Be=function(n,e){n.dom().appendChild(e.dom())},Ie=function(n,e){Oe(n,e),Be(e,n)},Pe=function(r,o){E(o,function(n,e){var t=0===e?r:o[e-1];De(t,n)})},ke=function(e,n){E(n,function(n){Be(e,n)})},Ee=function(n){n.dom().textContent="",E(ae(n),function(n){Me(n)})},Me=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},Ne=function(n){var e,t=ae(n);0<t.length&&(e=n,E(t,function(n){Oe(e,n)})),Me(n)},_e=function(n,e,t){return{element:b(n),rowspan:b(e),colspan:b(t)}},We=function(n,e,t){return{element:b(n),cells:b(e),section:b(t)}},je=function(n,e){return{element:b(n),isNew:b(e)}},ze=function(n,e){return{cells:b(n),section:b(e)}},Fe=function(n,e){var t=[];return E(ae(n),function(n){e(n)&&(t=t.concat([n])),t=t.concat(Fe(n,e))}),t},Le=function(n,e,t){return r=function(n){return Jn(n,e)},M(ue(n,t),r);var r},He=function(n,e){return t=function(n){return Jn(n,e)},M(ae(n),t);var t},qe=function(n,e){return t=e,o=(r=n)===undefined?h.document:r.dom(),Qn(o)?[]:k(o.querySelectorAll(t),Sn.fromDom);var t,r,o};function Ve(n,e,t,r,o){return n(t,r)?S.some(t):T(o)&&o(t)?S.none():e(t,r,o)}var Ue=function(n,e,t){for(var r=n.dom(),o=T(t)?t:b(!1);r.parentNode;){r=r.parentNode;var u=Sn.fromDom(r);if(e(u))return S.some(u);if(o(u))break}return S.none()},Ke=function(n,e,t){return Ue(n,function(n){return Jn(n,e)},t)},$e=function(n,e){return t=function(n){return Jn(n,e)},W(n.dom().childNodes,function(n){return t(Sn.fromDom(n))}).map(Sn.fromDom);var t},Xe=function(n,e){return t=e,o=(r=n)===undefined?h.document:r.dom(),Qn(o)?S.none():S.from(o.querySelector(t)).map(Sn.fromDom);var t,r,o},Ge=function(n,e,t){return Ve(function(n,e){return Jn(n,e)},Ke,n,e,t)},Ye=function(n,e,t){return void 0===t&&(t=0),sn(n,e).map(function(n){return parseInt(n,10)}).getOr(t)},Je=function(n,e){return Ye(n,e,1)},Qe=function(n){return 1<Je(n,"colspan")},Ze=function(n){return 1<Je(n,"rowspan")},nt=function(n,e){return parseInt(Se(n,e),10)},et=b(10),tt=b(10),rt=function(n,e){return ot(n,e,b(!0))},ot=function(n,e,t){return F(ae(n),function(n){return Jn(n,e)?t(n)?[n]:[]:ot(n,e,t)})},ut=function(n,e){return function(n,e,t){if(void 0===t&&(t=f),t(e))return S.none();if(B(n,Q(e)))return S.some(e);return Ke(e,n.join(","),function(n){return Jn(n,"table")||t(n)})}(["td","th"],n,e)},it=function(n){return rt(n,"th,td")},ct=function(n,e){return Ge(n,"table",e)},at=function(n){return rt(n,"tr")},lt=function(n){var e=at(n);return k(e,function(n){var e=n,t=oe(e).map(function(n){var e=Q(n);return"tfoot"===e||"thead"===e||"tbody"===e?e:"tbody"}).getOr("tbody"),r=k(it(n),function(n){var e=Ye(n,"rowspan",1),t=Ye(n,"colspan",1);return _e(n,e,t)});return We(e,r,t)})},ft=function(n,e){return n+","+e},st=function(n,e){var t=F(n.all,function(n){return n.cells()});return M(t,e)},dt=function(n){var d={},e=[],t=n.length,m=0;return E(n,function(n,f){var s=[];E(n.cells(),function(n){for(var e=0;d[ft(f,e)]!==undefined;)e++;for(var t,r,o,u=(t=n.element(),r=n.rowspan(),o=n.colspan(),{element:b(t),rowspan:b(r),colspan:b(o),row:b(f),column:b(e)}),i=0;i<n.colspan();i++)for(var c=0;c<n.rowspan();c++){var a=e+i,l=ft(f+c,a);d[l]=u,m=Math.max(m,a+1)}s.push(u)}),e.push(We(n.element(),s,n.section()))}),{grid:{rows:b(t),columns:b(m)},access:d,all:e}},mt={fromTable:function(n){var e=lt(n);return dt(e)},generate:dt,getAt:function(n,e,t){var r=n.access[ft(e,t)];return r!==undefined?S.some(r):S.none()},findItem:function(n,e,t){var r=st(n,function(n){return t(e,n.element())});return 0<r.length?S.some(r[0]):S.none()},filterItems:st,justCells:function(n){var e=k(n.all,function(n){return n.cells()});return z(e)}},gt=function(n,e){var t,u,r,i,c,a,l,o,f,s,d=function(n){return Jn(n.element(),e)},m=lt(n),g=mt.generate(m),p=(u=d,r=(t=g).grid.columns(),i=t.grid.rows(),c=r,l=a=0,K(t.access,function(n){if(u(n)){var e=n.row(),t=e+n.rowspan()-1,r=n.column(),o=r+n.colspan()-1;e<i?i=e:a<t&&(a=t),r<c?c=r:l<o&&(l=o)}}),{minRow:i,minCol:c,maxRow:a,maxCol:l}),h="th:not("+e+"),td:not("+e+")",v=ot(n,"th,td",function(n){return Jn(n,h)});return E(v,Me),function(n,e,t,r){for(var o,u,i,c=e.grid.columns(),a=e.grid.rows(),l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){if(!(l<t.minRow||l>t.maxRow||s<t.minCol||s>t.maxCol))mt.getAt(e,l,s).filter(r).isNone()?(o=f,0,u=n[l].element(),i=Sn.fromTag("td"),Be(i,Sn.fromTag("br")),(o?Be:Ae)(u,i)):f=!0}}(m,g,p,d),f=p,s=M(rt(o=n,"tr"),function(n){return 0===n.dom().childElementCount}),E(s,Me),f.minCol!==f.maxCol&&f.minRow!==f.maxRow||E(rt(o,"th,td"),function(n){mn(n,"rowspan"),mn(n,"colspan")}),mn(o,"width"),mn(o,"height"),Re(o,"width"),Re(o,"height"),n};var pt=function zs(t,r){var e=function(n){return t(n)?S.from(n.dom().nodeValue):S.none()};return{get:function(n){if(!t(n))throw new Error("Can only get "+r+" value of a "+r+" node");return e(n).getOr("")},getOption:e,set:function(n,e){if(!t(n))throw new Error("Can only set raw "+r+" value of a "+r+" node");n.dom().nodeValue=e}}}(rn,"text"),ht=function(n){return pt.get(n)},vt=function(n){return pt.getOption(n)},bt=function(n,e){return pt.set(n,e)},wt=function(n){return"img"===Q(n)?1:vt(n).fold(function(){return ae(n).length},function(n){return n.length})},yt=["img","br"],Ct=function(n){return vt(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()||B(yt,Q(n))},St=function(n){return o=Ct,(u=function(n){for(var e=0;e<n.childNodes.length;e++){var t=Sn.fromDom(n.childNodes[e]);if(o(t))return S.some(t);var r=u(n.childNodes[e]);if(r.isSome())return r}return S.none()})(n.dom());var o,u},xt=function(n){return Tt(n,Ct)},Tt=function(n,u){var i=function(n){for(var e=ae(n),t=e.length-1;0<=t;t--){var r=e[t];if(u(r))return S.some(r);var o=i(r);if(o.isSome())return o}return S.none()};return i(n)},Rt=function(n,e){return Sn.fromDom(n.dom().cloneNode(e))},Ot=function(n){return Rt(n,!1)},Dt=function(n){return Rt(n,!0)},At=function(n,e){var t,r,o,u,i=(t=n,r=e,o=Sn.fromTag(r),u=gn(t),ln(o,u),o),c=ae(Dt(n));return ke(i,c),i},Bt=function(){var n=Sn.fromTag("td");return Be(n,Sn.fromTag("br")),n},It=function(n,e,t){var r=At(n,e);return K(t,function(n,e){null===n?mn(r,e):an(r,e,n)}),r},Pt=function(n){return n},kt=function(n){return function(){return Sn.fromTag("tr",n.dom())}},Et=function(d,n,m){return{row:kt(n),cell:function(n){var r,o,u,e,t,i,c,a=re(n.element()),l=Sn.fromTag(Q(n.element()),a.dom()),f=m.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),s=0<f.length?(r=n.element(),o=l,u=f,St(r).map(function(n){var e=u.join(","),t=Le(n,e,function(n){return Zn(n,r)});return N(t,function(n,e){var t=Ot(e);return mn(t,"contenteditable"),Be(n,t),t},o)}).getOr(o)):l;return Be(s,Sn.fromTag("br")),e=n.element(),t=l,i=e.dom(),c=t.dom(),yn(i)&&yn(c)&&(c.style.cssText=i.style.cssText),Re(l,"height"),1!==n.colspan()&&Re(n.element(),"width"),d(n.element(),l),l},replace:It,gap:Bt}},Mt=function(n){return{row:kt(n),cell:Bt,replace:Pt,gap:Bt}},Nt=function(n,e){var t=e.column(),r=e.column()+e.colspan()-1,o=e.row(),u=e.row()+e.rowspan()-1;return t<=n.finishCol()&&r>=n.startCol()&&o<=n.finishRow()&&u>=n.startRow()},_t=function(n,e){return e.column()>=n.startCol()&&e.column()+e.colspan()-1<=n.finishCol()&&e.row()>=n.startRow()&&e.row()+e.rowspan()-1<=n.finishRow()},Wt=function(n,e){return t=Math.min(n.row(),e.row()),r=Math.min(n.column(),e.column()),o=Math.max(n.row()+n.rowspan()-1,e.row()+e.rowspan()-1),u=Math.max(n.column()+n.colspan()-1,e.column()+e.colspan()-1),{startRow:b(t),startCol:b(r),finishRow:b(o),finishCol:b(u)};var t,r,o,u},jt=function(n,e,t){var r=mt.findItem(n,e,Zn),o=mt.findItem(n,t,Zn);return r.bind(function(e){return o.map(function(n){return Wt(e,n)})})},zt=function(e,n,t){return jt(e,n,t).bind(function(n){return function(n,e){for(var t=!0,r=y(_t,e),o=e.startRow();o<=e.finishRow();o++)for(var u=e.startCol();u<=e.finishCol();u++)t=t&&mt.getAt(n,o,u).exists(r);return t?S.some(e):S.none()}(e,n)})},Ft=function(t,n,e){return jt(t,n,e).map(function(n){var e=mt.filterItems(t,y(Nt,n));return k(e,function(n){return n.element()})})},Lt=function(n,e){return mt.findItem(n,e,function(n,e){return ee(e,n)}).map(function(n){return n.element()})},Ht=function(i,c,a){return ct(i).bind(function(n){var r,e,o,u,t=Vt(n);return r=t,e=i,o=c,u=a,mt.findItem(r,e,Zn).bind(function(n){var e=0<o?n.row()+n.rowspan()-1:n.row(),t=0<u?n.column()+n.colspan()-1:n.column();return mt.getAt(r,e+o,t+u).map(function(n){return n.element()})})})},qt=function(n,e,t,r,o){var u=Vt(n),i=Zn(n,t)?S.some(e):Lt(u,e),c=Zn(n,o)?S.some(r):Lt(u,r);return i.bind(function(e){return c.bind(function(n){return Ft(u,e,n)})})},Vt=mt.fromTable,Ut=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Kt(){return{up:b({selector:Ke,closest:Ge,predicate:Ue,all:ue}),down:b({selector:qe,predicate:Fe}),styles:b({get:Se,getRaw:Te,set:ye,remove:Re}),attrs:b({get:fn,set:an,remove:mn,copyTo:function(n,e){var t=gn(n);ln(e,t)}}),insert:b({before:Oe,after:De,afterAll:Pe,append:Be,appendAll:ke,prepend:Ae,wrap:Ie}),remove:b({unwrap:Ne,remove:Me}),create:b({nu:Sn.fromTag,clone:function(n){return Sn.fromDom(n.dom().cloneNode(!1))},text:Sn.fromText}),query:b({comparePosition:function(n,e){return n.dom().compareDocumentPosition(e.dom())},prevSibling:ie,nextSibling:ce}),property:b({children:ae,name:Q,parent:oe,document:function(n){return n.dom().ownerDocument},isText:rn,isComment:en,isElement:tn,getText:ht,setText:bt,isBoundary:function(n){return!!tn(n)&&("body"===Q(n)||B(Ut,Q(n)))},isEmptyTag:function(n){return!!tn(n)&&B(["br","img","hr","input"],Q(n))},isNonEditable:function(n){return tn(n)&&"false"===fn(n,"contenteditable")}}),eq:Zn,is:te}}var $t=function(r,o,n,e){var t=o(r,n);return N(e,function(n,e){var t=o(r,e);return Xt(r,n,t)},t)},Xt=function(e,n,t){return n.bind(function(n){return t.filter(y(e.eq,n))})},Gt=function(n,e,t){return 0<t.length?$t(n,e,(r=t)[0],r.slice(1)):S.none();var r},Yt=function(t,n,e,r){void 0===r&&(r=f);var o=[n].concat(t.up().all(n)),u=[e].concat(t.up().all(e)),i=function(e){return j(e,r).fold(function(){return e},function(n){return e.slice(0,n+1)})},c=i(o),a=i(u),l=W(c,function(n){return I(a,(e=n,y(t.eq,e)));var e});return{firstpath:b(c),secondpath:b(a),shared:b(l)}},Jt=Kt(),Qt=function(t,n){return Gt(Jt,function(n,e){return t(e)},n)},Zt=function(n){return Ke(n,"table")},nr=function(l,f,s){var d=function(e){return function(n){return s!==undefined&&s(n)||Zn(n,e)}};return Zn(l,f)?S.some({boxes:S.some([l]),start:l,finish:f}):Zt(l).bind(function(a){return Zt(f).bind(function(u){if(Zn(a,u))return S.some({boxes:(o=l,i=f,c=Vt(a),Ft(c,o,i)),start:l,finish:f});if(ee(a,u)){var n=0<(e=Le(f,"td,th",d(a))).length?e[e.length-1]:f;return S.some({boxes:qt(a,l,a,f,u),start:l,finish:n})}if(ee(u,a)){var e,t=0<(e=Le(l,"td,th",d(u))).length?e[e.length-1]:l;return S.some({boxes:qt(u,l,a,f,u),start:l,finish:t})}return Yt(Jt,l,f,r).shared().bind(function(n){return Ge(n,"table",s).bind(function(n){var e=Le(f,"td,th",d(n)),t=0<e.length?e[e.length-1]:f,r=Le(l,"td,th",d(n)),o=0<r.length?r[r.length-1]:l;return S.some({boxes:qt(n,l,a,f,u),start:o,finish:t})})});var r,o,i,c})})},er=function(n,e){var t=qe(n,e);return 0<t.length?S.some(t):S.none()},tr=function(n,e,r){return Xe(n,e).bind(function(t){return Xe(n,r).bind(function(e){return Qt(Zt,[t,e]).map(function(n){return{first:b(t),last:b(e),table:b(n)}})})})},rr=function(n,e,t,r,o){return u=o,W(n,function(n){return Jn(n,u)}).bind(function(n){return Ht(n,e,t).bind(function(n){return t=r,Ke(e=n,"table").bind(function(n){return Xe(n,t).bind(function(n){return nr(n,e).bind(function(e){return e.boxes.map(function(n){return{boxes:n,start:e.start,finish:e.finish}})})})});var e,t})});var u},or=function(r,n,e){return tr(r,n,e).bind(function(i){var n=function(n){return Zn(r,n)},e=Ke(i.first(),"thead,tfoot,tbody,table",n),t=Ke(i.last(),"thead,tfoot,tbody,table",n);return e.bind(function(u){return t.bind(function(n){return Zn(u,n)?(e=i.table(),t=i.first(),r=i.last(),o=Vt(e),zt(o,t,r)):S.none();var e,t,r,o})})})},ur="data-mce-selected",ir="data-mce-first-selected",cr="data-mce-last-selected",ar=ur,lr="td[data-mce-selected],th[data-mce-selected]",fr="[data-mce-selected]",sr=ir,dr="td[data-mce-first-selected],th[data-mce-first-selected]",mr=cr,gr="td[data-mce-last-selected],th[data-mce-last-selected]",pr=/* */Object.freeze({__proto__:null,selected:ar,selectedSelector:lr,attributeSelector:fr,firstSelected:sr,firstSelectedSelector:dr,lastSelected:mr,lastSelectedSelector:gr}),hr=function(i){if(!p(i))throw new Error("cases must be an array");if(0===i.length)throw new Error("there must be at least one case");var c=[],t={};return E(i,function(n,r){var e=V(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],u=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!p(u))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==u.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+u.length+" ("+u+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==i.length)throw new Error("Wrong number of arguments to fold. Expected "+i.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=V(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!L(c,function(n){return B(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){h.console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},vr=hr([{none:[]},{multiple:["elements"]},{single:["selection"]}]),br=function(n,e,t,r){return n.fold(e,t,r)},wr=vr.none,yr=vr.multiple,Cr=vr.single,Sr=function(n,e){return br(e.get(),b([]),u,b([n]))},xr=function(n){return{element:b(n),mergable:S.none,unmergable:S.none,selection:b([n])}},Tr=function(n,e,t){return{element:b(t),mergable:b((o=e,br(n.get(),S.none,function(e,n){return 0===e.length?S.none():or(o,dr,gr).bind(function(n){return 1<e.length?S.some({bounds:b(n),cells:b(e)}):S.none()})},S.none))),unmergable:b(0<(r=Sr(t,n)).length&&L(r,function(n){return dn(n,"rowspan")&&1<parseInt(fn(n,"rowspan"),10)||dn(n,"colspan")&&1<parseInt(fn(n,"colspan"),10)})?S.some(r):S.none()),selection:b(Sr(t,n))};var r,o},Rr=function(m,n,g,p){m.on("BeforeGetContent",function(t){!0===t.selection&&br(n.get(),w,function(n){t.preventDefault(),ct(n[0]).map(Dt).map(function(n){return[gt(n,fr)]}).each(function(n){var e;t.content="text"===t.format?k(n,function(n){return n.dom().innerText}).join(""):(e=m,k(n,function(n){return e.selection.serializer.serialize(n.dom(),{})}).join(""))})},w)}),m.on("BeforeSetContent",function(d){!0===d.selection&&!0===d.paste&&S.from(m.dom.getParent(m.selection.getStart(),"th,td")).each(function(n){var s=Sn.fromDom(n);ct(s).each(function(e){var n,t,r,o,u,i,c=M((n=d.content,(r=(t||h.document).createElement("div")).innerHTML=n,ae(Sn.fromDom(r))),function(n){return"meta"!==Q(n)});if(1===c.length&&(i=c[0],"table"===Q(i))){d.preventDefault();var a=Sn.fromDom(m.getDoc()),l=Mt(a),f=(o=s,u=c[0],{element:b(o),clipboard:b(u),generators:b(l)});g.pasteCells(e,f).each(function(n){m.selection.setRng(n),m.focus(),p.clear(e)})}})})})},Or=function(t,r){return{left:b(t),top:b(r),translate:function(n,e){return Or(t+n,r+e)}}},Dr=Or,Ar=function(n,e){return n!==undefined?n:e!==undefined?e:0},Br=function(n){var e=n.dom().ownerDocument,t=e.body,r=e.defaultView,o=e.documentElement;if(t===n.dom())return Dr(t.offsetLeft,t.offsetTop);var u=Ar(r.pageYOffset,o.scrollTop),i=Ar(r.pageXOffset,o.scrollLeft),c=Ar(o.clientTop,t.clientTop),a=Ar(o.clientLeft,t.clientLeft);return Ir(n).translate(i-a,u-c)},Ir=function(n){var e,t=n.dom(),r=t.ownerDocument.body;return r===t?Dr(r.offsetLeft,r.offsetTop):ve(n)?(e=t.getBoundingClientRect(),Dr(e.left,e.top)):Dr(0,0)},Pr=function(n){var e=S.from(n.dom().documentElement).map(Sn.fromDom).getOr(n);return{parent:b(e),view:b(n),origin:b(Dr(0,0))}},kr=function(n,e){return{parent:b(e),view:b(n),origin:b(Dr(0,0))}};function Er(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=Se(n,r);return parseFloat(t)||0}return e},u=function(o,n){return _(n,function(n,e){var t=Se(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!R(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom();yn(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:u,max:function(n,e,t){var r=u(n,t);return r<e?e-r:0}}}var Mr=Er("height",function(n){var e=n.dom();return ve(n)?e.getBoundingClientRect().height:e.offsetHeight}),Nr=function(n){return Mr.get(n)},_r=function(n){return Mr.getOuter(n)},Wr=Er("width",function(n){return n.dom().offsetWidth}),jr=function(n){return Wr.get(n)},zr=function(n){return Wr.getOuter(n)},Fr=function(n,e){return{row:n,y:e}},Lr=function(n,e){return{col:n,x:e}},Hr=function(n){return Br(n).left()+zr(n)},qr=function(n){return Br(n).left()},Vr=function(n,e){return Lr(n,qr(e))},Ur=function(n,e){return Lr(n,Hr(e))},Kr=function(n){return Br(n).top()},$r=function(n,e){return Fr(n,Kr(e))},Xr=function(n,e){return Fr(n,Kr(e)+_r(e))},Gr=function(t,e,r){if(0===r.length)return[];var n=k(r.slice(1),function(n,e){return n.map(function(n){return t(e,n)})}),o=r[r.length-1].map(function(n){return e(r.length-1,n)});return n.concat([o])},Yr={delta:u,positions:function(n){return Gr($r,Xr,n)},edge:Kr},Jr={delta:u,edge:qr,positions:function(n){return Gr(Vr,Ur,n)}},Qr={delta:function(n){return-n},edge:Hr,positions:function(n){return Gr(Ur,Vr,n)}},Zr=function(t){var n=t.grid,e=P(n.columns(),u),r=P(n.rows(),u);return k(e,function(e){return no(function(){return F(r,function(n){return mt.getAt(t,n,e).filter(function(n){return n.column()===e}).fold(b([]),function(n){return[n]})})},function(n){return 1===n.colspan()},function(){return mt.getAt(t,0,e)})})},no=function(n,e,t){var r=n();return W(r,e).orThunk(function(){return S.from(r[0]).orThunk(t)}).map(function(n){return n.element()})},eo=function(t){var n=t.grid,e=P(n.rows(),u),r=P(n.columns(),u);return k(e,function(e){return no(function(){return F(r,function(n){return mt.getAt(t,e,n).filter(function(n){return n.row()===e}).fold(b([]),function(n){return[n]})})},function(n){return 1===n.rowspan()},function(){return mt.getAt(t,e,0)})})},to=function(r,o){if(o<0||o>=r.length-1)return S.none();var n=r[o].fold(function(){var n,e,t=(n=r.slice(0,o),(e=O.call(n,0)).reverse(),e);return q(t,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return S.some({value:n,delta:0})}),e=r[o+1].fold(function(){var n=r.slice(o+1);return q(n,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return S.some({value:n,delta:1})});return n.bind(function(t){return e.map(function(n){var e=n.delta+t.delta;return Math.abs(n.value-t.value)/e})})},ro=function(){var n=Yn().browser;return n.isIE()||n.isEdge()},oo=function(n,e,t){return r=Se(n,e),o=t,u=parseFloat(r),isNaN(u)?o:u;var r,o,u},uo=function(n){return ro()?(t=(e=n).dom().getBoundingClientRect().height,"border-box"===Se(e,"box-sizing")?t:t-oo(e,"padding-top",0)-oo(e,"padding-bottom",0)-(oo(e,"border-top-width",0)+oo(e,"border-bottom-width",0))):oo(n,"height",Nr(n));var e,t},io=function(n){return ro()?(t=(e=n).dom().getBoundingClientRect().width,"border-box"===Se(e,"box-sizing")?t:t-oo(e,"padding-left",0)-oo(e,"padding-right",0)-(oo(e,"border-left-width",0)+oo(e,"border-right-width",0))):oo(n,"width",jr(n));var e,t},co=/(\d+(\.\d+)?)(\w|%)*/,ao=/(\d+(\.\d+)?)%/,lo=/(\d+(\.\d+)?)px|em/,fo=function(n,e){var t,r=(t=n,S.from(t.dom().offsetParent).map(Sn.fromDom).getOr(be(re(n))));return e(n)/e(r)*100},so=function(n,e){ye(n,"width",e+"px")},mo=function(n,e){ye(n,"width",e+"%")},go=function(n,e){ye(n,"height",e+"px")},po=function(n,e,t,r){var o,u,i,c,a,l=parseInt(n,10);return vn(n,"%")&&"table"!==Q(e)?(u=l,i=t,c=r,a=ct(o=e).map(function(n){var e=i(n);return Math.floor(u/100*e)}).getOr(u),c(o,a),a):l},ho=function(n){var e,t=Te(e=n,"height").getOrThunk(function(){return uo(e)+"px"});return t?po(t,n,Nr,go):Nr(n)},vo=function(n){return Te(n,"width").fold(function(){return S.from(fn(n,"width"))},function(n){return S.some(n)})},bo=function(n,e){return n/e.pixelWidth()*100},wo=function(e,t){return vo(e).fold(function(){var n=jr(e);return bo(n,t)},function(n){return function(n,e,t){var r=ao.exec(e);if(null!==r)return parseFloat(r[1]);var o=io(n);return bo(o,t)}(e,n,t)})},yo=function(e,t){return vo(e).fold(function(){return io(e)},function(n){return function(n,e,t){var r=lo.exec(e);if(null!==r)return parseInt(r[1],10);var o=ao.exec(e);if(null===o)return io(n);var u=parseFloat(o[1]);return u/100*t.pixelWidth()}(e,n,t)})},Co=function(n){return t="rowspan",ho(e=n)/Je(e,t);var e,t},So=function(n,e,t){ye(n,"width",e+t)},xo=function(n){return fo(n,jr)+"%"},To=b(ao),Ro=b(lo),Oo=function(n,e,t){return Te(n,e).fold(function(){return t(n)+"px"},function(n){return n})},Do=function(n,e){return Oo(n,"width",function(n){return yo(n,e)})},Ao=function(n){return Oo(n,"height",Co)},Bo=function(n,e,t,r,o){var u=Zr(n),i=k(u,function(n){return n.map(e.edge)});return k(u,function(n,e){return n.filter(d(Qe)).fold(function(){var n=to(i,e);return r(n)},function(n){return t(n,o)})})},Io=function(n){return n.map(function(n){return n+"px"}).getOr("")},Po=function(n,e,t){return Bo(n,e,wo,function(n){return n.fold(function(){return t.minCellWidth()},function(n){return n/t.pixelWidth()*100})},t)},ko=function(n,e,t){return Bo(n,e,yo,function(n){return n.getOrThunk(t.minCellWidth)},t)},Eo=function(n,e,t,r){var o=eo(n),u=k(o,function(n){return n.map(e.edge)});return k(o,function(n,e){return n.filter(d(Ze)).fold(function(){var n=to(u,e);return r(n)},function(n){return t(n)})})},Mo=hr([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),No=function(n,e,t){var r=t.substring(0,t.length-n.length),o=parseFloat(r);return r===o.toString()?e(o):Mo.invalid(t)},_o=xn(xn({},Mo),{from:function(n){return vn(n,"%")?No("%",Mo.percent,n):vn(n,"px")?No("px",Mo.pixels,n):Mo.invalid(n)}}),Wo=function(n,r,o){return n.fold(function(){return r},function(n){return t=(e=n)/o,k(r,function(n){return _o.from(n).fold(function(){return n},function(n){return n*t+"px"},function(n){return n/100*e+"px"})});var e,t},function(n){return e=o,k(r,function(n){return _o.from(n).fold(function(){return n},function(n){return n/e*100+"%"},function(n){return n+"%"})});var e})},jo=function(n,e,t){var r,o,u,i=_o.from(t),c=L(n,function(n){return"0px"===n})?(r=i,o=n.length,u=r.fold(function(){return b("")},function(n){return b(n/o+"px")},function(n){return b(n/o+"px")}),P(o,u)):Wo(i,n,e);return Fo(c)},zo=function(n,e){return 0===n.length?e:N(n,function(n,e){return _o.from(e).fold(b(0),u,u)+n},0)},Fo=function(n){if(0===n.length)return n;var e,t,r=N(n,function(n,e){var t=_o.from(e).fold(function(){return{value:e,remainder:0}},function(n){return e=n,t="px",{value:(r=Math.floor(e))+t,remainder:e-r};var e,t,r},function(n){return{value:n+"%",remainder:0}});return{output:[t.value].concat(n.output),remainder:n.remainder+t.remainder}},{output:[],remainder:0}),o=r.output;return o.slice(0,o.length-1).concat([(e=o[o.length-1],t=Math.round(r.remainder),_o.from(e).fold(b(e),function(n){return n+t+"px"},function(n){return n+t+"%"}))])},Lo=_o.from,Ho=function(n){return Lo(n).fold(b("px"),b("px"),b("%"))},qo=function(a,n,e,c,l){var f=mt.fromTable(a),s=f.all,d=mt.justCells(f);n.each(function(n){var r,o,e=Ho(n),t=jr(a),u=Bo(f,c,Do,Io,l),i=jo(u,t,n);r=i,o=e,E(d,function(n){var e=r.slice(n.column(),n.colspan()+n.column()),t=zo(e,et());ye(n.element(),"width",t+o)}),ye(a,"width",n)}),e.each(function(n){var r,e,o,t=Ho(n),u=Nr(a),i=Eo(f,Yr,Ao,Io),c=jo(i,u,n);r=c,e=s,o=t,E(d,function(n){var e=r.slice(n.row(),n.rowspan()+n.row()),t=zo(e,tt());ye(n.element(),"height",t+o)}),E(e,function(n,e){ye(n.element(),"height",r[e])}),ye(a,"height",n)})},Vo=function(n){return vo(n).exists(function(n){return ao.test(n)})},Uo=function(n){return vo(n).exists(function(n){return lo.test(n)})},Ko=function(n){return vo(n).isNone()},$o=xo,Xo={ltr:Jr,rtl:Qr},Go=function(e){var t=function(n){return e(n).isRtl()?Xo.rtl:Xo.ltr};return{delta:function(n,e){return t(e).delta(n,e)},edge:function(n){return t(n).edge(n)},positions:function(n,e){return t(e).positions(n,e)}}},Yo=function(n){var r=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return E(e,function(n,e){r[n]=b(t[e])}),r}}.apply(null,n),o=[];return{bind:function(n){if(n===undefined)throw new Error("Event bind error: undefined handler");o.push(n)},unbind:function(e){o=M(o,function(n){return n!==e})},trigger:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=r.apply(null,n);E(o,function(n){n(t)})}}},Jo=function(n){return{registry:$(n,function(n){return{bind:n.bind,unbind:n.unbind}}),trigger:$(n,function(n){return n.trigger})}},Qo=hr([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),Zo=xn({},Qo),nu=function(n,e,u,i){var t,r,c=n.slice(0),o=(r=e,0===(t=n).length?Zo.none():1===t.length?Zo.only(0):0===r?Zo.left(0,1):r===t.length-1?Zo.right(r-1,r):0<r&&r<t.length-1?Zo.middle(r-1,r,r+1):Zo.none()),a=function(n){return k(n,b(0))},l=b(a(c)),f=function(n,e){if(0<=u){var t=Math.max(i.minCellWidth(),c[e]-u);return a(c.slice(0,n)).concat([u,t-c[e]]).concat(a(c.slice(e+1)))}var r=Math.max(i.minCellWidth(),c[n]+u),o=c[n]-r;return a(c.slice(0,n)).concat([r-c[n],o]).concat(a(c.slice(e+1)))},s=f;return o.fold(l,function(n){return i.singleColumnWidth(c[n],u)},s,function(n,e,t){return f(e,t)},function(n,e){if(0<=u)return a(c.slice(0,e)).concat([u]);var t=Math.max(i.minCellWidth(),c[e]+u);return a(c.slice(0,e)).concat([t-c[e]])})},eu=function(n,e,t){for(var r=0,o=n;o<e;o++)r+=t[o]!==undefined?t[o]:0;return r},tu=function(n,t){var e=mt.justCells(n);return k(e,function(n){var e=eu(n.column(),n.column()+n.colspan(),t);return{element:n.element(),width:e,colspan:n.colspan()}})},ru=function(n,t,r,e){var o,u,i,c,a=mt.fromTable(n),l=Eo(a,e,Co,function(n){return n.getOrThunk(tt)}),f=k(l,function(n,e){return r===e?Math.max(t+n,tt()):n}),s=(o=a,u=f,i=mt.justCells(o),k(i,function(n){var e=eu(n.row(),n.row()+n.rowspan(),u);return{element:n.element,height:b(e),rowspan:n.rowspan}})),d=(c=f,k(a.all,function(n,e){return{element:n.element,height:b(c[e])}}));E(d,function(n){go(n.element(),n.height())}),E(s,function(n){go(n.element(),n.height())});var m=N(f,function(n,e){return n+e},0);go(n,m)},ou=function(n){var t,r,e,o,u,i=Sn.fromDom(pe(n).getOr(n.target)),c=function(){return n.stopPropagation()},a=function(){return n.preventDefault()},l=(t=a,r=c,function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))});return e=i,o=n.clientX,u=n.clientY,{target:b(e),x:b(o),y:b(u),stop:c,prevent:a,kill:l,raw:b(n)}},uu=function(n,e,t,r,o){var u,i,c=(u=t,i=r,function(n){u(n)&&i(ou(n))});return n.dom().addEventListener(e,c,o),{unbind:y(iu,n,e,c,o)}},iu=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},cu=b(!0),au=function(n,e,t){return uu(n,e,cu,t,!1)},lu=ou,fu=function(n,e){var t=fn(n,e);return t===undefined||""===t?[]:t.split(" ")},su=function(n){return n.dom().classList!==undefined},du=function(n,e){return o=e,u=fu(t=n,r="class").concat([o]),an(t,r,u.join(" ")),!0;var t,r,o,u},mu=function(n,e){return o=e,0<(u=M(fu(t=n,r="class"),function(n){return n!==o})).length?an(t,r,u.join(" ")):mn(t,r),!1;var t,r,o,u},gu=function(n,e){su(n)?n.dom().classList.add(e):du(n,e)},pu=function(n){0===(su(n)?n.dom().classList:fu(n,"class")).length&&mn(n,"class")},hu=function(n,e){return su(n)&&n.dom().classList.contains(e)},vu=function(n){var e=n.replace(/\./g,"-");return{resolve:function(n){return e+"-"+n}}},bu=vu("ephox-dragster").resolve,wu=function(n){return n.slice(0).sort()},yu=function(r,o,u){if(0===o.length)throw new Error("You must specify at least one required field.");var t;return function(e,n){if(!p(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");E(n,function(n){if(!m(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}("required",o),t=wu(o),W(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}),function(e){var t=V(e);L(o,function(n){return B(t,n)})||function(n,e){throw new Error("All required keys ("+wu(n).join(", ")+") were not specified. Specified keys were: "+wu(e).join(", ")+".")}(o,t),r(o,t);var n=M(o,function(n){return!u.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+wu(n).join(", ")+") were not.")}(n,u.label),e}},Cu=function(e,n){var t=M(n,function(n){return!B(e,n)});0<t.length&&function(n){throw new Error("Unsupported keys for object: "+wu(n).join(", "))}(t)},Su=function(n){return yu(Cu,n,{validate:T,label:"function"})},xu=Su(["compare","extract","mutate","sink"]),Tu=Su(["element","start","stop","destroy"]),Ru=Su(["forceDrop","drop","move","delayDrop"]),Ou=xu({compare:function(n,e){return Dr(e.left()-n.left(),e.top()-n.top())},extract:function(n){return S.some(Dr(n.x(),n.y()))},sink:function(n,e){var t=function(n){var e=xn({layerClass:bu("blocker")},n),t=Sn.fromTag("div");an(t,"role","presentation"),Ce(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),gu(t,bu("blocker")),gu(t,e.layerClass);return{element:function(){return t},destroy:function(){Me(t)}}}(e),r=au(t.element(),"mousedown",n.forceDrop),o=au(t.element(),"mouseup",n.drop),u=au(t.element(),"mousemove",n.move),i=au(t.element(),"mouseout",n.delayDrop);return Tu({element:t.element,start:function(n){Be(n,t.element())},stop:function(){Me(t.element())},destroy:function(){t.destroy(),o.unbind(),u.unbind(),i.unbind(),r.unbind()}})},mutate:function(n,e){n.mutate(e.left(),e.top())}});function Du(){var u=S.none(),i=Jo({move:Yo(["info"])});return{onEvent:function(n,o){o.extract(n).each(function(n){var e,t,r;(e=o,t=n,r=u.map(function(n){return e.compare(n,t)}),u=S.some(t),r).each(function(n){i.trigger.move(n)})})},reset:function(){u=S.none()},events:i.registry}}function Au(){var n=function r(){return{onEvent:w,reset:w}}(),e=Du(),t=n;return{on:function(){t.reset(),t=e},off:function(){t.reset(),t=n},isOn:function(){return t===e},onEvent:function(n,e){t.onEvent(n,e)},events:e.events}}var Bu=function(e,t,n){var r,o,u,i=!1,c=Jo({start:Yo([]),stop:Yo([])}),a=Au(),l=function(){d.stop(),a.isOn()&&(a.off(),c.trigger.stop())},f=(r=l,o=200,u=null,{cancel:function(){null!==u&&(h.clearTimeout(u),u=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==u&&h.clearTimeout(u),u=h.setTimeout(function(){r.apply(null,n),u=null},o)}});a.events.move.bind(function(n){t.mutate(e,n.info())});var s=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i&&t.apply(null,n)}},d=t.sink(Ru({forceDrop:l,drop:s(l),move:s(function(n){f.cancel(),a.onEvent(n,t)}),delayDrop:s(f.throttle)}),n);return{element:d.element,go:function(n){d.start(n),a.on(),c.trigger.start()},on:function(){i=!0},off:function(){i=!1},destroy:function(){d.destroy()},events:c.registry}},Iu=function(n){return"true"===fn(n,"contenteditable")},Pu=vu("ephox-snooker").resolve,ku=function(){var t,r=Jo({drag:Yo(["xDelta","yDelta","target"])}),o=S.none(),n={mutate:function(n,e){t.trigger.drag(n,e)},events:(t=Jo({drag:Yo(["xDelta","yDelta"])})).registry};n.events.drag.bind(function(e){o.each(function(n){r.trigger.drag(e.xDelta(),e.yDelta(),n)})});return{assign:function(n){o=S.some(n)},get:function(){return o},mutate:n.mutate,events:r.registry}},Eu=Pu("resizer-bar"),Mu=Pu("resizer-rows"),Nu=Pu("resizer-cols"),_u=function(n){var e=qe(n.parent(),"."+Eu);E(e,Me)},Wu=function(t,n,r){var o=t.origin();E(n,function(n){n.each(function(n){var e=r(o,n);gu(e,Eu),Be(t.parent(),e)})})},ju=function(n,e,l,f){Wu(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.col,r=e.x-n.left(),o=l.top()-n.top(),u=7,i=f,c=Sn.fromTag("div"),Ce(c,{position:"absolute",left:r-u/2+"px",top:o+"px",height:i+"px",width:u+"px"}),ln(c,{"data-column":t,role:"presentation"}),c);return gu(a,Nu),a})},zu=function(n,e,l,f){Wu(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.row,r=l.left()-n.left(),o=e.y-n.top(),u=f,i=7,c=Sn.fromTag("div"),Ce(c,{position:"absolute",left:r+"px",top:o-i/2+"px",height:i+"px",width:u+"px"}),ln(c,{"data-row":t,role:"presentation"}),c);return gu(a,Mu),a})},Fu=function(n,e,t,r){_u(n);var o=mt.fromTable(e);!function(n,e,t,r,o,u){var i=Br(e),c=0<t.length?o.positions(t,e):[];zu(n,c,i,zr(e));var a=0<r.length?u.positions(r,e):[];ju(n,a,i,_r(e))}(n,e,eo(o),Zr(o),t,r)},Lu=function(n,e){var t=qe(n.parent(),"."+Eu);E(t,e)},Hu=function(n){Lu(n,function(n){ye(n,"display","none")})},qu=function(n){Lu(n,function(n){ye(n,"display","block")})},Vu=Pu("resizer-bar-dragging"),Uu=function(o,e,u){var t=ku(),r=function(n,e){void 0===e&&(e={});var t=e.mode!==undefined?e.mode:Ou;return Bu(n,t,e)}(t,{}),i=S.none(),n=function(n,e){return S.from(fn(n,e))};t.events.drag.bind(function(t){n(t.target(),"data-row").each(function(n){var e=nt(t.target(),"top");ye(t.target(),"top",e+t.yDelta()+"px")}),n(t.target(),"data-column").each(function(n){var e=nt(t.target(),"left");ye(t.target(),"left",e+t.xDelta()+"px")})});var c=function(n,e){return nt(n,e)-Ye(n,"data-initial-"+e,0)};r.events.stop.bind(function(){t.get().each(function(r){i.each(function(t){n(r,"data-row").each(function(n){var e=c(r,"top");mn(r,"data-initial-top"),m.trigger.adjustHeight(t,e,parseInt(n,10))}),n(r,"data-column").each(function(n){var e=c(r,"left");mn(r,"data-initial-left"),m.trigger.adjustWidth(t,e,parseInt(n,10))}),Fu(o,t,u,e)})})});var a=function(n,e){m.trigger.startAdjust(),t.assign(n),an(n,"data-initial-"+e,nt(n,e)),gu(n,Vu),ye(n,"opacity","0.2"),r.go(o.parent())},l=au(o.parent(),"mousedown",function(n){var e,t;e=n.target(),hu(e,Mu)&&a(n.target(),"top"),t=n.target(),hu(t,Nu)&&a(n.target(),"left")}),f=function(n){return Zn(n,o.view())},s=function(n){return Ge(n,"table",f).filter(function(n){return Ge(n,"[contenteditable]",f).exists(Iu)})},d=au(o.view(),"mouseover",function(n){s(n.target()).fold(function(){ve(n.target())&&_u(o)},function(n){i=S.some(n),Fu(o,n,u,e)})}),m=Jo({adjustHeight:Yo(["table","delta","row"]),adjustWidth:Yo(["table","delta","column"]),startAdjust:Yo([])});return{destroy:function(){l.unbind(),d.unbind(),r.destroy(),_u(o)},refresh:function(n){Fu(o,n,u,e)},on:r.on,off:r.off,hideBars:y(Hu,o),showBars:y(qu,o),events:m.registry}},Ku=function(n,p,h){var r=Yr,e=Uu(n,p,r),v=Jo({beforeResize:Yo(["table"]),afterResize:Yo(["table"]),startDrag:Yo([])});return e.events.adjustHeight.bind(function(n){var e=n.table();v.trigger.beforeResize(e);var t=r.delta(n.delta(),e);ru(e,t,n.row(),r),v.trigger.afterResize(e)}),e.events.startAdjust.bind(function(n){v.trigger.startDrag()}),e.events.adjustWidth.bind(function(n){var e=n.table();v.trigger.beforeResize(e);var t,r,o,u,i,c,a,l,f,s,d,m=p.delta(n.delta(),e),g=h(e);t=e,r=m,o=n.column(),u=p,c=(i=g).getCellDelta(r),a=mt.fromTable(t),l=i.getWidths(a,u,i),f=nu(l,o,c,i),s=k(f,function(n,e){return n+l[e]}),d=tu(a,s),E(d,function(n){i.setElementWidth(n.element,n.width)}),o===a.grid.columns()-1&&i.adjustTableWidth(c),v.trigger.afterResize(e)}),{on:e.on,off:e.off,hideBars:e.hideBars,showBars:e.showBars,destroy:e.destroy,events:v.registry}},$u=function(n,e){return n.fire("newrow",{node:e})},Xu=function(n,e){return n.fire("newcell",{node:e})},Gu=function(n,e,t,r,o){n.fire("TableSelectionChange",{cells:e,start:t,finish:r,otherCells:o})},Yu=function(n){n.fire("TableSelectionClear")},Ju={"border-collapse":"collapse",width:"100%"},Qu={border:"1"},Zu=function(n){return n.getParam("table_sizing_mode","auto")},ni=function(n){return n.getParam("table_responsive_width")},ei=function(n){return n.getParam("table_default_attributes",Qu,"object")},ti=function(n){return n.getParam("table_default_styles",function(n){if(li(n)){var e=n.getBody().offsetWidth;return xn(xn({},Ju),{width:e+"px"})}return fi(n)?G(Ju,function(n,e){return"width"!==e}):Ju}(n),"object")},ri=function(n){return n.getParam("table_tab_navigation",!0,"boolean")},oi=function(n){return n.getParam("table_cell_advtab",!0,"boolean")},ui=function(n){return n.getParam("table_row_advtab",!0,"boolean")},ii=function(n){return n.getParam("table_advtab",!0,"boolean")},ci=function(n){return n.getParam("table_style_by_css",!1,"boolean")},ai=function(n){return"relative"===Zu(n)||!0===ni(n)},li=function(n){return"fixed"===Zu(n)||!1===ni(n)},fi=function(n){return"responsive"===Zu(n)},si=function(n){var e="section",t=n.getParam("table_header_type",e,"string");return B(["section","cells","sectionCells","auto"],t)?t:e},di=function(n){var e=n.getParam("table_clone_elements");return m(e)?S.some(e.split(/[ ,]/)):Array.isArray(e)?S.some(e):S.none()},mi=function(n){return n.nodeName.toLowerCase()},gi=function(n){return Sn.fromDom(n.getBody())},pi=function(n){return n.getBoundingClientRect().width},hi=function(n){return n.getBoundingClientRect().height},vi=function(e){return function(n){return Zn(n,gi(e))}},bi=function(n){return/^\d+(\.\d+)?$/.test(n)?n+"px":n},wi=function(n){mn(n,"data-mce-style"),E(it(n),function(n){return mn(n,"data-mce-style")})},yi=function(n,e){var t=n.dom.getStyle(e,"width")||n.dom.getAttrib(e,"width");return S.from(t).filter(wn)},Ci=function(n){return/^(\d+(\.\d+)?)%$/.test(n)},Si={isRtl:b(!1)},xi={isRtl:b(!0)},Ti=function(n){return"rtl"==("rtl"===Se(n,"direction")?"rtl":"ltr")?xi:Si},Ri=function(n){var e=n;return{get:function(){return e},set:function(n){e=n}}},Oi=function(n){var e=function(){return jr(n)},t=b(0);return{width:e,pixelWidth:e,getWidths:ko,getCellDelta:t,singleColumnWidth:b([0]),minCellWidth:t,setElementWidth:w,adjustTableWidth:w,label:"none"}},Di=function(n,r){var o=Ri(parseFloat(n)),u=Ri(jr(r));return{width:o.get,pixelWidth:u.get,getWidths:Po,getCellDelta:function(n){return n/u.get()*100},singleColumnWidth:function(n,e){return[100-n]},minCellWidth:function(){return et()/u.get()*100},setElementWidth:mo,adjustTableWidth:function(n){var e=o.get(),t=e+n/100*e;mo(r,t),o.set(t),u.set(jr(r))},label:"percent"}},Ai=function(n,t){var r=Ri(n),o=r.get;return{width:o,pixelWidth:o,getWidths:ko,getCellDelta:u,singleColumnWidth:function(n,e){return[Math.max(et(),n+e)-n]},minCellWidth:et,setElementWidth:so,adjustTableWidth:function(n){var e=o()+n;so(t,e),r.set(e)},label:"pixel"}},Bi=function(e){return vo(e).fold(function(){return Oi(e)},function(n){return function(n,e){var t=To().exec(e);if(null!==t)return Di(t[1],n);var r=Ro().exec(e);if(null!==r){var o=parseInt(r[1],10);return Ai(o,n)}var u=jr(n);return Ai(u,n)}(e,n)})},Ii=Ai,Pi=Di,ki=function(n,e){if(ai(n)){var t=yi(n,e.dom()).filter(Ci).getOrThunk(function(){return $o(e)});return Pi(t,e)}return li(n)?Ii(jr(e),e):Bi(e)},Ei=function(n){mn(n,"width")},Mi=function(n,e,t){var r=jr(n)+"px";qo(n,S.some(r),S.none(),e,t),Ei(n)},Ni=function(n,e){var t,r,o,u,i=Go(Ti),c=ki(n,e);r=i,o=c,u=xo(t=e),qo(t,S.some(u),S.none(),r,o),Ei(t)},_i=function(n,e){var t=Go(Ti),r=ki(n,e);Mi(e,t,r)},Wi=function(n){Re(n,"width"),E(it(n),function(n){Re(n,"width"),Ei(n)}),Ei(n)},ji=function(){var n=Sn.fromTag("div");return Ce(n,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Be(be(Sn.fromDom(h.document)),n),n},zi=function(c){var u,i,a=S.none(),l=S.none(),f=S.none(),s=function(n){return"TABLE"===n.nodeName},n=function(){return l};return c.on("init",function(){var n,e,t=Go(Ti),r=(n=c).inline?kr(gi(n),ji()):Pr(Sn.fromDom(n.getDoc()));if(f=S.some(r),e=c.getParam("object_resizing",!0),(m(e)?"table"===e:e)&&c.getParam("table_resize_bars",!0,"boolean")){var o=Ku(r,t,function(n){return ki(c,n)});o.on(),o.events.startDrag.bind(function(n){a=S.some(c.selection.getRng())}),o.events.beforeResize.bind(function(n){var e,t,r,o,u=n.table().dom();e=c,r=pi(t=u),o=hi(u),e.fire("ObjectResizeStart",{target:t,width:r,height:o})}),o.events.afterResize.bind(function(n){var e,t,r,o,u=n.table(),i=u.dom();wi(u),a.each(function(n){c.selection.setRng(n),c.focus()}),e=c,r=pi(t=i),o=hi(i),e.fire("ObjectResized",{target:t,width:r,height:o}),c.undoManager.add()}),l=S.some(o)}}),c.on("ObjectResizeStart",function(n){var e=n.target;if(s(e)){var t=Sn.fromDom(e);!Uo(t)&&li(c)?_i(c,t):!Vo(t)&&ai(c)&&Ni(c,t),u=n.width,i=yi(c,e).getOr("")}}),c.on("ObjectResized",function(n){var e=n.target;if(s(e)){var t=Sn.fromDom(e);if(""===i||!Ci(i)&&fi(c))Ni(c,t);else if(Ci(i)){var r=parseFloat(i.replace("%","")),o=n.width*r/u;ye(t,"width",o+"%")}else E(it(t),function(n){var e=Se(n,"width");ye(n,"width",e),mn(n,"width")});wi(t)}}),c.on("SwitchMode",function(){l.each(function(n){c.mode.isReadOnly()?n.hideBars():n.showBars()})}),{lazyResize:n,lazyWire:function(){return f.getOr(Pr(Sn.fromDom(c.getBody())))},destroy:function(){l.each(function(n){n.destroy()}),f.each(function(n){var e;e=n,c.inline&&Me(e.parent())})}}},Fi=function(n,e){return{element:b(n),offset:b(e)}},Li=function(e,n,t){return e.property().isText(n)&&0===e.property().getText(n).trim().length||e.property().isComment(n)?t(n).bind(function(n){return Li(e,n,t).orThunk(function(){return S.some(n)})}):S.none()},Hi=function(n,e){return n.property().isText(e)?n.property().getText(e).length:n.property().children(e).length},qi=function(n,e){var t=Li(n,e,n.query().prevSibling).getOr(e);if(n.property().isText(t))return Fi(t,Hi(n,t));var r=n.property().children(t);return 0<r.length?qi(n,r[r.length-1]):Fi(t,Hi(n,t))},Vi=qi,Ui=Kt(),Ki=function(t,r){vo(t).bind(function(n){var e=co.exec(n);return null!==e?S.some({width:b(parseFloat(e[1])),unit:b(e[3])}):S.none()}).each(function(n){var e=n.width()/2;So(t,e,n.unit()),So(r,e,n.unit())})},$i=function(n){return mt.fromTable(n).grid},Xi=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Gi=function(n,e,t,r){t===r?mn(n,e):an(n,e,t)},Yi=function(o,n){var u=[],i=[],c=H(He(o,"caption,colgroup")).fold(function(){return y(Ae,o)},function(n){return y(De,n)}),e=function(n,e){0<n.length?function(n,e){var t=$e(o,e).getOrThunk(function(){var n=Sn.fromTag(e,re(o).dom());return"thead"===e?c(n):Be(o,n),n});Ee(t);var r=k(n,function(n){n.isNew()&&u.push(n.element());var e=n.element();return Ee(e),E(n.cells(),function(n){n.isNew()&&i.push(n.element()),Gi(n.element(),"colspan",n.colspan(),1),Gi(n.element(),"rowspan",n.rowspan(),1),Be(e,n.element())}),e});ke(t,r)}(n,e):$e(o,e).each(Me)},t=[],r=[],a=[];return E(n,function(n){switch(n.section()){case"thead":t.push(n);break;case"tbody":r.push(n);break;case"tfoot":a.push(n)}}),e(t,"thead"),e(r,"tbody"),e(a,"tfoot"),{newRows:u,newCells:i}},Ji=function(n,e,t){n.cells()[e]=t},Qi=function(n,e){return ze(e,n.section())},Zi=function(n,e){var t=n.cells(),r=k(t,e);return ze(r,n.section())},nc=function(n,e){return n.cells()[e]},ec=function(n,e){return nc(n,e).element()},tc=function(n){return n.cells().length},rc=function(n,e){if(0===n.length)return 0;var t=n[0];return j(n,function(n){return!e(t.element(),n.element())}).fold(function(){return n.length},function(n){return n})},oc=function(n,e,t,r){var o,u=n[e].cells().slice(t),i=rc(u,r),c=(o=t,k(n,function(n){return nc(n,o)}).slice(e));return{colspan:i,rowspan:rc(c,r)}},uc=function(a,l){var f=k(a,function(n){return k(n.cells(),function(){return!1})});return k(a,function(n,c){var e,t,r=F(n.cells(),function(n,e){if(!1!==f[c][e])return[];var t,r,o,u,i=oc(a,c,e,l);return function(n,e,t,r){for(var o=n;o<n+t;o++)for(var u=e;u<e+r;u++)f[o][u]=!0}(c,e,i.rowspan,i.colspan),[(t=n.element(),r=i.rowspan,o=i.colspan,u=n.isNew(),{element:b(t),rowspan:b(r),colspan:b(o),isNew:b(u)})]});return e=r,t=n.section(),{details:b(e),section:b(t)}})},ic=function(n,e,t){for(var r=[],o=0;o<n.grid.rows();o++){for(var u=[],i=0;i<n.grid.columns();i++){var c=mt.getAt(n,o,i).map(function(n){return je(n.element(),t)}).getOrThunk(function(){return je(e.gap(),!0)});u.push(c)}var a=ze(u,n.all[o].section());r.push(a)}return r},cc=function(n,c){return k(n,function(n){var e,t,r,o,u,i=(e=n.details(),q(e,function(n){return oe(n.element()).map(function(n){var e=oe(n).isNone();return je(n,e)})}).getOrThunk(function(){return je(c.row(),!0)}));return t=i.element(),r=n.details(),o=n.section(),u=i.isNew(),{element:b(t),cells:b(r),section:b(o),isNew:b(u)}})},ac=function(n,e){var t=uc(n,Zn);return cc(t,e)},lc=function(n,e){return q(n.all,function(n){return W(n.cells(),function(n){return Zn(e,n.element())})})},fc=function(l,e,f,s,d){return function(r,o,n,u,i,c){var a=mt.fromTable(o);return e(a,n).map(function(n){var e=ic(a,u,!1),t=l(e,n,Zn,d(u)),r=ac(t.grid(),u);return{grid:b(r),cursor:t.cursor}}).fold(function(){return S.none()},function(n){var e=Yi(o,n.grid()),t=S.from(c).getOrThunk(function(){return Bi(o)});return f(o,n.grid(),i,t),s(o),Fu(r,o,Yr,i),S.some({cursor:n.cursor,newRows:b(e.newRows),newCells:b(e.newCells)})})}},sc=function(e,n){return ut(n.element()).bind(function(n){return lc(e,n)})},dc=function(e,n){var t=k(n.selection(),function(n){return ut(n).bind(function(n){return lc(e,n)})}),r=Xi(t);return 0<r.length?S.some({cells:r,generators:n.generators,clipboard:n.clipboard}):S.none()},mc=function(e,n){var t=k(n.selection(),function(n){return ut(n).bind(function(n){return lc(e,n)})}),r=Xi(t);return 0<r.length?S.some(r):S.none()},gc=function(n,e,t,r){for(var o=!0,u=0;u<n.length;u++)for(var i=0;i<tc(n[0]);i++){var c=t(ec(n[u],i),e);!0===c&&!1===o?Ji(n[u],i,je(r(),!0)):!0===c&&(o=!1)}return n},pc=function(u,t,i,c){if(0<t&&t<u.length){var n=u[t-1].cells(),e=(r=i,_(n,function(n,e){return I(n,function(n){return r(n.element(),e.element())})?n:n.concat([e])},[]));E(e,function(r){for(var o=S.none(),n=function(t){for(var n=function(e){var n=u[t].cells()[e];i(n.element(),r.element())&&(o.isNone()&&(o=S.some(c())),o.each(function(n){Ji(u[t],e,je(n,!0))}))},e=0;e<tc(u[0]);e++)n(e)},e=t;e<u.length;e++)n(e)})}var r;return u},hc=function(t){return{is:function(n){return t===n},isValue:C,isError:f,getOr:b(t),getOrThunk:b(t),getOrDie:b(t),or:function(n){return hc(t)},orThunk:function(n){return hc(t)},fold:function(n,e){return e(t)},map:function(n){return hc(n(t))},mapError:function(n){return hc(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return S.some(t)}}},vc=function(t){return{is:f,isValue:f,isError:C,getOr:u,getOrThunk:function(n){return n()},getOrDie:function(){return n=String(t),function(){throw new Error(n)}();var n},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return vc(t)},mapError:function(n){return vc(n(t))},each:w,bind:function(n){return vc(t)},exists:f,forall:C,toOption:S.none}},bc={value:hc,error:vc,fromOption:function(n,e){return n.fold(function(){return vc(e)},hc)}},wc=function(n,e){return{rowDelta:0,colDelta:tc(n[0])-tc(e[0])}},yc=function(n,e){return{rowDelta:n.length-e.length,colDelta:0}},Cc=function(n,e){return k(n,function(){return je(e.cell(),!0)})},Sc=function(n,e,t){return n.concat(P(e,function(){return Qi(n[n.length-1],Cc(n[n.length-1].cells(),t))}))},xc=function(n,e,t){return k(n,function(n){return Qi(n,n.cells().concat(Cc(P(e,u),t)))})},Tc=function(n,e,t){var r=e.colDelta<0?xc:u;return(e.rowDelta<0?Sc:u)(r(n,Math.abs(e.colDelta),t),Math.abs(e.rowDelta),t)},Rc=function(t,r,o,u,i){return function(n,e,t){if(n.row()>=e.length||n.column()>tc(e[0]))return bc.error("invalid start address out of table bounds, row: "+n.row()+", column: "+n.column());var r=e.slice(n.row()),o=r[0].cells().slice(n.column()),u=tc(t[0]),i=t.length;return bc.value({rowDelta:r.length-i,colDelta:o.length-u})}(t,r,o).map(function(n){var e=Tc(r,n,u);return function(n,e,t,r,o){for(var u,i,c,a,l,f=n.row(),s=n.column(),d=f+t.length,m=s+tc(t[0]),g=f;g<d;g++)for(var p=s;p<m;p++){c=p,l=a=void 0,a=y(o,nc((u=e)[i=g],c).element()),l=u[i],1<u.length&&1<tc(l)&&(0<c&&a(ec(l,c-1))||c<l.cells().length-1&&a(ec(l,c+1))||0<i&&a(ec(u[i-1],c))||i<u.length-1&&a(ec(u[i+1],c)))&&gc(e,ec(e[g],p),o,r.cell);var h=ec(t[g-f],p-s),v=r.replace(h);Ji(e[g],p,je(v,!0))}return e}(t,e,o,u,i)})},Oc=function(r,n,e,t,o){var u,i,c,a;u=n,i=r,c=o,a=t.cell,0<i&&i<u[0].cells().length&&E(u,function(n){var e=n.cells()[i-1],t=n.cells()[i];c(t.element(),e.element())&&Ji(n,i,je(a(),!0))});var l=yc(e,n),f=Tc(e,l,t),s=yc(n,f),d=Tc(n,s,t);return k(d,function(n,e){var t=n.cells().slice(0,r).concat(f[e].cells()).concat(n.cells().slice(r,n.cells().length));return Qi(n,t)})},Dc=function(n,e,t,r,o){pc(e,n,o,r.cell);var u=wc(t,e),i=Tc(t,u,r),c=wc(e,i),a=Tc(e,c,r);return a.slice(0,n).concat(i).concat(a.slice(n,a.length))},Ac=function(t,r,n,o,u){var e=t.slice(0,r),i=t.slice(r),c=Zi(t[n],function(n,e){return 0<r&&r<t.length&&o(ec(t[r-1],e),ec(t[r],e))?nc(t[r],e):je(u(n.element(),o),!0)});return e.concat([c]).concat(i)},Bc=function(n,l,f,s,d){return k(n,function(n){var e,t,r,o,u,i,c,a=0<l&&l<tc(n)&&s(ec(n,l-1),ec(n,l))?nc(n,l):je(d(ec(n,f),s),!0);return t=l,r=a,o=(e=n).cells(),u=o.slice(0,t),i=o.slice(t),c=u.concat([r]).concat(i),Qi(e,c)})},Ic=function(n,t,r,o){return k(n,function(n){return Zi(n,function(n){return e=n,I(t,function(n){return r(e.element(),n.element())})?je(o(n.element(),r),!0):n;var e})})},Pc=function(n,e,t,r){return ec(n[e],t)!==undefined&&0<e&&r(ec(n[e-1],t),ec(n[e],t))},kc=function(n,e,t){return 0<e&&t(ec(n,e-1),ec(n,e))},Ec=function(t,r,o,n){var e=F(t,function(n,e){return Pc(t,e,r,o)||kc(n,r,o)?[]:[nc(n,r)]});return Ic(t,e,o,n)},Mc=function(t,r,o,n){var u=t[r],e=F(u.cells(),function(n,e){return Pc(t,r,e,o)||kc(u,e,o)?[]:[n]});return Ic(t,e,o,n)},Nc=Su(["cell","row","replace","gap"]),_c=function(n){var e=Ye(n,"colspan",1),t=Ye(n,"rowspan",1);return{element:b(n),colspan:b(e),rowspan:b(t)}},Wc=function(r,o){void 0===o&&(o=_c),Nc(r);var t=Ri(S.none()),u=function(n){var e,t=o(n);return e=t,r.cell(e)},i=function(n){var e=u(n);return t.get().isNone()&&t.set(S.some(e)),c=S.some({item:n,replacement:e}),e},c=S.none();return{getOrInit:function(e,t){return c.fold(function(){return i(e)},function(n){return t(e,n.item)?n.replacement:i(e)})},cursor:t.get}},jc=function(c,a){return function(r){var o=Ri(S.none());Nc(r);var u=[],i=function(n){var e={scope:c},t=r.replace(n,a,e);return u.push({item:n,sub:t}),o.get().isNone()&&o.set(S.some(t)),t};return{replaceOrInit:function(e,t){return r=e,o=t,W(u,function(n){return o(n.item,r)}).fold(function(){return i(e)},function(n){return t(e,n.item)?n.sub:i(e)});var r,o},cursor:o.get}}},zc=function(t){Nc(t);var n=Ri(S.none());return{combine:function(e){return n.get().isNone()&&n.set(S.some(e)),function(){var n=t.cell({element:b(e),colspan:b(1),rowspan:b(1)});return Re(n,"width"),Re(e,"width"),n}},cursor:n.get}},Fc=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Lc=Kt(),Hc=function(n){return e=n,t=Lc.property().name(e),B(Fc,t);var e,t},qc=function(n){return e=n,t=Lc.property().name(e),B(["ol","ul"],t);var e,t},Vc=function(n){return e=n,B(["br","img","hr","input"],Lc.property().name(e));var e},Uc=function(n){var e,u=function(n){return"br"===Q(n)},t=function(o){return xt(o).bind(function(t){var r=ce(t).map(function(n){return!!Hc(n)||!!Vc(n)&&"img"!==Q(n)}).getOr(!1);return oe(t).map(function(n){return!0===r||("li"===Q(e=n)||Ue(e,qc).isSome())||u(t)||Hc(n)&&!Zn(o,n)?[]:[Sn.fromTag("br")];var e})}).getOr([])},r=0===(e=F(n,function(n){var e=ae(n);return L(e,function(n){return u(n)||rn(n)&&0===ht(n).trim().length})?[]:e.concat(t(n))})).length?[Sn.fromTag("br")]:e;Ee(n[0]),ke(n[0],r)},Kc=function(n){0===it(n).length&&Me(n)},$c=function(n,e){return{grid:b(n),cursor:b(e)}},Xc=function(n,e,t){return Gc(n,e,t).orThunk(function(){return Gc(n,0,0)})},Gc=function(n,e,t){return S.from(n[e]).bind(function(n){return S.from(n.cells()[t]).bind(function(n){return S.from(n.element())})})},Yc=function(n,e,t){return $c(n,Gc(n,e,t))},Jc=function(n){return _(n,function(n,e){return I(n,function(n){return n.row()===e.row()})?n:n.concat([e])},[]).sort(function(n,e){return n.row()-e.row()})},Qc=function(n){return _(n,function(n,e){return I(n,function(n){return n.column()===e.column()})?n:n.concat([e])},[]).sort(function(n,e){return n.column()-e.column()})},Zc=function(n,e,t){var r,o=(r=t,k(n,function(n){var e=k(it(n),function(n){var e=Ye(n,"rowspan",1),t=Ye(n,"colspan",1);return _e(n,e,t)});return We(n,e,r.section())})),u=mt.generate(o);return ic(u,e,!0)},na=function(n,e){var t=M(n,e);return 0===t.length?S.some("td"):t.length===n.length?S.some("th"):S.none()},ea=function(n,e,t,r){var o=mt.generate(e),u=r.getWidths(o,t,r),i=tu(o,u);E(i,function(n){r.setElementWidth(n.element,n.width)})},ta=fc(function(n,e,t,r){var o=e[0].row(),u=e[0].row(),i=Jc(e),c=_(i,function(n,e){return Ac(n,u,o,t,r.getOrInit)},n);return Yc(c,u,e[0].column())},mc,w,w,Wc),ra=fc(function(n,e,t,r){var o=Jc(e),u=o[o.length-1].row(),i=o[o.length-1].row()+o[o.length-1].rowspan(),c=_(o,function(n,e){return Ac(n,i,u,t,r.getOrInit)},n);return Yc(c,i,e[0].column())},mc,w,w,Wc),oa=fc(function(n,e,t,r){var o=Qc(e),u=o[0].column(),i=o[0].column(),c=_(o,function(n,e){return Bc(n,i,u,t,r.getOrInit)},n);return Yc(c,e[0].row(),i)},mc,ea,w,Wc),ua=fc(function(n,e,t,r){var o=e[e.length-1].column(),u=e[e.length-1].column()+e[e.length-1].colspan(),i=Qc(e),c=_(i,function(n,e){return Bc(n,u,o,t,r.getOrInit)},n);return Yc(c,e[0].row(),u)},mc,ea,w,Wc),ia=fc(function(n,e,t,r){var o,u,i,c,a=Qc(e),l=(o=n,u=a[0].column(),i=a[a.length-1].column(),c=k(o,function(n){var e=n.cells().slice(0,u).concat(n.cells().slice(i+1));return ze(e,n.section())}),M(c,function(n){return 0<n.cells().length})),f=Xc(l,e[0].row(),e[0].column());return $c(l,f)},mc,ea,Kc,Wc),ca=fc(function(n,e,t,r){var o,u,i,c=Jc(e),a=(o=n,u=c[0].row(),i=c[c.length-1].row(),o.slice(0,u).concat(o.slice(i+1))),l=Xc(a,e[0].row(),e[0].column());return $c(a,l)},mc,w,Kc,Wc),aa=fc(function(n,e,t,r){var o=Ec(n,e.column(),t,r.replaceOrInit);return Yc(o,e.row(),e.column())},sc,w,w,jc("row","th")),la=fc(function(n,e,t,r){var o=Ec(n,e.column(),t,r.replaceOrInit);return Yc(o,e.row(),e.column())},sc,w,w,jc(null,"td")),fa=(fc(function(n,e,t,r){var o=Mc(n,e.row(),t,r.replaceOrInit);return Yc(o,e.row(),e.column())},sc,w,w,jc("col","th")),fc(function(n,e,t,r){var o=Mc(n,e.row(),t,r.replaceOrInit);return Yc(o,e.row(),e.column())},sc,w,w,jc(null,"td")),fc(function(n,e,t,r){var o=e.cells();Uc(o);var u=function(n,e,t){if(0===n.length)return n;for(var r=e.startRow();r<=e.finishRow();r++)for(var o=e.startCol();o<=e.finishCol();o++)Ji(n[r],o,je(t(),!1));return n}(n,e.bounds(),b(o[0]));return $c(u,S.from(o[0]))},function(n,e){return e.mergable()},w,w,zc)),sa=fc(function(n,e,t,r){var o=N(e,function(n,e){return gc(n,e,t,r.combine(e))},n);return $c(o,S.from(e[0]))},function(n,e){return e.unmergable()},ea,w,zc),da=fc(function(n,t,e,r){var o,u,i,c,a,l=(o=t.clipboard(),u=t.generators(),i=mt.fromTable(o),ic(i,u,!0)),f=(c=t.row(),a=t.column(),{row:b(c),column:b(a)});return Rc(f,n,l,t.generators(),e).fold(function(){return $c(n,S.some(t.element()))},function(n){var e=Xc(n,t.row(),t.column());return $c(n,e)})},function(e,t){return ut(t.element()).bind(function(n){return lc(e,n).map(function(n){return xn(xn({},n),{generators:t.generators,clipboard:t.clipboard})})})},ea,w,Wc),ma=fc(function(n,e,t,r){var o=n[e.cells[0].row()],u=e.cells[0].column(),i=Zc(e.clipboard(),e.generators(),o),c=Oc(u,n,i,e.generators(),t),a=Xc(c,e.cells[0].row(),e.cells[0].column());return $c(c,a)},dc,w,w,Wc),ga=fc(function(n,e,t,r){var o=n[e.cells[0].row()],u=e.cells[e.cells.length-1].column()+e.cells[e.cells.length-1].colspan(),i=Zc(e.clipboard(),e.generators(),o),c=Oc(u,n,i,e.generators(),t),a=Xc(c,e.cells[0].row(),e.cells[0].column());return $c(c,a)},dc,w,w,Wc),pa=fc(function(n,e,t,r){var o=n[e.cells[0].row()],u=e.cells[0].row(),i=Zc(e.clipboard(),e.generators(),o),c=Dc(u,n,i,e.generators(),t),a=Xc(c,e.cells[0].row(),e.cells[0].column());return $c(c,a)},dc,w,w,Wc),ha=fc(function(n,e,t,r){var o=n[e.cells[0].row()],u=e.cells[e.cells.length-1].row()+e.cells[e.cells.length-1].rowspan(),i=Zc(e.clipboard(),e.generators(),o),c=Dc(u,n,i,e.generators(),t),a=Xc(c,e.cells[0].row(),e.cells[0].column());return $c(c,a)},dc,w,w,Wc),va=function(n,e){var u=mt.fromTable(n);return mc(u,e).bind(function(n){var e=n[n.length-1],t=n[0].column(),r=e.column()+e.colspan(),o=z(k(u.all,function(n){return M(n.cells(),function(n){return n.column()>=t&&n.column()<r})}));return na(o,function(n){return"th"===Q(n.element())})}).getOr("")},ba=function(n){return mi(n.parentNode)},wa=function(n,e){var t="thead"===ba(e),r=!I(e.cells,function(n){return"th"!==mi(n)});return t||r?S.some({thead:t,ths:r}):S.none()},ya=function(n,e){return"thead"===(t=wa(0,e).fold(function(){return ba(e)},function(n){return"thead"}))?"header":"tfoot"===t?"footer":"body";var t},Ca=function(e,n,t){var r=e.getParent(n,"table"),o=n.parentNode,u=mi(o);if(t!==u){var i=e.select(t,r)[0];if(!i){i=e.create(t);var c=r.firstChild;"thead"===t?H(He(Sn.fromDom(r),"caption,colgroup")).fold(function(){return r.insertBefore(i,c)},function(n){return e.insertAfter(i,n.dom())}):r.appendChild(i)}"tbody"===t&&"thead"===u&&i.firstChild?i.insertBefore(n,i.firstChild):i.appendChild(n),o.hasChildNodes()||e.remove(o)}},Sa=function(t,n,r,o){return E(n,function(n){var e=mi(n)!==r?t.rename(n,r):n;t.setAttrib(e,"scope",o)})},xa=function(n,e,t){var r,o=n.dom;if("header"===t){var u=si(n),i="auto"===u?(r=ct(Sn.fromDom(e.cells[0])).map(function(n){return at(n)}).getOr([]),q(r,function(n){return wa(0,n.dom())}).map(function(n){return n.thead&&n.ths?"sectionCells":n.thead?"section":"cells"}).getOr("section")):u;Sa(o,e.cells,"section"===i?"td":"th","col"),Ca(o,e,"cells"===i?"tbody":"thead")}else Sa(o,e.cells,"td",null),Ca(o,e,"footer"===t?"tfoot":"tbody")},Ta=function(e){return{get:function(){var n=gi(e);return er(n,lr).fold(function(){return e.selection.getStart()===undefined?wr():Cr(e.selection)},function(n){return yr(n)})}}},Ra=function(e){return function(n){return S.from(n.dom.getParent(n.selection.getStart(),e)).map(function(n){return Sn.fromDom(n)})}},Oa=Ra("th,td"),Da=Ra("th,td,caption"),Aa=function(e){return Oa(e).map(function(n){return Sr(n,Ta(e))}).map(function(n){return k(n,function(n){return n.dom()})}).getOr([])},Ba=function(t){var n,e,r,o=Oa(t),u=o.bind(function(n){return ct(n)}).map(function(n){return at(n)}).map(function(n){return k(n,function(n){return n.dom()})});return e=u,r=function(e,n){return M(n,function(n){return I(n.cells,function(n){return"1"===t.dom.getAttrib(n,ar)||n===e.dom()})})},((n=o).isSome()&&e.isSome()?S.some(r(n.getOrDie(),e.getOrDie())):S.none()).getOr([])},Ia=function(s,n){var e=function(n){return"table"===Q(gi(n))},d=di(s),t=function(c,a,l,f){return function(n,e){wi(n);var t=f(),r=Sn.fromDom(s.getDoc()),o=Go(Ti),u=Et(l,r,d),i=ki(s,n);return a(n)?c(t,n,e,u,o,i).bind(function(n){return E(n.newRows(),function(n){$u(s,n.dom())}),E(n.newCells(),function(n){Xu(s,n.dom())}),n.cursor().map(function(n){var e=Vi(Ui,n),t=s.dom.createRng();return t.setStart(e.element().dom(),e.offset()),t.setEnd(e.element().dom(),e.offset()),t})}):S.none()}},r=t(ca,function(n){return!1===e(s)||1<$i(n).rows()},w,n),o=t(ia,function(n){return!1===e(s)||1<$i(n).columns()},w,n),u=t(ta,C,w,n),i=t(ra,C,w,n),c=t(oa,C,Ki,n),a=t(ua,C,Ki,n),l=t(fa,C,w,n),f=t(sa,C,w,n),m=t(ma,C,w,n),g=t(ga,C,w,n),p=t(pa,C,w,n),h=t(ha,C,w,n),v=t(da,C,w,n),b=function(n,e){return Y(n,"type").filter(function(n){return B(e,n)})};return{deleteRow:r,deleteColumn:o,insertRowsBefore:u,insertRowsAfter:i,insertColumnsBefore:c,insertColumnsAfter:a,mergeCells:l,unmergeCells:f,pasteColsBefore:m,pasteColsAfter:g,pasteRowsBefore:p,pasteRowsAfter:h,pasteCells:v,setTableCellType:function(e,n){return b(n,["td","th"]).each(function(n){Sa(e.dom,Aa(e),n,null)})},setTableRowType:function(t,n){return b(n,["header","body","footer"]).each(function(e){k(Ba(t),function(n){return xa(t,n,e)})})},makeColumnHeader:t(aa,C,w,n),unmakeColumnHeader:t(la,C,w,n),getTableRowType:function(n){var e=Ba(n);if(0<e.length){var t=k(e,function(n){return ya(0,n)}),r=B(t,"header"),o=B(t,"footer");if(r||o){var u=B(t,"body");return!r||u||o?r||u||!o?"":"footer":"header"}return"body"}},getTableCellType:function(n){return na(Aa(n),function(n){return"th"===mi(n)}).getOr("")},getTableColType:va}},Pa={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"}},ka=function(n,e,t,r){return P(n,function(n){return function(n,e,t,r){for(var o=Sn.fromTag("tr"),u=0;u<n;u++){var i=r<e||u<t?Sn.fromTag("th"):Sn.fromTag("td");u<t&&an(i,"scope","row"),r<e&&an(i,"scope","col"),Be(i,Sn.fromTag("br")),Be(o,i)}return o}(e,t,r,n)})},Ea=function(n,e){n.selection.select(e.dom(),!0),n.selection.collapse(!0)},Ma=function(o,n,e,t,r){var u=ti(o),i={styles:u,attributes:ei(o)},c=function(n,e,t,r,o,u){void 0===u&&(u=Pa);var i=Sn.fromTag("table"),c="cells"!==o;Ce(i,u.styles),ln(i,u.attributes);var a=Math.min(n,t);if(c&&0<t){var l=Sn.fromTag("thead");Be(i,l);var f=ka(t,e,"sectionCells"===o?a:0,r);ke(l,f)}var s=Sn.fromTag("tbody");Be(i,s);var d=ka(c?n-a:n,e,c?0:t,r);return ke(s,d),i}(e,n,r,t,si(o),i);an(c,"data-mce-id","__mce");var a,l,f,s=(a=c,l=Sn.fromTag("div"),f=Sn.fromDom(a.dom().cloneNode(!0)),Be(l,f),l.dom().innerHTML);return o.insertContent(s),Xe(gi(o),'table[data-mce-id="__mce"]').map(function(n){var e,t,r;return li(o)?_i(o,n):fi(o)?Wi(n):(ai(o)||(e=u.width,m(e)&&-1!==e.indexOf("%")))&&Ni(o,n),wi(n),mn(n,"data-mce-id"),t=o,E(qe(n,"tr"),function(n){$u(t,n.dom()),E(qe(n,"th,td"),function(n){Xu(t,n.dom())})}),r=o,Xe(n,"td,th").each(y(Ea,r)),n.dom()}).getOr(null)},Na=function(n,e,t,r,o){void 0===r&&(r={});var u=function(n){return R(n)&&0<n};if(u(e)&&u(t)){var i=r.headerRows||0,c=r.headerColumns||0;return Ma(n,t,e,c,i)}return console.error(o),null},_a=function(n){return function(){return n().fold(function(){return[]},function(n){return k(n,function(n){return n.dom()})})}},Wa=function(t){return function(n){var e=0<n.length?S.some(k(n,Sn.fromDom)):S.none();t(e)}},ja=function(r,n,e,t){return{insertTable:function(n,e,t){return void 0===t&&(t={}),Na(r,e,n,t,"Invalid values for insertTable - rows and columns values are required to insert a table.")},setClipboardRows:Wa(n.setRows),getClipboardRows:_a(n.getRows),setClipboardCols:Wa(n.setColumns),getClipboardCols:_a(n.getColumns),resizeHandler:e,selectionTargets:t}},za=function(n,e){var t=mt.fromTable(n);return mc(t,e).map(function(n){var e=n[n.length-1],i=n[0].column(),c=e.column()+e.colspan();return k(t.all,function(n){var e=M(n.cells(),function(n){return n.column()>=i&&n.column()<c}),t=k(e,function(n){var e,t,r,o,u=Dt(n.element());return r=c-i,o=Je(e=u,t="colspan"),1===r||o<=1?mn(e,t):an(e,t,Math.min(r,o)),u}),r=Sn.fromTag("tr");return ke(r,t),r})})},Fa=function(n,e,r){var o=mt.fromTable(n);return mc(o,e).map(function(n){var e=ic(o,r,!1).slice(n[0].row(),n[n.length-1].row()+n[n.length-1].rowspan()),t=ac(e,r);return k(t,function(n){var t=Ot(n.element());return E(n.cells(),function(n){var e=Dt(n.element());Gi(e,"colspan",n.colspan(),1),Gi(e,"rowspan",n.rowspan(),1),Be(t,e)}),t})})},La=tinymce.util.Tools.resolve("tinymce.util.Tools"),Ha=function(o,n,u){return function(n,e){for(var t=0;t<e.length;t++){var r=o.getStyle(e[t],u);if(void 0===n&&(n=r),n!==r)return""}return n}(void 0,o.select("td,th",n))},qa=function(n,e,t){t&&n.formatter.apply("align"+t,{},e)},Va=function(e,t){La.each("left center right".split(" "),function(n){e.formatter.remove("align"+n,{},t)})},Ua=function(n,e){return(e||[]).concat(k(n,function(n){return{text:n.text||n.title,value:n.value}}))},Ka=function(e){return function(n){return pn(n,"rgb",0)?e.toHex(n):n}},$a=function(n,e){var t=Sn.fromDom(e);return{borderwidth:Te(t,"border-width").getOr(""),borderstyle:Te(t,"border-style").getOr(""),bordercolor:Te(t,"border-color").map(Ka(n)).getOr(""),backgroundcolor:Te(t,"background-color").map(Ka(n)).getOr("")}},Xa=function(n){var o=n[0],e=n.slice(1);return E(e,function(n){E(V(o),function(r){K(n,function(n,e){var t=o[r];""!==t&&r===e&&t!==n&&(o[r]="")})})}),o},Ga=function(n){var e=[{name:"borderstyle",type:"selectbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===n?[{name:"borderwidth",type:"input",label:"Border width"}].concat(e):e}},Ya=function(n,e,t,r){return W(n,function(n){return t.formatter.matchNode(r,e+n)}).getOr("")},Ja=y(Ya,["left","center","right"],"align"),Qa=y(Ya,["top","middle","bottom"],"valign"),Za=function(n,e){var t,r,o,u,i=ti(n),c=ei(n),a=e?(t=n.dom,{borderstyle:Y(i,"border-style").getOr(""),bordercolor:Ka(t)(Y(i,"border-color").getOr("")),backgroundcolor:Ka(t)(Y(i,"background-color").getOr(""))}):{};return xn(xn(xn(xn(xn(xn({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),i),c),a),(u=i["border-width"],ci(n)&&u?{border:u}:Y(c,"border").fold(function(){return{}},function(n){return{border:n}}))),(r=Y(i,"border-spacing").or(Y(c,"cellspacing")).fold(function(){return{}},function(n){return{cellspacing:n}}),o=Y(i,"border-padding").or(Y(c,"cellpadding")).fold(function(){return{}},function(n){return{cellpadding:n}}),xn(xn({},r),o)))},nl=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"selectbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"selectbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"selectbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"selectbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],el=function(n){return(0<(e=Ua(n.getParam("table_cell_class_list",[],"array"))).length?S.some({name:"class",type:"selectbox",label:"Class",items:e}):S.none()).fold(function(){return nl},function(n){return nl.concat(n)});var e},tl=function(u){return function(t,r){var o=t.dom;return{setAttrib:function(n,e){u&&!e||o.setAttrib(r,n,e)},setStyle:function(n,e){u&&!e||o.setStyle(r,n,e)},setFormat:function(n,e){u&&!e||(""===e?t.formatter.remove(n,{value:null},r,!0):t.formatter.apply(n,{value:e},r))}}}},rl={normal:tl(!1),ifTruthy:tl(!0)},ol=function(d,n,m){var g=d.dom,p=1===n.length;E(n,function(n){var e,t,r,o,u,i,c,a,l,f=m.celltype&&mi(n)!==m.celltype?g.rename(n,m.celltype):n,s=p?rl.normal(d,f):rl.ifTruthy(d,f);t=m,(e=s).setAttrib("scope",t.scope),e.setAttrib("class",t["class"]),e.setStyle("width",bi(t.width)),e.setStyle("height",bi(t.height)),oi(d)&&(o=m,(r=s).setFormat("tablecellbackgroundcolor",o.backgroundcolor),r.setFormat("tablecellbordercolor",o.bordercolor),r.setFormat("tablecellborderstyle",o.borderstyle),r.setFormat("tablecellborderwidth",bi(o.borderwidth))),p&&(Va(d,f),u=d,i=f,La.each("top middle bottom".split(" "),function(n){u.formatter.remove("valign"+n,{},i)})),m.halign&&qa(d,f,m.halign),m.valign&&(c=d,a=f,(l=m.valign)&&c.formatter.apply("valign"+l,{},a))})},ul=function(n,e,t){var r=t.getData();t.close(),n.undoManager.transact(function(){ol(n,e,r),n.focus()})},il=function(u){var n=Aa(u);if(0!==n.length){var e=k(n,function(n){return t=n,r=oi(e=u),o=e.dom,xn({width:o.getStyle(t,"width")||o.getAttrib(t,"width"),height:o.getStyle(t,"height")||o.getAttrib(t,"height"),scope:o.getAttrib(t,"scope"),celltype:mi(t),"class":o.getAttrib(t,"class",""),halign:Ja(e,t),valign:Qa(e,t)},r?$a(o,t):{});var e,t,r,o}),t=Xa(e),r={type:"tabpanel",tabs:[{title:"General",name:"general",items:el(u)},Ga("cell")]},o={type:"panel",items:[{type:"grid",columns:2,items:el(u)}]};u.windowManager.open({title:"Cell Properties",size:"normal",body:oi(u)?r:o,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:t,onSubmit:y(ul,u,n)})}},cl=[{type:"selectbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],al=function(n){return(0<(e=Ua(n.getParam("table_row_class_list",[],"array"))).length?S.some({name:"class",type:"selectbox",label:"Class",items:e}):S.none()).fold(function(){return cl},function(n){return cl.concat(n)});var e},ll=function(i,n,c,a){var l=1===n.length;E(n,function(n){a.type!==mi(n.parentNode)&&xa(i,n,a.type);var e,t,r,o,u=l?rl.normal(i,n):rl.ifTruthy(i,n);t=a,(e=u).setAttrib("scope",t.scope),e.setAttrib("class",t["class"]),e.setStyle("height",bi(t.height)),ui(i)&&(o=a,(r=u).setStyle("background-color",o.backgroundcolor),r.setStyle("border-color",o.bordercolor),r.setStyle("border-style",o.borderstyle)),a.align!==c.align&&(Va(i,n),qa(i,n,a.align))})},fl=function(n,e,t,r){var o=r.getData();r.close(),n.undoManager.transact(function(){ll(n,e,t,o),n.focus()})},sl=function(u){var n=Ba(u);if(0!==n.length){var e=k(n,function(n){return t=n,r=ui(e=u),o=e.dom,xn({height:o.getStyle(t,"height")||o.getAttrib(t,"height"),scope:o.getAttrib(t,"scope"),"class":o.getAttrib(t,"class",""),type:ya(0,t),align:Ja(e,t)},r?$a(o,t):{});var e,t,r,o}),t=Xa(e),r={type:"tabpanel",tabs:[{title:"General",name:"general",items:al(u)},Ga("row")]},o={type:"panel",items:[{type:"grid",columns:2,items:al(u)}]};u.windowManager.open({title:"Row Properties",size:"normal",body:ui(u)?r:o,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:t,onSubmit:y(fl,u,n,t)})}},dl=tinymce.util.Tools.resolve("tinymce.Env"),ml=function(n,e,t,r){if("TD"===e.tagName||"TH"===e.tagName)m(t)?n.setStyle(e,t,r):n.setStyle(e,t);else if(e.children)for(var o=0;o<e.children.length;o++)ml(n,e.children[o],t,r)},gl=function(t,r,n){var o,u=t.dom,i=n.getData();n.close(),""===i["class"]&&delete i["class"],t.undoManager.transact(function(){if(!r){var n=parseInt(i.cols,10)||1,e=parseInt(i.rows,10)||1;r=Ma(t,n,e,0,0)}!function(n,e,t){var r,o=n.dom,u={},i={};if(u["class"]=t["class"],i.height=bi(t.height),o.getAttrib(e,"width")&&!ci(n)?u.width=(r=t.width)?r.replace(/px$/,""):"":i.width=bi(t.width),ci(n)?(i["border-width"]=bi(t.border),i["border-spacing"]=bi(t.cellspacing)):(u.border=t.border,u.cellpadding=t.cellpadding,u.cellspacing=t.cellspacing),ci(n)&&e.children)for(var c=0;c<e.children.length;c++)ml(o,e.children[c],{"border-width":bi(t.border),padding:bi(t.cellpadding)}),ii(n)&&ml(o,e.children[c],{"border-color":t.bordercolor});ii(n)&&(i["background-color"]=t.backgroundcolor,i["border-color"]=t.bordercolor,i["border-style"]=t.borderstyle),u.style=o.serializeStyle(xn(xn({},ti(n)),i)),o.setAttribs(e,xn(xn({},ei(n)),u))}(t,r,i),(o=u.select("caption",r)[0])&&!i.caption&&u.remove(o),!o&&i.caption&&((o=u.create("caption")).innerHTML=dl.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===i.align?Va(t,r):qa(t,r,i.align),t.focus(),t.addVisual()})},pl=function(n,e){var t,r,o,u,i,c,a,l,f=n.dom,s=Za(n,ii(n));!1===e?(t=f.getParent(n.selection.getStart(),"table"))?(o=t,u=ii(r=n),l=r.dom,s=xn({width:l.getStyle(o,"width")||l.getAttrib(o,"width"),height:l.getStyle(o,"height")||l.getAttrib(o,"height"),cellspacing:l.getStyle(o,"border-spacing")||l.getAttrib(o,"cellspacing"),cellpadding:l.getAttrib(o,"cellpadding")||Ha(r.dom,o,"padding"),border:(i=l,c=o,a=Te(Sn.fromDom(c),"border-width"),ci(r)&&a.isSome()?a.getOr(""):i.getAttrib(c,"border")||Ha(r.dom,c,"border-width")||Ha(r.dom,c,"border")),caption:!!l.select("caption",o)[0],"class":l.getAttrib(o,"class",""),align:Ja(r,o)},u?$a(l,o):{})):ii(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""):(s.cols="1",s.rows="1",ii(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""));var d=Ua(n.getParam("table_class_list",[],"array"));0<d.length&&s["class"]&&(s["class"]=s["class"].replace(/\s*mce\-item\-table\s*/g,""));var m,g,p,h,v={type:"grid",columns:2,items:(m=d,g=e?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],p=n.getParam("table_appearance_options",!0,"boolean")?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],h=0<m.length?[{type:"selectbox",name:"class",label:"Class",items:m}]:[],g.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(p).concat([{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(h))},b=ii(n)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[v]},Ga("table")]}:{type:"panel",items:[v]};n.windowManager.open({title:"Table Properties",size:"normal",body:b,onSubmit:y(gl,n,t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s})},hl=function(a,t,l,f,e){var r=vi(a),o=function(n){return ct(n,r)},u=function(r){return Oa(a).each(function(t){o(t).each(function(e){var n=Tr(f,e,t);r(e,n).each(function(n){a.selection.setRng(n),a.focus(),l.clear(e),wi(e)})})})},i=function(){return Oa(a).map(function(r){return o(r).bind(function(n){var e=Tr(f,n,r),t=Et(w,Sn.fromDom(a.getDoc()),S.none());return Fa(n,e,t)})})},c=function(){return Oa(a).map(function(t){return o(t).bind(function(n){var e=Tr(f,n,t);return za(n,e)})})},s=function(c,n){return n().each(function(n){var i=k(n,function(n){return Dt(n)});Oa(a).each(function(u){return o(u).each(function(e){var n,t,r=Mt(Sn.fromDom(a.getDoc())),o=(n=i,t=r,{selection:b(Sr(u,f)),clipboard:b(n),generators:b(t)});c(e,o).each(function(n){a.selection.setRng(n),a.focus(),l.clear(e)})})})})};K({mceTableSplitCells:function(){return u(t.unmergeCells)},mceTableMergeCells:function(){return u(t.mergeCells)},mceTableInsertRowBefore:function(){return u(t.insertRowsBefore)},mceTableInsertRowAfter:function(){return u(t.insertRowsAfter)},mceTableInsertColBefore:function(){return u(t.insertColumnsBefore)},mceTableInsertColAfter:function(){return u(t.insertColumnsAfter)},mceTableDeleteCol:function(){return u(t.deleteColumn)},mceTableDeleteRow:function(){return u(t.deleteRow)},mceTableCutCol:function(n){return c().each(function(n){e.setColumns(n),u(t.deleteColumn)})},mceTableCutRow:function(n){return i().each(function(n){e.setRows(n),u(t.deleteRow)})},mceTableCopyCol:function(n){return c().each(function(n){return e.setColumns(n)})},mceTableCopyRow:function(n){return i().each(function(n){return e.setRows(n)})},mceTablePasteColBefore:function(n){return s(t.pasteColsBefore,e.getColumns)},mceTablePasteColAfter:function(n){return s(t.pasteColsAfter,e.getColumns)},mceTablePasteRowBefore:function(n){return s(t.pasteRowsBefore,e.getRows)},mceTablePasteRowAfter:function(n){return s(t.pasteRowsAfter,e.getRows)},mceTableDelete:function(){return Da(a).each(function(n){ct(n,r).filter(d(r)).each(function(n){var e=Sn.fromText("");if(De(n,e),Me(n),a.dom.isEmpty(a.getBody()))a.setContent(""),a.selection.setCursorLocation();else{var t=a.dom.createRng();t.setStart(e.dom(),0),t.setEnd(e.dom(),0),a.selection.setRng(t),a.nodeChanged()}})})},mceTableSizingMode:function(n,e){return t=e,Da(a).each(function(n){fi(a)||li(a)||ai(a)||ct(n,r).each(function(n){"relative"!==t||Vo(n)?"fixed"!==t||Uo(n)?"responsive"!==t||Ko(n)||Wi(n):_i(a,n):Ni(a,n),wi(n)})});var t}},function(n,e){return a.addCommand(e,n)}),K({mceTableCellType:function(n,e){return t.setTableCellType(a,e)},mceTableRowType:function(n,e){return t.setTableRowType(a,e)}},function(n,e){return a.addCommand(e,n)}),a.addCommand("mceTableColType",function(n,e){return Y(e,"type").each(function(n){return u("th"===n?t.makeColumnHeader:t.unmakeColumnHeader)})}),K({mceTableProps:y(pl,a,!1),mceTableRowProps:y(sl,a),mceTableCellProps:y(il,a)},function(n,e){return a.addCommand(e,function(){return n()})}),a.addCommand("mceInsertTable",function(n,e){g(e)&&0<V(e).length?Na(a,e.rows,e.columns,e.options,"Invalid values for mceInsertTable - rows and columns values are required to insert a table."):pl(a,!0)}),a.addCommand("mceTableApplyCellStyle",function(n,e){if(g(e)){var r=Aa(a);0!==r.length&&K(e,function(e,n){var t="tablecell"+n.toLowerCase().replace("-","");a.formatter.has(t)&&m(e)&&E(r,function(n){rl.normal(a,n).setFormat(t,e)})})}})},vl=function(t,r,o){var n=vi(t);K({mceTableRowType:function(){return r.getTableRowType(t)},mceTableCellType:function(){return r.getTableCellType(t)},mceTableColType:function(){return Oa(t).bind(function(t){return ct(t,n).map(function(n){var e=Tr(o,n,t);return r.getTableColType(n,e)})}).getOr("")}},function(n,e){return t.addQueryValueHandler(e,n)})},bl=function(){var e=Ri(S.none()),t=Ri(S.none()),r=function(n){n.set(S.none())};return{getRows:e.get,setRows:function(n){e.set(n),r(t)},clearRows:function(){return r(e)},getColumns:t.get,setColumns:function(n){t.set(n),r(e)},clearColumns:function(){return r(t)}}},wl=hr([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),yl=xn(xn({},wl),{none:function(n){return void 0===n&&(n=undefined),wl.none(n)}}),Cl=function(t,n){return ct(t,n).bind(function(n){var e=it(n);return j(e,function(n){return Zn(t,n)}).map(function(n){return{index:n,all:e}})})},Sl=function(n,e,t,r){return{start:b(n),soffset:b(e),finish:b(t),foffset:b(r)}},xl=hr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Tl={before:xl.before,on:xl.on,after:xl.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(u,u,u)}},Rl=hr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Ol={domRange:Rl.domRange,relative:Rl.relative,exact:Rl.exact,exactFromRange:function(n){return Rl.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var e,t=n.match({domRange:function(n){return Sn.fromDom(n.startContainer)},relative:function(n,e){return Tl.getStart(n)},exact:function(n,e,t,r){return n}});return e=t,Sn.fromDom(e.dom().ownerDocument.defaultView)},range:Sl},Dl=function(n,e){return n.selectNodeContents(e.dom())},Al=function(n,e,t){var r,o,u=n.document.createRange();return r=u,e.fold(function(n){r.setStartBefore(n.dom())},function(n,e){r.setStart(n.dom(),e)},function(n){r.setStartAfter(n.dom())}),o=u,t.fold(function(n){o.setEndBefore(n.dom())},function(n,e){o.setEnd(n.dom(),e)},function(n){o.setEndAfter(n.dom())}),u},Bl=function(n,e,t,r,o){var u=n.document.createRange();return u.setStart(e.dom(),t),u.setEnd(r.dom(),o),u},Il=function(n){return{left:b(n.left),top:b(n.top),right:b(n.right),bottom:b(n.bottom),width:b(n.width),height:b(n.height)}},Pl=hr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),kl=function(n,e,t){return e(Sn.fromDom(t.startContainer),t.startOffset,Sn.fromDom(t.endContainer),t.endOffset)},El=function(n,e){var o,t,r,u=(o=n,e.match({domRange:function(n){return{ltr:b(n),rtl:S.none}},relative:function(n,e){return{ltr:Tn(function(){return Al(o,n,e)}),rtl:Tn(function(){return S.some(Al(o,e,n))})}},exact:function(n,e,t,r){return{ltr:Tn(function(){return Bl(o,n,e,t,r)}),rtl:Tn(function(){return S.some(Bl(o,t,r,n,e))})}}}));return(r=(t=u).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Pl.rtl(Sn.fromDom(n.endContainer),n.endOffset,Sn.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return kl(0,Pl.ltr,r)}):kl(0,Pl.ltr,r)},Ml=function(u,n){return El(u,n).match({ltr:function(n,e,t,r){var o=u.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=u.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}})},Nl=(Pl.ltr,Pl.rtl,function(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom}),_l=function(t,r,n,e,o){var u=function(n){var e=t.dom().createRange();return e.setStart(r.dom(),n),e.collapse(!0),e},i=ht(r).length,c=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var u=r,i=1;i<o;i++){var c=n(i),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||u<a)return i-1;u=a}}return 0}(function(n){return u(n).getBoundingClientRect()},n,e,o.right,i);return u(c)},Wl=function(n,e,t,r){return rn(e)?function(e,t,r,o){var n=e.dom().createRange();n.selectNode(t.dom());var u=n.getClientRects();return q(u,function(n){return Nl(n,r,o)?S.some(n):S.none()}).map(function(n){return _l(e,t,r,o,n)})}(n,e,t,r):(u=e,i=t,c=r,a=(o=n).dom().createRange(),l=ae(u),q(l,function(n){return a.selectNode(n.dom()),Nl(a.getBoundingClientRect(),i,c)?Wl(o,n,i,c):S.none()}));var o,u,i,c,a,l},jl=function(n,e){return e-n.left<n.right-e},zl=function(n,e,t){var r=n.dom().createRange();return r.selectNode(e.dom()),r.collapse(t),r},Fl=function(e,n,t){var r=e.dom().createRange();r.selectNode(n.dom());var o=r.getBoundingClientRect(),u=jl(o,t);return(!0===u?St:xt)(n).map(function(n){return zl(e,n,u)})},Ll=function(n,e,t){var r=e.dom().getBoundingClientRect(),o=jl(r,t);return S.some(zl(n,e,o))},Hl=function(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var u=o.getBoundingClientRect();return function(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var u=o.getBoundingClientRect(),i=Math.max(u.left,Math.min(u.right,t)),c=Math.max(u.top,Math.min(u.bottom,r));return Wl(n,e,i,c)}(n,e,Math.max(u.left,Math.min(u.right,t)),Math.max(u.top,Math.min(u.bottom,r)))},ql=document.caretPositionFromPoint?function(t,n,e){return S.from(t.dom().caretPositionFromPoint(n,e)).bind(function(n){if(null===n.offsetNode)return S.none();var e=t.dom().createRange();return e.setStart(n.offsetNode,n.offset),e.collapse(),S.some(e)})}:document.caretRangeFromPoint?function(n,e,t){return S.from(n.dom().caretRangeFromPoint(e,t))}:function(o,u,e){return Sn.fromPoint(o,u,e).bind(function(r){var n=function(){return n=o,t=u,(0===ae(e=r).length?Ll:Fl)(n,e,t);var n,e,t};return 0===ae(r).length?n():Hl(o,r,u,e).orThunk(n)})},Vl=function(n,e){var t=Q(n);return"input"===t?Tl.after(n):B(["br","img"],t)?0===e?Tl.before(n):Tl.after(n):Tl.on(n,e)},Ul=function(n,e){var t=n.fold(Tl.before,Vl,Tl.after),r=e.fold(Tl.before,Vl,Tl.after);return Ol.relative(t,r)},Kl=function(n,e,t,r){var o=Vl(n,e),u=Vl(t,r);return Ol.relative(o,u)},$l=function(n,e,t,r){var o,u,i,c,a,l=(u=e,i=t,c=r,(a=re(o=n).dom().createRange()).setStart(o.dom(),u),a.setEnd(i.dom(),c),a),f=Zn(n,t)&&e===r;return l.collapsed&&!f},Xl=function(n,e){S.from(n.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(e)})},Gl=function(n,e,t,r,o){var u=Bl(n,e,t,r,o);Xl(n,u)},Yl=function(s,n){return El(s,n).match({ltr:function(n,e,t,r){Gl(s,n,e,t,r)},rtl:function(n,e,t,r){var o,u,i,c,a,l=s.getSelection();if(l.setBaseAndExtent)l.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(l.extend)try{u=n,i=e,c=t,a=r,(o=l).collapse(u.dom(),i),o.extend(c.dom(),a)}catch(f){Gl(s,t,r,n,e)}else Gl(s,t,r,n,e)}})},Jl=function(n,e,t,r,o){var u=Kl(e,t,r,o);Yl(n,u)},Ql=function(n,e,t){var r=Ul(e,t);Yl(n,r)},Zl=function(n){var o=Ol.getWin(n).dom(),e=function(n,e,t,r){return Bl(o,n,e,t,r)},t=n.match({domRange:function(n){var e=Sn.fromDom(n.startContainer),t=Sn.fromDom(n.endContainer);return Kl(e,n.startOffset,t,n.endOffset)},relative:Ul,exact:Kl});return El(o,t).match({ltr:e,rtl:e})},nf=function(n){var e=Sn.fromDom(n.anchorNode),t=Sn.fromDom(n.focusNode);return $l(e,n.anchorOffset,t,n.focusOffset)?S.some(Sl(e,n.anchorOffset,t,n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return S.some(Sl(Sn.fromDom(e.startContainer),e.startOffset,Sn.fromDom(t.endContainer),t.endOffset))}return S.none()}(n)},ef=function(n,e){var t,r,o=(t=e,r=n.document.createRange(),Dl(r,t),r);Xl(n,o)},tf=function(n){return e=n,S.from(e.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(nf).map(function(n){return Ol.exact(n.start(),n.soffset(),n.finish(),n.foffset())});var e},rf=function(n,e){var t,r,o,u=Ml(n,e);return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?S.some(o).map(Il):S.none()},of=function(n,e,t){return r=n,o=e,u=t,i=Sn.fromDom(r.document),ql(i,o,u).map(function(n){return Sl(Sn.fromDom(n.startContainer),n.startOffset,Sn.fromDom(n.endContainer),n.endOffset)});var r,o,u,i},uf=tinymce.util.Tools.resolve("tinymce.util.VK"),cf=function(n,e,t,r){return ff(n,e,Cl(o=t,u).fold(function(){return yl.none(o)},function(n){return n.index+1<n.all.length?yl.middle(o,n.all[n.index+1]):yl.last(o)}),r);var o,u},af=function(n,e,t,r){return ff(n,e,Cl(o=t,u).fold(function(){return yl.none()},function(n){return 0<=n.index-1?yl.middle(o,n.all[n.index-1]):yl.first(o)}),r);var o,u},lf=function(n,e){var t=Ol.exact(e,0,e,0);return Zl(t)},ff=function(o,n,e,u,t){return e.fold(S.none,S.none,function(n,e){return St(e).map(function(n){return lf(0,n)})},function(r){return ct(r,n).bind(function(n){var e,t=xr(r);return o.undoManager.transact(function(){u.insertRowsAfter(n,t)}),e=qe(n,"tr"),H(e).bind(function(n){return Xe(n,"td,th").map(function(n){return lf(0,n)})})})})},sf=["table","li","dl"],df=function(e,t,r,o){if(e.keyCode===uf.TAB){var u=gi(t),i=function(n){var e=Q(n);return Zn(n,u)||B(sf,e)},n=t.selection.getRng();if(n.collapsed){var c=Sn.fromDom(n.startContainer);ut(c,i).each(function(n){e.preventDefault(),(e.shiftKey?af:cf)(t,i,n,r,o).each(function(n){t.selection.setRng(n)})})}}},mf=function(n,e){return{selection:b(n),kill:b(e)}},gf=function(n,e,t,r){return{start:b(Tl.on(n,e)),finish:b(Tl.on(t,r))}},pf=function(n,e){var t=Ml(n,e);return Sl(Sn.fromDom(t.startContainer),t.startOffset,Sn.fromDom(t.endContainer),t.endOffset)},hf=gf,vf=function(t,n,r,e,o){return Zn(r,e)?S.none():nr(r,e,n).bind(function(n){var e=n.boxes.getOr([]);return 0<e.length?(o(t,e,n.start,n.finish),S.some(mf(S.some(hf(r,0,r,wt(r))),!0))):S.none()})},bf=function(n,e){return{item:b(n),mode:b(e)}},wf=function(n,e,t,r){return void 0===r&&(r=yf),n.property().parent(e).map(function(n){return bf(n,r)})},yf=function(n,e,t,r){return void 0===r&&(r=Cf),t.sibling(n,e).map(function(n){return bf(n,r)})},Cf=function(n,e,t,r){void 0===r&&(r=Cf);var o=n.property().children(e);return t.first(o).map(function(n){return bf(n,r)})},Sf=[{current:wf,next:yf,fallback:S.none()},{current:yf,next:Cf,fallback:S.some(wf)},{current:Cf,next:Cf,fallback:S.some(yf)}],xf=function(e,t,r,o,n){return void 0===n&&(n=Sf),W(n,function(n){return n.current===r}).bind(function(n){return n.current(e,t,o,n.next).orThunk(function(){return n.fallback.bind(function(n){return xf(e,t,n,o)})})})},Tf=function(){return{sibling:function(n,e){return n.query().prevSibling(e)},first:function(n){return 0<n.length?S.some(n[n.length-1]):S.none()}}},Rf=function(){return{sibling:function(n,e){return n.query().nextSibling(e)},first:function(n){return 0<n.length?S.some(n[0]):S.none()}}},Of=function(e,n,t,r,o,u){return xf(e,n,r,o).bind(function(n){return u(n.item())?S.none():t(n.item())?S.some(n.item()):Of(e,n.item(),t,n.mode(),o,u)})},Df=function(e){return function(n){return 0===e.property().children(n).length}},Af=function(n,e,t,r){return Of(n,e,t,yf,Tf(),r)},Bf=function(n,e,t,r){return Of(n,e,t,yf,Rf(),r)},If=Kt(),Pf=function(n,e){return Af(t=If,n,Df(t),e);var t},kf=function(n,e){return Bf(t=If,n,Df(t),e);var t},Ef=hr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),Mf=function(n){return Ge(n,"tr")},Nf=xn(xn({},Ef),{verify:function(c,n,e,t,r,a,o){return Ge(t,"td,th",o).bind(function(i){return Ge(n,"td,th",o).map(function(u){return Zn(i,u)?Zn(t,i)&&wt(i)===r?a(u):Ef.none("in same cell"):Qt(Mf,[i,u]).fold(function(){return e=u,t=i,r=(n=c).getRect(e),(o=n.getRect(t)).right>r.left&&o.left<r.right?Ef.success():a(u);var n,e,t,r,o},function(n){return a(u)})})}).getOr(Ef.none("default"))},cata:function(n,e,t,r,o){return n.fold(e,t,r,o)}}),_f=function(r){return oe(r).bind(function(e){var t=ae(e);return Wf(t,r).map(function(n){return{parent:b(e),children:b(t),element:b(r),index:b(n)}})})},Wf=function(n,e){return j(n,y(Zn,e))},jf=function(n){return"br"===Q(n)},zf=function(n,e,t){return e(n,t).bind(function(n){return rn(n)&&0===ht(n).trim().length?zf(n,e,t):S.some(n)})},Ff=function(e,n,t,r){return le(o=n,u=t).filter(jf).orThunk(function(){return le(o,u-1).filter(jf)}).bind(function(n){return r.traverse(n).fold(function(){return zf(n,r.gather,e).map(r.relative)},function(n){return _f(n).map(function(n){return Tl.on(n.parent(),n.index())})})});var o,u},Lf=function(n,e,t,r){var o,u,i;return(jf(e)?(o=n,u=e,(i=r).traverse(u).orThunk(function(){return zf(u,i.gather,o)}).map(i.relative)):Ff(n,e,t,r)).map(function(n){return{start:b(n),finish:b(n)}})},Hf=function(n,e){return{left:n.left,top:n.top+e,right:n.right,bottom:n.bottom+e}},qf=function(n,e){return{left:n.left,top:n.top-e,right:n.right,bottom:n.bottom-e}},Vf=function(n,e,t){return{left:n.left+e,top:n.top+t,right:n.right+e,bottom:n.bottom+t}},Uf=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom}},Kf=function(n,e){return S.some(n.getRect(e))},$f=function(n,e,t){return tn(e)?Kf(n,e).map(Uf):rn(e)?(r=n,o=e,(0<=(u=t)&&u<wt(o)?r.getRangedRect(o,u,o,u+1):0<u?r.getRangedRect(o,u-1,o,u):S.none()).map(Uf)):S.none();var r,o,u},Xf=function(n,e){return tn(e)?Kf(n,e).map(Uf):rn(e)?n.getRangedRect(e,0,e,wt(e)).map(Uf):S.none()},Gf=hr([{none:[]},{retry:["caret"]}]),Yf=function(e,n,r){return Ve(function(n,e){return e(n)},Ue,n,Hc,t).fold(b(!1),function(n){return Xf(e,n).exists(function(n){return t=n,(e=r).left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right;var e,t})});var t},Jf={point:function(n){return n.bottom},adjuster:function(n,e,t,r,o){var u=Hf(o,5);return Math.abs(t.bottom-r.bottom)<1||t.top>o.bottom?Gf.retry(u):t.top===o.bottom?Gf.retry(Hf(o,1)):Yf(n,e,o)?Gf.retry(Vf(u,5,0)):Gf.none()},move:Hf,gather:kf},Qf=function(t,r,o,u,i){return 0===i?S.some(u):(a=t,l=u.left,f=r.point(u),a.elementFromPoint(l,f).filter(function(n){return"table"===Q(n)}).isSome()?(e=u,c=i-1,Qf(t,n=r,o,n.move(e,5),c)):t.situsFromPoint(u.left,r.point(u)).bind(function(n){return n.start().fold(S.none,function(e){return Xf(t,e).bind(function(n){return r.adjuster(t,e,n,o,u).fold(S.none,function(n){return Qf(t,r,o,n,i-1)})}).orThunk(function(){return S.some(u)})},S.none)}));var n,e,c,a,l,f},Zf=function(e,t,n){var r,o,u,i=e.move(n,5),c=Qf(t,e,n,i,100).getOr(i);return o=c,u=t,((r=e).point(o)>u.getInnerHeight()?S.some(r.point(o)-u.getInnerHeight()):r.point(o)<0?S.some(-r.point(o)):S.none()).fold(function(){return t.situsFromPoint(c.left,e.point(c))},function(n){return t.scrollBy(0,n),t.situsFromPoint(c.left,e.point(c)-n)})},ns={tryUp:y(Zf,{point:function(n){return n.top},adjuster:function(n,e,t,r,o){var u=qf(o,5);return Math.abs(t.top-r.top)<1||t.bottom<o.top?Gf.retry(u):t.bottom===o.top?Gf.retry(qf(o,1)):Yf(n,e,o)?Gf.retry(Vf(u,5,0)):Gf.none()},move:qf,gather:Pf}),tryDown:y(Zf,Jf),ieTryUp:function(n,e){return n.situsFromPoint(e.left,e.top-5)},ieTryDown:function(n,e){return n.situsFromPoint(e.left,e.bottom+5)},getJumpSize:b(5)},es=function(u,i,c){return u.getSelection().bind(function(o){return Lf(i,o.finish(),o.foffset(),c).fold(function(){return S.some(Fi(o.finish(),o.foffset()))},function(n){var e,t=u.fromSitus(n),r=Nf.verify(u,o.finish(),o.foffset(),t.finish(),t.foffset(),c.failure,i);return e=r,Nf.cata(e,function(n){return S.none()},function(){return S.none()},function(n){return S.some(Fi(n,0))},function(n){return S.some(Fi(n,wt(n)))})})})},ts=function(r,o,u,i,c,a){return 0===a?S.none():us(r,o,u,i,c).bind(function(n){var e=r.fromSitus(n),t=Nf.verify(r,u,i,e.finish(),e.foffset(),c.failure,o);return Nf.cata(t,function(){return S.none()},function(){return S.some(n)},function(n){return Zn(u,n)&&0===i?rs(r,u,i,qf,c):ts(r,o,n,0,c,a-1)},function(n){return Zn(u,n)&&i===wt(n)?rs(r,u,i,Hf,c):ts(r,o,n,wt(n),c,a-1)})})},rs=function(e,n,t,r,o){return $f(e,n,t).bind(function(n){return os(e,o,r(n,ns.getJumpSize()))})},os=function(n,e,t){var r=Yn().browser;return r.isChrome()||r.isSafari()||r.isFirefox()||r.isEdge()?e.otherRetry(n,t):r.isIE()?e.ieRetry(n,t):S.none()},us=function(e,n,t,r,o){return $f(e,t,r).bind(function(n){return os(e,o,n)})},is=function(n,e){return Ue(n,function(n){return oe(n).exists(function(n){return Zn(n,e)})},t).isSome();var t},cs=function(u,i,c,n,a){return Ge(n,"td,th",i).bind(function(o){return Ge(o,"table",i).bind(function(n){return is(a,n)?es(e=u,t=i,r=c).bind(function(n){return ts(e,t,n.element(),n.offset(),r,20).map(e.fromSitus)}).bind(function(e){return Ge(e.finish(),"td,th",i).map(function(n){return{start:b(o),finish:b(n),range:b(e)}})}):S.none();var e,t,r})})},as=function(n,e,t,r,o,u){return Yn().browser.isIE()?S.none():u(r,e).orThunk(function(){return cs(n,e,t,r,o).map(function(n){var e=n.range();return mf(S.some(hf(e.start(),e.soffset(),e.finish(),e.foffset())),!0)})})},ls=function(n,r){return Ge(n,"tr",r).bind(function(t){return Ge(t,"table",r).bind(function(n){var e=qe(n,"tr");return Zn(t,e[0])?Af(If,n,function(n){return xt(n).isSome()},r).map(function(n){var e=wt(n);return mf(S.some(hf(n,e,n,e)),!0)}):S.none()})})},fs=function(n,r){return Ge(n,"tr",r).bind(function(t){return Ge(t,"table",r).bind(function(n){var e=qe(n,"tr");return Zn(t,e[e.length-1])?Bf(If,n,function(n){return St(n).isSome()},r).map(function(n){return mf(S.some(hf(n,0,n,0)),!0)}):S.none()})})},ss=function(n,e,t,r,o,u,i){return cs(n,t,r,o,u).bind(function(n){return vf(e,t,n.start(),n.finish(),i)})},ds=function(n,e){return Ge(n,"td,th",e)};var ms={traverse:ce,gather:kf,relative:Tl.before,otherRetry:ns.tryDown,ieRetry:ns.ieTryDown,failure:Nf.failedDown},gs={traverse:ie,gather:Pf,relative:Tl.before,otherRetry:ns.tryUp,ieRetry:ns.ieTryUp,failure:Nf.failedUp},ps=function(e){return function(n){return n===e}},hs=ps(38),vs=ps(40),bs=function(n){return 37<=n&&n<=40},ws={isBackward:ps(37),isForward:ps(39)},ys={isBackward:ps(39),isForward:ps(37)},Cs=function(n){return{left:n.left(),top:n.top(),right:n.right(),bottom:n.bottom(),width:n.width(),height:n.height()}},Ss=function(c){return{elementFromPoint:function(n,e){return Sn.fromPoint(Sn.fromDom(c.document),n,e)},getRect:function(n){return n.dom().getBoundingClientRect()},getRangedRect:function(n,e,t,r){var o=Ol.exact(n,e,t,r);return rf(c,o).map(Cs)},getSelection:function(){return tf(c).map(function(n){return pf(c,n)})},fromSitus:function(n){var e=Ol.relative(n.start(),n.finish());return pf(c,e)},situsFromPoint:function(n,e){return of(c,n,e).map(function(n){return gf(n.start(),n.soffset(),n.finish(),n.foffset())})},clearSelection:function(){c.getSelection().removeAllRanges()},collapseSelection:function(i){void 0===i&&(i=!1),tf(c).each(function(n){return n.fold(function(n){return n.collapse(i)},function(n,e){var t=i?n:e;Ql(c,t,t)},function(n,e,t,r){var o=i?n:t,u=i?e:r;Jl(c,o,u,o,u)})})},setSelection:function(n){Jl(c,n.start(),n.soffset(),n.finish(),n.foffset())},setRelativeSelection:function(n,e){Ql(c,n,e)},selectContents:function(n){ef(c,n)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){var n,e,t,r;return(n=Sn.fromDom(c.document),e=n!==undefined?n.dom():h.document,t=e.body.scrollLeft||e.documentElement.scrollLeft,r=e.body.scrollTop||e.documentElement.scrollTop,Dr(t,r)).top()},scrollBy:function(n,e){var t,r,o;t=n,r=e,((o=Sn.fromDom(c.document))!==undefined?o.dom():h.document).defaultView.scrollBy(t,r)}}},xs=function(n,e){return{rows:n,cols:e}},Ts=function(n,e,t,r){var o=function c(o,u,e,i){var t=S.none(),r=function(){t=S.none()};return{mousedown:function(n){i.clear(u),t=ds(n.target(),e)},mouseover:function(n){t.each(function(r){i.clearBeforeUpdate(u),ds(n.target(),e).each(function(t){nr(r,t,e).each(function(n){var e=n.boxes.getOr([]);(1<e.length||1===e.length&&!Zn(r,t))&&(i.selectRange(u,e,n.start,n.finish),o.selectContents(t))})})})},mouseup:function(n){t.each(r)}}}(Ss(n),e,t,r);return{mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},Rs=function(n,g,p,h){var l=Ss(n),f=function(){return h.clear(g),S.none()};return{keydown:function(n,e,t,r,o,i){var u=n.raw(),c=u.which,a=!0===u.shiftKey;return er(g,h.selectedSelector).fold(function(){return vs(c)&&a?y(ss,l,g,p,ms,r,e,h.selectRange):hs(c)&&a?y(ss,l,g,p,gs,r,e,h.selectRange):vs(c)?y(as,l,p,ms,r,e,fs):hs(c)?y(as,l,p,gs,r,e,ls):S.none},function(u){var n=function(n){return function(){return q(n,function(n){return e=n.rows,t=n.cols,r=g,rr(u,e,t,(o=h).firstSelectedSelector,o.lastSelectedSelector).map(function(n){return o.clearBeforeUpdate(r),o.selectRange(r,n.boxes,n.start,n.finish),n.boxes});var e,t,r,o}).fold(function(){return tr(g,h.firstSelectedSelector,h.lastSelectedSelector).map(function(n){var e=vs(c)||i.isForward(c)?Tl.after:Tl.before;return l.setRelativeSelection(Tl.on(n.first(),0),e(n.table())),h.clear(g),mf(S.none(),!0)})},function(n){return S.some(mf(S.none(),!0))})}};return vs(c)&&a?n([xs(1,0)]):hs(c)&&a?n([xs(-1,0)]):i.isBackward(c)&&a?n([xs(0,-1),xs(-1,0)]):i.isForward(c)&&a?n([xs(0,1),xs(1,0)]):bs(c)&&!1==a?f:S.none})()},keyup:function(l,f,s,d,m){return er(g,h.selectedSelector).fold(function(){var t,r,n,e,o,u,i,c=l.raw(),a=c.which;return!1!=(!0===c.shiftKey)&&bs(a)?(t=g,r=p,n=f,e=s,o=d,u=m,i=h.selectRange,Zn(n,o)&&e===u?S.none():Ge(n,"td,th",r).bind(function(e){return Ge(o,"td,th",r).bind(function(n){return vf(t,r,e,n,i)})})):S.none()},S.none)}}},Os=function(n,r,e,o){var u=Ss(n);return function(n,t){o.clearBeforeUpdate(r),nr(n,t,e).each(function(n){var e=n.boxes.getOr([]);o.selectRange(r,e,n.start,n.finish),u.selectContents(t),u.collapseSelection()})}},Ds=function(e,n){E(n,function(n){!function(n,e){su(n)?n.dom().classList.remove(e):mu(n,e);pu(n)}(e,n)})},As={byClass:function(o){var e,t,u=(e=o.selected,function(n){gu(n,e)}),r=(t=[o.selected,o.lastSelected,o.firstSelected],function(n){Ds(n,t)}),i=function(n){var e=qe(n,o.selectedSelector);E(e,r)};return{clearBeforeUpdate:i,clear:i,selectRange:function(n,e,t,r){i(n),E(e,u),gu(t,o.firstSelected),gu(r,o.lastSelected)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,u,e){var t=function(n){mn(n,o.selected),mn(n,o.firstSelected),mn(n,o.lastSelected)},i=function(n){an(n,o.selected,"1")},c=function(n){r(n),e()},r=function(n){var e=qe(n,o.selectedSelector);E(e,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(n,e,t,r){c(n),E(e,i),an(t,o.firstSelected,"1"),an(r,o.lastSelected,"1"),u(e,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},Bs=function(n,e,s){var d=mt.fromTable(n);return mc(d,e).map(function(n){var t,e,r,o,u,i,c,a,l,f=ic(d,s,!1);return{upOrLeftCells:(t=n,e=s,r=f.slice(0,t[t.length-1].row()+1),o=ac(r,e),F(o,function(n){var e=n.cells().slice(0,t[t.length-1].column()+1);return k(e,function(n){return n.element()})})),downOrRightCells:(i=n,c=s,a=(u=f).slice(i[0].row()+i[0].rowspan()-1,u.length),l=ac(a,c),F(l,function(n){var e=n.cells().slice(i[0].column()+i[0].colspan()-1,+n.cells().length);return k(e,function(n){return n.element()})}))}})},Is=function(n){return!1===hu(Sn.fromDom(n.target),"ephox-snooker-resizer-bar")};function Ps(d,m,n){var g=As.byAttr(pr,function(u,i,c){n.targets().each(function(o){ct(i).each(function(n){var e=di(d),t=Et(w,Sn.fromDom(d.getDoc()),e),r=Bs(n,o,t);Gu(d,u,i,c,r)})})},function(){return Yu(d)});return d.on("init",function(n){var r=d.getWin(),o=gi(d),e=vi(d),t=Ts(r,o,e,g),c=Rs(r,o,e,g),u=Os(r,o,e,g);d.on("TableSelectorChange",function(n){return u(n.start,n.finish)});var i,a,l=function(n,e){!0===n.raw().shiftKey&&(e.kill()&&n.kill(),e.selection().each(function(n){var e=Ol.relative(n.start(),n.finish()),t=Ml(r,e);d.selection.setRng(t)}))},f=function(n){return 0===n.button},s=(i=Ri(Sn.fromDom(o)),a=Ri(0),{touchEnd:function(n){var e=Sn.fromDom(n.target);if("td"===Q(e)||"th"===Q(e)){var t=i.get(),r=a.get();Zn(t,e)&&n.timeStamp-r<300&&(n.preventDefault(),u(e,e))}i.set(e),a.set(n.timeStamp)}});d.on("mousedown",function(n){f(n)&&Is(n)&&t.mousedown(lu(n))}),d.on("mouseover",function(n){var e;((e=n).buttons===undefined||dl.browser.isEdge()&&0===e.buttons||0!=(1&e.buttons))&&Is(n)&&t.mouseover(lu(n))}),d.on("mouseup",function(n){f(n)&&Is(n)&&t.mouseup(lu(n))}),d.on("touchend",s.touchEnd),d.on("keyup",function(n){var e=lu(n);if(e.raw().shiftKey&&bs(e.raw().which)){var t=d.selection.getRng(),r=Sn.fromDom(t.startContainer),o=Sn.fromDom(t.endContainer);c.keyup(e,r,t.startOffset,o,t.endOffset).each(function(n){l(e,n)})}}),d.on("keydown",function(n){var e=lu(n);m().each(function(n){return n.hideBars()});var t=d.selection.getRng(),r=Sn.fromDom(d.selection.getStart()),o=Sn.fromDom(t.startContainer),u=Sn.fromDom(t.endContainer),i=Ti(r).isRtl()?ys:ws;c.keydown(e,o,t.startOffset,u,t.endOffset,i).each(function(n){l(e,n)}),m().each(function(n){return n.showBars()})}),d.on("NodeChange",function(){var n=d.selection,e=Sn.fromDom(n.getStart()),t=Sn.fromDom(n.getEnd());Qt(ct,[e,t]).fold(function(){return g.clear(o)},w)})}),{clear:g.clear}}var ks=function(n,t){var o=Ri(S.none()),u=Ri([]),e=function(){return Da(n).bind(function(e){var n=ct(e);return n.map(function(n){return"caption"===Q(e)?xr(e):Tr(t,n,e)})})},r=function(){o.set(Tn(e)()),E(u.get(),function(n){return n()})},i=function(e,t){var r=function(){return o.get().fold(function(){e.setDisabled(!0)},function(n){e.setDisabled(t(n))})};return r(),u.set(u.get().concat([r])),function(){u.set(M(u.get(),function(n){return n!==r}))}};return n.on("NodeChange ExecCommand TableSelectorChange",r),{onSetupTable:function(n){return i(n,function(n){return!1})},onSetupCellOrRow:function(n){return i(n,function(n){return"caption"===Q(n.element())})},onSetupPasteable:function(e){return function(n){return i(n,function(n){return"caption"===Q(n.element())||e().isNone()})}},onSetupMergeable:function(n){return i(n,function(n){return n.mergable().isNone()})},onSetupUnmergeable:function(n){return i(n,function(n){return n.unmergable().isNone()})},resetTargets:r,targets:function(){return o.get()}}},Es=function(e,n,t){e.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(n){return n("inserttable | cell row column | advtablesort | tableprops deletetable")}});var r=function(n){return function(){return e.execCommand(n)}};e.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:r("mceTableProps"),icon:"table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:r("mceTableDelete"),icon:"table-delete-table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:r("mceTableCellProps"),icon:"table-cell-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:r("mceTableMergeCells"),icon:"table-merge-cells",onSetup:n.onSetupMergeable}),e.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:r("mceTableSplitCells"),icon:"table-split-cells",onSetup:n.onSetupUnmergeable}),e.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:r("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:r("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:r("mceTableDeleteRow"),icon:"table-delete-row",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:r("mceTableRowProps"),icon:"table-row-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:r("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:r("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:r("mceTableDeleteCol"),icon:"table-delete-column",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",icon:"cut-row",onAction:r("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",icon:"duplicate-row",onAction:r("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",icon:"paste-row-before",onAction:r("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",icon:"paste-row-after",onAction:r("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablecutcol",{tooltip:"Cut column",icon:"cut-column",onAction:r("mceTableCutCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecopycol",{tooltip:"Copy column",icon:"duplicate-column",onAction:r("mceTableCopyCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablepastecolbefore",{tooltip:"Paste column before",icon:"paste-column-before",onAction:r("mceTablePasteColBefore"),onSetup:n.onSetupPasteable(t.getColumns)}),e.ui.registry.addButton("tablepastecolafter",{tooltip:"Paste column after",icon:"paste-column-after",onAction:r("mceTablePasteColAfter"),onSetup:n.onSetupPasteable(t.getColumns)}),e.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:r("mceInsertTable"),icon:"table"})},Ms=function(e){var n=e.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol");0<n.length&&e.ui.registry.addContextToolbar("table",{predicate:function(n){return e.dom.is(n,"table")&&e.getBody().contains(n)},items:n,scope:"node",position:"node"})},Ns=function(r,n,e){var t=function(n){return function(){return r.execCommand(n)}},o=function(n){var e=n.numRows,t=n.numColumns;r.undoManager.transact(function(){Ma(r,t,e,0,0)}),r.addVisual()},u={text:"Table properties",onSetup:n.onSetupTable,onAction:t("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:n.onSetupTable,onAction:t("mceTableDelete")};r.ui.registry.addMenuItem("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",onAction:t("mceTableInsertRowBefore"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",onAction:t("mceTableInsertRowAfter"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tabledeleterow",{text:"Delete row",icon:"table-delete-row",onAction:t("mceTableDeleteRow"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablerowprops",{text:"Row properties",icon:"table-row-properties",onAction:t("mceTableRowProps"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecutrow",{text:"Cut row",icon:"cut-row",onAction:t("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecopyrow",{text:"Copy row",icon:"duplicate-row",onAction:t("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",onAction:t("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(e.getRows)}),r.ui.registry.addMenuItem("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",onAction:t("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(e.getRows)});r.ui.registry.addMenuItem("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",onAction:t("mceTableInsertColBefore"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",onAction:t("mceTableInsertColAfter"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",onAction:t("mceTableDeleteCol"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecutcolumn",{text:"Cut column",icon:"cut-column",onAction:t("mceTableCutCol"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",onAction:t("mceTableCopyCol"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",onAction:t("mceTablePasteColBefore"),onSetup:n.onSetupPasteable(e.getColumns)}),r.ui.registry.addMenuItem("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",onAction:t("mceTablePasteColAfter"),onSetup:n.onSetupPasteable(e.getColumns)});r.ui.registry.addMenuItem("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",onAction:t("mceTableCellProps"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",onAction:t("mceTableMergeCells"),onSetup:n.onSetupMergeable}),r.ui.registry.addMenuItem("tablesplitcells",{text:"Split cell",icon:"table-split-cells",onAction:t("mceTableSplitCells"),onSetup:n.onSetupUnmergeable});!1===r.getParam("table_grid",!0,"boolean")?r.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:t("mceInsertTable")}):r.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:o}]}}),r.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:t("mceInsertTable")}),r.ui.registry.addMenuItem("tableprops",u),r.ui.registry.addMenuItem("deletetable",i),r.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return"tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter"}}),r.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return"tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn"}}),r.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return"tablecellprops tablemergecells tablesplitcells"}}),r.ui.registry.addContextMenu("table",{update:function(){return n.resetTargets(),n.targets().fold(function(){return""},function(n){return"caption"===Q(n.element())?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})},_s={tablecellbackgroundcolor:{selector:"td,th",styles:{backgroundColor:"%value"},remove_similar:!0},tablecellbordercolor:{selector:"td,th",styles:{borderColor:"%value"},remove_similar:!0},tablecellborderstyle:{selector:"td,th",styles:{borderStyle:"%value"},remove_similar:!0},tablecellborderwidth:{selector:"td,th",styles:{borderWidth:"%value"},remove_similar:!0}},Ws=function(n){n.formatter.register(_s)};function js(e){var n=Ta(e),t=ks(e,n),r=zi(e),o=Ps(e,r.lazyResize,t),u=Ia(e,r.lazyWire),i=bl();return hl(e,u,o,n,i),vl(e,u,n),Rr(e,n,u,o),Ns(e,t,i),Es(e,t,i),Ms(e),e.on("PreInit",function(){e.serializer.addTempAttr(sr),e.serializer.addTempAttr(mr),Ws(e)}),ri(e)&&e.on("keydown",function(n){df(n,e,u,r.lazyWire)}),e.on("remove",function(){r.destroy()}),ja(e,i,r,t)}!function Fs(){n.add("table",js)}()}(window);