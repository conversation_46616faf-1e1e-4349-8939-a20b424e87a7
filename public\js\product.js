// 产品展示页面的主要JavaScript文件
var PIDLST = [];         // 产品ID列表
var ALL_PRODUCT_LST = []; // 所有产品数据
var NAV_LIST = [];       // 导航菜单数据

// ====== 本地缓存工具函数 ======
function setCache(key, data, expireMinutes = 10) {
    const cacheObj = {
        data: data,
        expire: Date.now() + expireMinutes * 60 * 1000
    };
    localStorage.setItem(key, JSON.stringify(cacheObj));
}

function getCache(key) {
    const cacheStr = localStorage.getItem(key);
    if (!cacheStr) return null;
    try {
        const cacheObj = JSON.parse(cacheStr);
        if (Date.now() < cacheObj.expire) {
            return cacheObj.data;
        } else {
            localStorage.removeItem(key);
            return null;
        }
    } catch (e) {
        localStorage.removeItem(key);
        return null;
    }
}

/**
 * 获取产品URL，兼容不同的字段名称
 * @param {Object} product 产品对象
 * @returns {string} 产品URL
 */
function getProductUrl(product) {
    // 尝试不同的可能URL字段名
    return product.url || product.detail_url || product.link || '#';
}

/**
 * 处理产品URL路径，确保格式正确
 * @param {string} url 原始URL
 * @returns {string} 处理后的URL
 */
function normalizeProductUrl(url) {
    if (!url) return '#';
    
    // 已经是完整URL的情况
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }
    
    // 如果是相对路径但没有/开头
    if (!url.startsWith('/')) {
        return '/' + url;
    }
    
    return url;
}

/**
 * 检查当前页面是否为英文版本
 * @returns {boolean} 是否为英文版本
 */
function isEnglishPage() {
    return window.location.pathname.includes('_en.html') || 
           window.location.pathname.includes('/en/') ||
           window.location.hostname.includes('en.') || 
           window.location.search.includes('lang=en') ||
           document.documentElement.lang === 'en';
}

/**
 * 根据当前页面语言获取语言标识
 * @returns {number} 0表示中文，1表示英文
 */
function getCurrentLang() {
    return isEnglishPage() ? 1 : 0;
}

/**
 * 获取当前语言的无产品提示文本
 * @returns {string} 提示文本
 */
function getNoProductsText() {
    return isEnglishPage() ? 'No products available' : '暂无相关产品';
}

/**
 * 获取当前语言的未命名产品文本
 * @returns {string} 未命名产品文本
 */
function getUnnamedProductText() {
    return isEnglishPage() ? 'Unnamed Product' : '未命名产品';
}

/**
 * 产品点击事件处理函数
 * @param {Element} e 点击的DOM元素
 */
function product_click_fun(e) {
    var detail_name = $(e).attr('data-detail');
    // 如果URL为空或不存在，则不进行跳转
    if (!detail_name) {
        console.warn('产品链接为空');
        return;
    }
    window.open(normalizeProductUrl(detail_name));
}

/**
 * 菜单筛选点击事件处理函数
 * @param {Element} element 点击的菜单项元素
 */
function ul_filter_link_func(element) {
    // 移除所有菜单项的active类
    $('#product_menu a').removeClass('active');
    
    // 为当前点击的菜单项添加active类
    $(element).addClass('active');
    
    // 获取筛选条件
    var filter = $(element).attr('data-filter');
    
    // 检查是否是第一个菜单项
    var isFirstMenuItem = $(element).parent().attr('id') === 'p0';
    
    // 根据筛选条件展示产品
    filterProducts(filter, isFirstMenuItem);
}

/**
 * 根据筛选条件过滤产品
 * @param {string} filter 筛选条件
 * @param {boolean} isFirstMenuItem 是否是第一个菜单项
 */
function filterProducts(filter, isFirstMenuItem) {
    // 清空产品展示区域
    $('#row').empty();
    
    // 如果是第一个菜单项，始终显示所有产品
    if (isFirstMenuItem) {
        displayProducts(ALL_PRODUCT_LST);
        return;
    }
    
    console.log('筛选条件:', filter);
    
    // 创建中英文信息类型映射表（双向映射）
    var typeMapping = {
        '核心板': 'Core Board',
        '主板': 'Mainboard',
        '终端': 'Terminal',
        '方案': 'Solution',
        '智能设备': 'Smart Device',
        '配件': 'Accessory'
    };
    
    // 反向映射表（英文到中文）
    var reverseMapping = {};
    for (var cnType in typeMapping) {
        reverseMapping[typeMapping[cnType]] = cnType;
    }
    
    // 其他菜单项按筛选条件处理
    var filteredProducts = ALL_PRODUCT_LST.filter(function(product) {
        if (!product.info_type) return false;
        
        var productInfoType = product.info_type.toLowerCase();
        var filterLower = filter.toLowerCase();
        
        // 直接匹配
        if (productInfoType.indexOf(filterLower) !== -1) {
            return true;
        }
        
        // 尝试通过映射匹配（英文界面查找对应中文类型，中文界面查找对应英文类型）
        if (isEnglishPage()) {
            // 在英文界面，查找对应的中文类型
            var cnType = reverseMapping[filter];
            if (cnType && productInfoType.indexOf(cnType.toLowerCase()) !== -1) {
                return true;
            }
            
            // 检查原始info_type（如果保存了）
            if (product.original_info_type && 
                product.original_info_type.toLowerCase().indexOf(cnType ? cnType.toLowerCase() : filterLower) !== -1) {
                return true;
            }
        } else {
            // 在中文界面，查找对应的英文类型
            var enType = typeMapping[filter];
            if (enType && productInfoType.indexOf(enType.toLowerCase()) !== -1) {
                return true;
            }
        }
        
        // 同时检查产品名称
        if (product.name && product.name.toLowerCase().indexOf(filterLower) !== -1) {
            return true;
        }
        
        return false;
    });
    
    // 按照display_order字段升序排列
    filteredProducts.sort(function(a, b) {
        var orderA = a.display_order || 0;
        var orderB = b.display_order || 0;
        return orderA - orderB;
    });
    
    console.log('筛选后的产品数量:', filteredProducts.length);
    displayProducts(filteredProducts);
}

/**
 * 显示产品列表
 * @param {Array} products 要显示的产品数据数组
 */
function displayProducts(products) {
    var productsHtml = '';
    
    if (products.length === 0) {
        // 如果没有产品，显示提示信息
        $('#row').html('<div class="no-products">' + getNoProductsText() + '</div>');
        return;
    }
    
    // 定义产品类型的优先级顺序关键词
    var typeKeywords = isEnglishPage() ? 
        {
            // 英文关键词优先级排序 - 增加更多可能的匹配词
            'Core Board': 1,
            'Core': 1,
            'SoM': 1,
            'System on Module': 1,
            'Mainboard': 2,
            'Motherboard': 2,
            'Main Board': 2,
            'Mother Board': 2,
            'Development Board': 2,
            'Terminal': 3,
            'Device': 3,
            'End Product': 3,
            'Finished Product': 3,
            'Solution': 4,
            'Smart Device': 5,
            'Accessory': 6
        } : 
        {
            '核心板': 1,
            '主板': 2,
            '终端': 3,
            '方案': 4,
            '智能设备': 5,
            '配件': 6
        };
    
    // 对产品进行排序
    var sortedProducts = [...products].sort(function(a, b) {
        // 获取产品类型优先级
        var getTypePriority = function(product) {
            if (!product.info_type) return 999;
            
            // 检查info_type是否包含关键词
            for (var keyword in typeKeywords) {
                // 不区分大小写地匹配关键词
                if (product.info_type.toLowerCase().indexOf(keyword.toLowerCase()) !== -1) {
                    return typeKeywords[keyword];
                }
            }
            
            // 尝试通过产品名称推断类型
            if (product.name) {
                for (var keyword in typeKeywords) {
                    if (product.name.toLowerCase().indexOf(keyword.toLowerCase()) !== -1) {
                        return typeKeywords[keyword];
                    }
                }
            }
            
            return 999; // 未匹配到关键词
        };
        
        // 首先按照类型优先级排序
        var typeOrderA = getTypePriority(a);
        var typeOrderB = getTypePriority(b);
        
        if (typeOrderA !== typeOrderB) {
            return typeOrderA - typeOrderB;
        }
        
        // 如果类型相同，则按照display_order排序
        var orderA = a.display_order || 0;
        var orderB = b.display_order || 0;
        return orderA - orderB;
    });
    
    // 遍历排序后的产品数据，生成HTML
    sortedProducts.forEach(function(product) {
        // 只显示状态为显示的产品
        if (product.show === true || product.show === 1 || product.show === '1') {
            // 获取格式化后的产品URL
            var productUrl = normalizeProductUrl(getProductUrl(product));
            
            // 添加调试日志
            console.log('产品URL信息:', {
                name: product.name,
                originalUrl: product.url,
                formattedUrl: productUrl
            });
            
            // 构建产品卡片HTML
            var productHtml = 
                '<div class="gallery-item-wrapper">' +
                '  <div class="gallery-item" onclick="product_click_fun(this)" data-detail="' + productUrl + '">' +
                '    <div class="gallery-thumb">' +
                '      <img src="' + (product.image_path || 'images/default-product.png') + '" alt="' + (product.name || '') + '">' +
                '    </div>' +
                '    <div class="gallery-details">' +
                '      <h5 class="ptitle">' + (product.name || getUnnamedProductText()) + '</h5>' +
                '    </div>' +
                '  </div>' +
                '</div>';
            
            productsHtml += productHtml;
        }
    });
    
    // 更新产品展示区域
    $('#row').html(productsHtml);
    
    // 调试信息 - 显示排序后的产品类型
    console.log('产品排序情况:', sortedProducts.map(function(product) {
        return {
            name: product.name,
            info_type: product.info_type,
            display_order: product.display_order || 0
        };
    }));

    // 产品渲染完毕后再加载footer
    if (typeof window.initFooter === 'function') {
        window.initFooter();
    }
}

/**
 * 初始化产品页面
 * 1. 加载导航菜单
 * 2. 加载所有产品数据
 */
function init_product() {
    var lang = getCurrentLang(); // 获取当前页面语言
    var navType = isEnglishPage() ? 'navigation' : '导航'; // 根据语言确定导航类型名称
    var navCacheKey = 'product_nav_' + lang;

    // 添加调试日志
    console.log('当前语言:', isEnglishPage() ? 'English' : '中文', 'Lang ID:', lang);

    // 检查URL参数中是否有filter参数
    var urlParams = new URLSearchParams(window.location.search);
    var filterFromUrl = urlParams.get('filter');

    if (filterFromUrl) {
        console.log('从URL获取到筛选条件:', filterFromUrl);
    }

    // 优先读取导航缓存
    var cachedNav = getCache(navCacheKey);
    if (cachedNav) {
        NAV_LIST = cachedNav;
        renderNavAndLoadProducts();
        return;
    }

    // 从数据库获取导航栏数据
    $.ajax({
        type: "get",
        url: "/apis/product_list/",
        data: { info_type: navType, lang: lang }, // 根据当前语言获取导航数据
        success: function(response) {
            if (response.status === 'ok') {
                NAV_LIST = response.data;
                setCache(navCacheKey, NAV_LIST, 60); // 缓存10分钟
                renderNavAndLoadProducts();
            }
        }
    });

    function renderNavAndLoadProducts() {
        // 生成导航菜单
        var menu_str = '';
        NAV_LIST.sort(function(a, b) {
            if (a.name === (isEnglishPage() ? 'All' : '全部')) return -1;
            if (b.name === (isEnglishPage() ? 'All' : '全部')) return 1;
            return (a.display_order || 0) - (b.display_order || 0);
        });
        NAV_LIST.forEach(function(nav, index) {
            var displayText = nav.name || (isEnglishPage() ? 'Unnamed' : '未命名');
            var filterValue = (displayText === '全部' || displayText === 'All') ? 'all' : displayText;
            var isActive = (index === 0);
            if (filterFromUrl && filterValue === filterFromUrl) {
                isActive = true;
            } else if (filterFromUrl && index === 0) {
                isActive = false;
            }
            menu_str += '<li id="p' + index + '">' +
                '<a href="#" onclick="ul_filter_link_func(this)" data-filter="' + filterValue + '" ' +
                'style="text-transform: capitalize;cursor: pointer;"' +
                (isActive ? ' class="active"' : '') + '>' + 
                displayText + '</a></li>';
        });
        $('#product_menu').html(menu_str);
        // 加载产品数据
        loadProducts();
        // 如果有URL筛选参数，自动触发筛选
        if (filterFromUrl) {
            setTimeout(function() {
                var menuItem = $('#product_menu a[data-filter="' + filterFromUrl + '"]');
                if (menuItem.length > 0) {
                    var isFirstMenuItem = menuItem.parent().attr('id') === 'p0';
                    filterProducts(filterFromUrl, isFirstMenuItem);
                }
            }, 500);
        }
    }
}

/**
 * 加载产品数据
 */
function loadProducts() {
    var lang = getCurrentLang(); // 获取当前页面语言
    var navType = isEnglishPage() ? 'navigation' : '导航'; // 根据语言确定导航类型名称
    var cacheKey = 'product_list_' + lang;

    // 先尝试读取缓存
    var cachedData = getCache(cacheKey);
    if (cachedData) {
        processProductData(cachedData);
        return;
    }

    // 没有缓存才请求接口
    $.ajax({
        type: "get",
        url: "/apis/product_list/",
        data: { 
            size: 1000,  // 获取足够多的产品数据
            info_type_not: navType, // 排除导航类型
            lang: lang // 根据当前语言获取产品数据
        },
        success: function(response) {
            if (response.status === 'ok') {
                setCache(cacheKey, response.data, 60); // 缓存10分钟
                processProductData(response.data);
            }
        }
    });
}

function processProductData(data) {
    var lang = getCurrentLang();
    var navType = isEnglishPage() ? 'navigation' : '导航';
    // 过滤数据，确保不包含navigation类型
    var filteredData = data.filter(function(item) {
        if (isEnglishPage()) {
            return item.info_type !== 'navigation';
        }
        return item.info_type !== '导航';
    });
    // 处理英文界面信息类型映射
    ALL_PRODUCT_LST = filteredData.map(function(item) {
        var processedItem = {...item};
        if (isEnglishPage() && processedItem.info_type) {
            var typeMapping = {
                '核心板': 'Core Board',
                '主板': 'Mainboard',
                '终端': 'Terminal',
                '方案': 'Solution',
                '智能设备': 'Smart Device',
                '配件': 'Accessory'
            };
            for (var cnType in typeMapping) {
                if (processedItem.info_type.includes(cnType)) {
                    processedItem.original_info_type = processedItem.info_type;
                    processedItem.info_type = typeMapping[cnType];
                    break;
                }
            }
        }
        return processedItem;
    });
    displayProducts(ALL_PRODUCT_LST);
}

// 页面加载完成后初始化产品页面
$(document).ready(function() {
    init_product();
}); 

