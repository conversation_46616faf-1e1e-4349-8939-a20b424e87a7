<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Animate carousel">
    <meta name="author" content="<PERSON>">
    <title>
      Animate Demo | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/demos/demos.html">Demos</a> 
              </li>
              <li> <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a>  </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>Animate</h1>
        </div>
      </div>
    </section>

    <!--  Demos -->
    <section id="demos">
      <div class="row">
        <div class="large-12 columns">
          <div class="fadeOut owl-carousel owl-theme">
            <div class="item">
              <h4>1</h4>
            </div>
            <div class="item">
              <h4>2</h4>
            </div>
            <div class="item">
              <h4>3</h4>
            </div>
            <div class="item">
              <h4>4</h4>
            </div>
            <div class="item">
              <h4>5</h4>
            </div>
            <div class="item">
              <h4>6</h4>
            </div>
            <div class="item">
              <h4>7</h4>
            </div>
            <div class="item">
              <h4>8</h4>
            </div>
            <div class="item">
              <h4>9</h4>
            </div>
            <div class="item">
              <h4>10</h4>
            </div>
            <div class="item">
              <h4>11</h4>
            </div>
            <div class="item">
              <h4>12</h4>
            </div>
          </div>
          <h3 id="overview">Overview</h3>
          <p>To get fade out effect just set:</p>
          <pre><code>animateOut: &#39;fadeOut&#39;</code></pre>
          <blockquote>
            <p><code>fadeOut</code> value is the only built-in CSS animate style. However there are tons of additional CSS animations that you can use in Owl. Simply download
              <a href="https://daneden.github.io/animate.css/">animate.css</a>  library and you are ready to extend Owl with new fancy transitions.</p>
          </blockquote>
          <h3 id="important">Important</h3>
          <p>Animate functions work only with one item and only in browsers that support perspective property.</p>
          <h3 id="how-to-use-additional-animation-from-animate-css-library">How to use additional animation from <code>animate.css</code> library</h3>
          <ol>
            <li> <a href="https://daneden.github.io/animate.css/">Download animate.css</a>  </li>
            <li>Include animate.css into header.</li>
            <li>Set <code>animateOut</code> and <code>animateIn</code> options with the style names you picked.</li>
          </ol>
          <pre><code>$(&#39;.custom1&#39;).owlCarousel({
    animateOut: &#39;slideOutDown&#39;,
    animateIn: &#39;flipInX&#39;,
    items:1,
    margin:30,
    stagePadding:30,
    smartSpeed:450
});</code></pre>
          <p>Example with slideOutDown and flipInX</p>
          <div class="custom1 owl-carousel owl-theme">
            <div class="item">
              <h4>1</h4>
            </div>
            <div class="item">
              <h4>2</h4>
            </div>
            <div class="item">
              <h4>3</h4>
            </div>
            <div class="item">
              <h4>4</h4>
            </div>
            <div class="item">
              <h4>5</h4>
            </div>
            <div class="item">
              <h4>6</h4>
            </div>
            <div class="item">
              <h4>7</h4>
            </div>
            <div class="item">
              <h4>8</h4>
            </div>
            <div class="item">
              <h4>9</h4>
            </div>
            <div class="item">
              <h4>10</h4>
            </div>
            <div class="item">
              <h4>11</h4>
            </div>
            <div class="item">
              <h4>12</h4>
            </div>
          </div>
          <h3 id="how-does-it-work-">How does it work?</h3>
          <p>Before animation starts three classes are added to each item:</p>
          <ul>
            <li>.animated - added on both In and Out item - ive included this class from Animate.css into Owl core CSS file.</li>
            <li>.owl-animated-out - only on Out item - use it to change z-index</li>
            <li>.owl-animated-in - only on In item - use it to change z-index</li>
            <li>.classNameOut - only on Out item - this is your custom animation class from options.</li>
            <li>.classNameIn - only on In item - this is your custom animation class from options.</li>
          </ul>
          <p>Part of owl.carousel.css:</p>
          <pre><code class="language-css"><span class="comment">/* Feel free to change duration  */</span> 
<span class="class">.animated</span>  <span class="rules">{
  <span class="rule"><span class="attribute">-webkit-animation-duration</span> :<span class="value"> <span class="number">1000</span> ms</span> </span> ;
  <span class="rule"><span class="attribute">animation-duration</span> :<span class="value"> <span class="number">1000</span> ms</span> </span> ;
  <span class="rule"><span class="attribute">-webkit-animation-fill-mode</span> :<span class="value"> both</span> </span> ;
  <span class="rule"><span class="attribute">animation-fill-mode</span> :<span class="value"> both</span> </span> ;
<span class="rule">}</span> </span> 
<span class="comment">/* .owl-animated-out - only for current item */</span> 
<span class="comment">/* This is very important class. Use z-index if you want move Out item above In item */</span> 
<span class="class">.owl-animated-out</span> <span class="rules">{
  <span class="rule"><span class="attribute">z-index</span> :<span class="value"> <span class="number">1</span> 
</span> </span> </span> }
<span class="comment">/* .owl-animated-in - only for upcoming item
/* This is very important class. Use z-index if you want move In item above Out item */</span> 
<span class="class">.owl-animated-in</span> <span class="rules">{
  <span class="rule"><span class="attribute">z-index</span> :<span class="value"> <span class="number">0</span> 
</span> </span> </span> }
<span class="comment">/* .fadeOut is style taken from Animation.css and this is how it looks in owl.carousel.css:  */</span> 
<span class="class">.fadeOut</span>  <span class="rules">{
  <span class="rule"><span class="attribute">-webkit-animation-name</span> :<span class="value"> fadeOut</span> </span> ;
  <span class="rule"><span class="attribute">animation-name</span> :<span class="value"> fadeOut</span> </span> ;
<span class="rule">}</span> </span> 
<span class="at_rule">@<span class="keyword">-webkit-keyframes</span>  fadeOut </span> {
  0% <span class="rules">{
    <span class="rule"><span class="attribute">opacity</span> :<span class="value"> <span class="number">1</span> </span> </span> ;
  <span class="rule">}</span> </span> 
  100% <span class="rules">{
    <span class="rule"><span class="attribute">opacity</span> :<span class="value"> <span class="number">0</span> </span> </span> ;
  <span class="rule">}</span> </span> 
}
<span class="at_rule">@<span class="keyword">keyframes</span>  fadeOut </span> {
  0% <span class="rules">{
    <span class="rule"><span class="attribute">opacity</span> :<span class="value"> <span class="number">1</span> </span> </span> ;
  <span class="rule">}</span> </span> 
  100% <span class="rules">{
    <span class="rule"><span class="attribute">opacity</span> :<span class="value"> <span class="number">0</span> </span> </span> ;
  <span class="rule">}</span> </span> 
}</code></pre>
          <link rel="stylesheet" href="../assets/css/animate.css">
          <script>
            jQuery(document).ready(function($) {
              $('.fadeOut').owlCarousel({
                items: 1,
                animateOut: 'fadeOut',
                loop: true,
                margin: 10,
              });
              $('.custom1').owlCarousel({
                animateOut: 'slideOutDown',
                animateIn: 'flipInX',
                items: 1,
                margin: 30,
                stagePadding: 30,
                smartSpeed: 450
              });
            });
          </script>
        </div>
      </div>
    </section>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>