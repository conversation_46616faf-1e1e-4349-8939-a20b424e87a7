{"all": [{"class": "中文教程", "wikis": [{"name": "RK3399Pro开发板使用教程", "desc": "本教程详细介绍了RK3399Pro开发板的使用方法，包括硬件连接、系统烧录、驱动安装等内容。", "img": "images/wiki/rk3399.jpg", "link": "wiki_detail.html?id=1"}, {"name": "人脸识别应用开发指南", "desc": "介绍如何使用贝启科技的人脸识别模块进行应用开发，包含API调用方式和示例代码。", "img": "images/wiki/face.jpg", "link": "wiki_detail.html?id=2"}, {"name": "智能语音交互系统搭建", "desc": "从零开始搭建一个完整的智能语音交互系统，包括硬件选型、软件配置和应用开发。", "img": "images/wiki/voice.jpg", "link": "wiki_detail.html?id=3"}]}, {"class": "English Tutorials", "wikis": [{"name": "RK3399Pro Development Board Guide", "desc": "This tutorial introduces how to use RK3399Pro development board, including hardware connection, system burning, driver installation, etc.", "img": "images/wiki/rk3399.jpg", "link": "wiki_detail.html?id=4"}, {"name": "Face Recognition Development Guide", "desc": "Learn how to develop applications using Bearkey's face recognition module, including API calling methods and sample code.", "img": "images/wiki/face.jpg", "link": "wiki_detail.html?id=5"}]}]}