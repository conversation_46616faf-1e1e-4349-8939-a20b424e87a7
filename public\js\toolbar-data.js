// 工具栏自动加载和初始化
(function() {
    // 定义全局变量
    let toolbarButtons = []; // 存储工具栏按钮的引用
    const buttonPositions = []; // 存储按钮位置
    let isToolbarCreated = false; // 标记工具栏是否已创建
    
    let toolbarData = {}; // 存储工具栏数据
    
    // 全局变量用于存储悬停定时器
    const hoverTimers = {
        0: null,
        1: null,
        2: null,
        3: null
    };
    
    // 图片路径
    const imagePaths = [
        '/images/tool/路径 22.png',  // 客服
        '/images/tool/组 661.png',   // 邮箱
        '/images/tool/组 667.png',   // 电话
        '/images/tool/联合 2.png'    // 微信
    ];
    
    // 图片替代文本
    const altTexts = ['客服', '邮箱', '电话', '微信'];
    
    // 按钮颜色
    const colors = ['#0050A2', '#0050A2', '#0050A2', '#0050A2'];

    // 获取工具栏数据
    async function fetchToolbarData() {
        try {
            console.log('=== 开始获取工具栏数据 ===');
            const isEnglish = detectEnglishPage();
            console.log('当前语言:', isEnglish ? '英文' : '中文');
            
            // 获取中英文数据
            const response = await $.ajax({
                url: '/apis/company_info/list',
                type: 'GET',
                data: {
                    page: 1,
                    page_size: 100,
                    filters: JSON.stringify({
                        info_type: 'toolbar',
                        lang: isEnglish ? 1 : 0,
                        show: true
                    })
                }
            });

            console.log('API请求参数:', {
                url: '/apis/company_info/list',
                filters: {
                    info_type: 'toolbar',
                    lang: isEnglish ? 1 : 0,
                    show: true
                }
            });

            if (response.status === 'ok' && response.data) {
                console.log(`获取到${response.data.length}条工具栏数据`);
                
                // 输出原始数据
                console.log('原始数据:');
                response.data.forEach((item, index) => {
                    console.log(`[${index}] 数据项:`, {
                        ID: item.id,
                        标题: item.title,
                        内容: item.content || '无文本内容',
                        图片路径: item.image_path || '无图片',
                        显示顺序: item.display_order,
                        信息类型: item.info_type,
                        语言: item.lang === 1 ? '英文' : '中文',
                        是否显示: item.show ? '是' : '否'
                    });
                });

                // 将数据按标题分类存储
                toolbarData = response.data.reduce((acc, item) => {
                    // 根据语言映射标题
                    let key = item.title;
                    if (isEnglish) {
                        switch (item.title) {
                            case '在线客服':
                                key = 'E-Service';
                                break;
                            case '商务微信':
                                key = 'Business';
                                break;
                            case '邮箱':
                                key = 'E-Mail';
                                break;
                            case '电话咨询':
                                key = 'Phone';
                                break;
                        }
                    }
                    acc[key] = item;
                    return acc;
                }, {});
                
                // 输出分类后的数据
                console.log('=== 按标题分类后的数据 ===');
                Object.entries(toolbarData).forEach(([title, data]) => {
                    console.log(`\n${title}:`, {
                        ID: data.id,
                        内容: data.content || '无文本内容',
                        图片路径: data.image_path || '无图片',
                        显示顺序: data.display_order
                    });
                });
                
                // 验证必需的数据项
                const requiredTitles = isEnglish ? 
                    ['E-Service', 'Business', 'E-Mail', 'Phone'] :
                    ['在线客服', '商务微信', '邮箱', '电话咨询'];
                    
                console.log('\n=== 数据验证 ===');
                requiredTitles.forEach(title => {
                    const data = toolbarData[title];
                    if (data) {
                        console.log(`✓ ${title}: 数据已获取`);
                        if (title === (isEnglish ? 'E-Service' : '在线客服') || 
                            title === (isEnglish ? 'Business' : '商务微信')) {
                            console.log(`  - 图片: ${data.image_path ? '已提供' : '缺失'}`);
                        }
                        if (title === (isEnglish ? 'E-Mail' : '邮箱') || 
                            title === (isEnglish ? 'Phone' : '电话咨询')) {
                            console.log(`  - 内容: ${data.content ? '已提供' : '缺失'}`);
                        }
                    } else {
                        console.log(`✗ ${title}: 数据缺失，将使用默认值`);
                    }
                });
                
                console.log('\n=== 工具栏数据获取完成 ===');
            } else {
                console.warn('API返回状态异常或无数据:', response);
            }
        } catch (error) {
            console.error('获取工具栏数据失败:', error);
            console.log('将使用默认值显示工具栏内容');
        }
    }

    // 创建客服弹窗函数
    function showCustomerServicePopup(isEnglish) {
        // 获取客服数据并检查显示状态
        const serviceData = toolbarData[isEnglish ? 'E-Service' : '在线客服'];
        if (!serviceData || !serviceData.show) {
            console.log(isEnglish ? 'E-Service data not found or hidden' : '在线客服数据不存在或设置为不显示');
            return null;
        }

        // 关闭其他弹窗
        closeAllPopups('.customer-service-popup');
        
        // 检查是否已有弹窗
        let popup = document.querySelector('.customer-service-popup');
        if (popup) {
            clearTimeout(popup.hideTimeout);
            return popup;
        }
        
        // 创建弹窗容器
        popup = document.createElement('div');
        popup.className = 'customer-service-popup';
        popup.style.position = 'fixed';
        popup.style.width = '120px';
        popup.style.height = '150px';
        popup.style.backgroundColor = '#FFFFFF';
        popup.style.borderRadius = '8px';
        popup.style.border = '1px solid #0062BA';
        popup.style.zIndex = '9998';
        popup.style.right = '90px';
        popup.style.top = buttonPositions[0] + 'px';
        popup.style.display = 'flex';
        popup.style.flexDirection = 'column';
        popup.style.alignItems = 'center';
        
        // 创建二维码容器
        const qrContainer = document.createElement('div');
        qrContainer.style.display = 'flex';
        qrContainer.style.justifyContent = 'center';
        qrContainer.style.alignItems = 'center';
        qrContainer.style.width = '100%';
        qrContainer.style.height = '110px';
        qrContainer.style.padding = '10px';
        qrContainer.style.boxSizing = 'border-box';
        
        // 创建二维码图片
        const qrImage = document.createElement('img');
        qrImage.src = serviceData.image_path ? normalizeImagePath(serviceData.image_path) : './images/tool/蒙版组 42.png';
        qrImage.alt = isEnglish ? 'Scan QR Code' : '扫码咨询';
        qrImage.style.marginTop = '7px';
        qrImage.style.objectFit = 'contain';
        
        // 创建标题/说明文字
        const title = document.createElement('div');
        title.textContent = serviceData.title || (isEnglish ? 'E-Service' : '在线客服');
        title.style.fontFamily = 'Source Han Sans CN, sans-serif';
        title.style.fontWeight = '400';
        title.style.fontSize = '14px';
        title.style.color = '#323232';
        title.style.lineHeight = '29px';
        title.style.textAlign = 'center';
        title.style.fontStyle = 'normal';
        title.style.width = '100%';
        title.style.marginTop = 'auto';
        title.style.paddingBottom = '3px';
        
        // 组装弹窗
        qrContainer.appendChild(qrImage);
        popup.appendChild(qrContainer);
        popup.appendChild(title);
        
        // 添加到页面
        document.body.appendChild(popup);
        
        // 获取第一个按钮
        const firstButton = document.querySelector('.toolbar-test-btn[data-index="0"]');
        
        // 添加鼠标事件监听
        addPopupEventListeners(popup, firstButton);
        
        return popup;
    }
    
    // 创建邮箱弹窗函数
    function showEmailPopup() {
        const isEnglish = detectEnglishPage();
        // 获取邮箱数据并检查显示状态
        const emailData = toolbarData[isEnglish ? 'E-Mail' : '邮箱'];
        if (!emailData || !emailData.show) {
            console.log(isEnglish ? 'E-Mail data not found or hidden' : '邮箱数据不存在或设置为不显示');
            return null;
        }

        // 关闭其他弹窗
        closeAllPopups('.email-popup');
        
        // 检查是否已有弹窗
        let popup = document.querySelector('.email-popup');
        if (popup) {
            clearTimeout(popup.hideTimeout);
            return popup;
        }
        
        // 创建弹窗容器
        popup = document.createElement('div');
        popup.className = 'email-popup';
        popup.style.position = 'fixed';
        popup.style.width = '229px';
        popup.style.height = '64px';
        popup.style.backgroundColor = '#007DDB';
        popup.style.borderRadius = '8px';
        popup.style.zIndex = '9998';
        popup.style.right = '90px';
        popup.style.top = buttonPositions[1] + 'px';
        popup.style.display = 'flex';
        popup.style.alignItems = 'center';
        popup.style.justifyContent = 'center';
        
        // 创建邮箱文本
        const emailText = document.createElement('div');
        emailText.textContent = emailData.content || '<EMAIL>';
        emailText.style.fontFamily = 'Source Han Sans CN, sans-serif';
        emailText.style.fontWeight = '400';
        emailText.style.fontSize = '18px';
        emailText.style.color = '#FFFFFF';
        emailText.style.lineHeight = '29px';
        emailText.style.fontStyle = 'normal';
        emailText.style.textTransform = 'none';
        emailText.style.textAlign = 'center';
        
        // 组装弹窗
        popup.appendChild(emailText);
        
        // 添加到页面
        document.body.appendChild(popup);
        
        // 获取第二个按钮
        const secondButton = document.querySelector('.toolbar-test-btn[data-index="1"]');
        
        // 添加鼠标事件监听
        addPopupEventListeners(popup, secondButton);
        
        return popup;
    }
    
    // 创建电话弹窗函数
    function showPhonePopup() {
        const isEnglish = detectEnglishPage();
        // 获取电话数据并检查显示状态
        const phoneData = toolbarData[isEnglish ? 'Phone' : '电话咨询'];
        if (!phoneData || !phoneData.show) {
            console.log(isEnglish ? 'Phone data not found or hidden' : '电话数据不存在或设置为不显示');
            return null;
        }

        // 关闭其他弹窗
        closeAllPopups('.phone-popup');
        
        // 检查是否已有弹窗
        let popup = document.querySelector('.phone-popup');
        if (popup) {
            clearTimeout(popup.hideTimeout);
            return popup;
        }
        
        // 创建弹窗容器
        popup = document.createElement('div');
        popup.className = 'phone-popup';
        popup.style.position = 'fixed';
        popup.style.width = '176px';
        popup.style.height = '93px';
        popup.style.backgroundColor = '#007DDB';
        popup.style.borderRadius = '8px';
        popup.style.zIndex = '9998';
        popup.style.right = '90px';
        popup.style.top = '533px';
        popup.style.display = 'flex';
        popup.style.flexDirection = 'column';
        popup.style.alignItems = 'center';
        popup.style.justifyContent = 'center';
        popup.style.padding = '10px';
        
        // 创建电话号码文本容器
        const phoneContainer = document.createElement('div');
        phoneContainer.style.width = '100%';
        phoneContainer.style.display = 'flex';
        phoneContainer.style.flexDirection = 'column';
        phoneContainer.style.alignItems = 'center';
        phoneContainer.style.justifyContent = 'center';
        
        // 获取电话号码
        const phoneNumbers = phoneData.content ? phoneData.content.split(' ') : ['0592-5232963', '18606919996'];
        
        // 创建电话号码文本
        phoneNumbers.forEach(number => {
            const phoneText = document.createElement('div');
            phoneText.textContent = number;
            phoneText.style.fontFamily = 'Source Han Sans CN, sans-serif';
            phoneText.style.fontWeight = '400';
            phoneText.style.fontSize = '20px';
            phoneText.style.color = '#FFFFFF';
            phoneText.style.lineHeight = '30px';
            phoneText.style.textAlign = 'center';
            phoneText.style.fontStyle = 'normal';
            phoneText.style.textTransform = 'none';
            phoneText.style.width = '100%';
            phoneContainer.appendChild(phoneText);
        });
        
        // 组装弹窗
        popup.appendChild(phoneContainer);
        
        // 添加到页面
        document.body.appendChild(popup);
        
        // 获取第三个按钮
        const thirdButton = document.querySelector('.toolbar-test-btn[data-index="2"]');
        
        // 添加鼠标事件监听
        addPopupEventListeners(popup, thirdButton);
        
        return popup;
    }
    
    // 创建微信弹窗函数
    function showWeChatPopup(isEnglish) {
        // 获取微信数据并检查显示状态
        const wechatData = toolbarData[isEnglish ? 'Business' : '商务微信'];
        if (!wechatData || !wechatData.show) {
            console.log(isEnglish ? 'Business WeChat data not found or hidden' : '商务微信数据不存在或设置为不显示');
            return null;
        }

        // 关闭其他弹窗
        closeAllPopups('.wechat-popup');
        
        // 检查是否已有弹窗
        let popup = document.querySelector('.wechat-popup');
        if (popup) {
            clearTimeout(popup.hideTimeout);
            return popup;
        }
        
        // 创建弹窗容器
        popup = document.createElement('div');
        popup.className = 'wechat-popup';
        popup.style.position = 'fixed';
        popup.style.width = '120px';
        popup.style.height = '150px';
        popup.style.backgroundColor = '#FFFFFF';
        popup.style.borderRadius = '8px';
        popup.style.border = '1px solid #0062BA';
        popup.style.zIndex = '9998';
        popup.style.right = '90px';
        popup.style.top = '468px';
        popup.style.display = 'flex';
        popup.style.flexDirection = 'column';
        popup.style.alignItems = 'center';
        
        // 创建二维码容器
        const qrContainer = document.createElement('div');
        qrContainer.style.display = 'flex';
        qrContainer.style.justifyContent = 'center';
        qrContainer.style.alignItems = 'center';
        qrContainer.style.width = '100%';
        qrContainer.style.height = 'auto';
        qrContainer.style.boxSizing = 'border-box';
        
        // 创建二维码图片
        const qrImage = document.createElement('img');
        qrImage.src = wechatData.image_path ? normalizeImagePath(wechatData.image_path) : './images/tool/矩形 1623.png';
        qrImage.alt = isEnglish ? 'Business WeChat QR Code' : '商务微信二维码';
        qrImage.style.marginTop = '5px';
        qrImage.style.objectFit = 'contain';
        
        // 创建标题/说明文字
        const title = document.createElement('div');
        title.textContent = wechatData.title || (isEnglish ? 'Business' : '商务微信');
        title.style.fontFamily = 'Source Han Sans CN, sans-serif';
        title.style.fontWeight = '400';
        title.style.fontSize = '14px';
        title.style.color = '#323232';
        title.style.lineHeight = '29px';
        title.style.textAlign = 'center';
        title.style.fontStyle = 'normal';
        title.style.textTransform = 'none';
        title.style.width = '100%';
        title.style.marginTop = 'auto';
        title.style.paddingBottom = '3px';
        
        // 组装弹窗
        qrContainer.appendChild(qrImage);
        popup.appendChild(qrContainer);
        popup.appendChild(title);
        
        // 添加到页面
        document.body.appendChild(popup);
        
        // 获取第四个按钮
        const fourthButton = document.querySelector('.toolbar-test-btn[data-index="3"]');
        
        // 添加鼠标事件监听
        addPopupEventListeners(popup, fourthButton);
        
        return popup;
    }

    // 添加弹窗鼠标事件监听器
    function addPopupEventListeners(popup, button) {
        // 鼠标移入弹窗时清除定时器
        popup.addEventListener('mouseenter', function() {
            clearTimeout(popup.hideTimeout);
        });
        
        // 鼠标移出弹窗时设置1秒后关闭
        popup.addEventListener('mouseleave', function() {
            if (document.querySelector('.toolbar-test-btn:hover') !== button) {
                popup.hideTimeout = setTimeout(function() {
                    popup.remove();
                }, 1000);
            }
        });
        
        // 鼠标移出按钮时设置定时器，如果鼠标不在弹窗上才关闭
        button.addEventListener('mouseleave', function() {
            if (!popup.matches(':hover')) {
                popup.hideTimeout = setTimeout(function() {
                    popup.remove();
                }, 1000);
            }
        });
    }

    // 处理图片路径
    function normalizeImagePath(imgPath) {
        if (!imgPath) return '';
        
        // 移除可能的Windows路径格式
        imgPath = imgPath.replace(/\\/g, '/');
        
        // 如果是完整URL，直接返回
        if (imgPath.startsWith('http://') || imgPath.startsWith('https://')) {
            return imgPath;
        }
        
        // 如果是相对路径，确保以/开头
        return imgPath.startsWith('/') ? imgPath : '/' + imgPath;
    }
    
    // 检测页面语言
    function detectEnglishPage() {
        // 方法1：检查HTML标签的lang属性
        const htmlLang = document.documentElement.lang || '';
        if (htmlLang.toLowerCase().startsWith('en')) {
            return true;
        }
        
        // 方法2：检查body上的类名
        if (document.body.classList.contains('english-version')) {
            return true;
        }
        
        // 方法3：检查URL中是否包含en或english
        const url = window.location.href.toLowerCase();
        if (url.includes('/en/') || url.includes('/english/') || url.includes('lang=en')) {
            return true;
        }
        
        // 默认返回false，表示中文页面
        return false;
    }
    
    // 判断是否在首页轮播图区域
    function isInHomeCarousel() {
        // 检查是否在首页
        const isHomePage = window.location.pathname === '/' || 
                          window.location.pathname.endsWith('index.html') || 
                          window.location.pathname.endsWith('index_en.html');
        
        if (!isHomePage) {
            // 非首页直接返回false
            return false;
        }
        
        // 获取轮播图区域
        const carouselSection = document.querySelector('.carousel-bg');
        if (!carouselSection) {
            return false;
        }
        
        // 获取轮播图区域的位置信息
        const carouselRect = carouselSection.getBoundingClientRect();
        
        // 轮播图区域判断逻辑：当轮播图在视口中可见时
        return (carouselRect.top < window.innerHeight && carouselRect.bottom > 0);
    }
    
    // 关闭所有弹窗的函数
    function closeAllPopups(exceptClass) {
        const popupClasses = ['.customer-service-popup', '.email-popup', '.phone-popup', '.wechat-popup'];
        popupClasses.forEach(function(className) {
            if (className !== exceptClass) {
                const popup = document.querySelector(className);
                if (popup) {
                    clearTimeout(popup.hideTimeout);
                    popup.remove();
                }
            }
        });
    }
    
    // 创建工具栏
    function createToolbar() {
        if (isToolbarCreated) return;
        
        // 检测页面语言
        const isEnglishPage = detectEnglishPage();
        console.log('页面语言: ' + (isEnglishPage ? '英文' : '中文'));
        
        // 检查是否为小屏幕
        const isSmallScreen = window.innerWidth <= 491;
        console.log('是否为小屏幕: ' + isSmallScreen);
        
        // 创建按钮
        for (let i = 0; i < 4; i++) {
            const btn = document.createElement('div');
            btn.className = 'toolbar-test-btn';
            btn.setAttribute('data-index', i);
            btn.style.position = 'fixed';
            btn.style.right = isSmallScreen ? '-44px' : '16px';
            const topPosition = (350 + i * 68);
            btn.style.top = topPosition + 'px';
            buttonPositions.push(topPosition);
            
            btn.style.width = '64px';
            btn.style.height = '64px';
            btn.style.backgroundColor = colors[i];
            btn.style.color = 'white';
            btn.style.borderRadius = '8px';
            btn.style.display = 'flex';
            btn.style.alignItems = 'center';
            btn.style.justifyContent = 'center';
            btn.style.zIndex = '9999';
            btn.style.cursor = 'pointer';
            btn.style.fontWeight = 'bold';
            btn.style.transition = 'all 0.3s ease';
            
            // 为按钮添加图片
            const img = document.createElement('img');
            img.src = imagePaths[i];
            img.alt = altTexts[i];
            img.style.maxWidth = '90%';
            img.style.maxHeight = '90%';
            img.style.filter = 'brightness(0) invert(1)';
            img.style.objectFit = 'contain';
            img.style.display = 'block';
            btn.appendChild(img);
            
            // 小屏幕时添加触碰展开效果
            if (isSmallScreen && i === 0) {
                // 创建一个包含所有按钮的容器
                const toolbarContainer = document.createElement('div');
                toolbarContainer.className = 'toolbar-container';
                toolbarContainer.style.position = 'fixed';
                toolbarContainer.style.right = '0';
                toolbarContainer.style.top = '350px';
                toolbarContainer.style.width = '64px';
                toolbarContainer.style.height = '270px';
                toolbarContainer.style.zIndex = '9998';
                toolbarContainer.style.transition = 'all 0.3s ease';
                
                // 鼠标移入容器时展开所有按钮
                toolbarContainer.addEventListener('mouseenter', function() {
                    toolbarButtons.forEach(button => {
                        button.style.right = '16px';
                    });
                });
                
                // 鼠标移出容器时收起所有按钮
                toolbarContainer.addEventListener('mouseleave', function() {
                    toolbarButtons.forEach(button => {
                        button.style.right = '-44px';
                        
                        // 关闭所有弹窗
                        closeAllPopups();
                    });
                });
                
                document.body.appendChild(toolbarContainer);
            }
            
            // 添加悬停效果
            btn.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#007DDB';
                
                // 小屏幕时展开按钮
                if (isSmallScreen) {
                    this.style.right = '16px';
                }
                
                // 清除之前的定时器
                clearTimeout(hoverTimers[i]);
                
                // 设置延迟0.3秒后显示弹窗
                const index = parseInt(this.getAttribute('data-index'));
                hoverTimers[index] = setTimeout(function() {
                    if (index === 0) {
                        showCustomerServicePopup(isEnglishPage);
                    } else if (index === 1) {
                        showEmailPopup();
                    } else if (index === 2) {
                        showPhonePopup();
                    } else if (index === 3) {
                        showWeChatPopup(isEnglishPage);
                    }
                }, 300);
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.backgroundColor = colors[i];
                
                // 鼠标离开按钮时清除悬停定时器
                const index = parseInt(this.getAttribute('data-index'));
                clearTimeout(hoverTimers[index]);
            });
            
            document.body.appendChild(btn);
            toolbarButtons.push(btn);
            console.log('按钮 ' + altTexts[i] + ' 已创建，位置：' + topPosition);
        }
        
        isToolbarCreated = true;
    }
    
    // 更新工具栏显示状态
    function updateToolbarVisibility() {
        // 判断是否在首页轮播图区域
        const inCarouselArea = isInHomeCarousel();
        
        // 已创建工具栏的情况
        if (isToolbarCreated) {
            if (inCarouselArea) {
                // 在轮播图区域，隐藏工具栏
                toolbarButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 关闭所有弹窗
                closeAllPopups();
            } else {
                // 不在轮播图区域，显示工具栏
                toolbarButtons.forEach(btn => {
                    btn.style.display = 'flex';
                });
            }
        } else {
            // 未创建工具栏且不在轮播图区域，创建工具栏
            if (!inCarouselArea) {
                createToolbar();
            }
        }
    }
    
    // 处理窗口大小变化
    function handleResize() {
        // 移除所有现有的工具栏按钮和容器
        toolbarButtons.forEach(btn => btn.remove());
        toolbarButtons = [];
        
        const container = document.querySelector('.toolbar-container');
        if (container) container.remove();
        
        // 重置工具栏状态
        isToolbarCreated = false;
        buttonPositions.length = 0;
        
        // 根据当前状态重新创建工具栏
        updateToolbarVisibility();
    }
    
    // 初始化函数
    async function init() {
        console.log('工具栏初始化开始');
        
        // 先获取工具栏数据
        await fetchToolbarData();
        
        // 先检查是否在轮播图区域
        updateToolbarVisibility();
        
        // 监听滚动事件
        window.addEventListener('scroll', updateToolbarVisibility);
        
        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);
        
        console.log('工具栏初始化完成');
    }
    
    // 当DOM完全加载时初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟一小段时间，确保页面元素已加载
            setTimeout(init, 100);
        });
    } else {
        // 如果DOM已经加载完成，延迟执行初始化
        setTimeout(init, 100);
    }
    
    // 页面完全加载后再次检查
    window.addEventListener('load', function() {
        setTimeout(updateToolbarVisibility, 500);
    });
    
    // 暴露到全局，方便其他脚本调用
    window.updateToolbarVisibility = updateToolbarVisibility;
})(); 