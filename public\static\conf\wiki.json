{"all": [{"class": "开发板教程", "wikis": [{"name": "RK3399Pro开发板", "desc": "高性能AI开发板使用教程", "img": "images/wiki/rk3399.jpg", "link": "https://www.bearkey.net/rk3399pro"}, {"name": "RK1808开发板", "desc": "边缘计算AI开发板使用教程", "img": "images/wiki/rk1808.jpg", "link": "https://www.bearkey.net/rk1808"}]}, {"class": "解决方案教程", "wikis": [{"name": "人脸识别方案", "desc": "基于RK3399Pro的人脸识别解决方案", "img": "images/wiki/face.jpg", "link": "https://www.bearkey.net/face"}, {"name": "智能语音方案", "desc": "基于RK3308的智能语音解决方案", "img": "images/wiki/voice.jpg", "link": "https://www.bearkey.net/voice"}]}]}