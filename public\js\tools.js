(function ($) {
    $.getUrlParam = function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
})(jQuery);


function showToast(msg, duration) {
    duration = isNaN(duration) ? 3000 : duration;
    var m = document.createElement('div');
    m.innerHTML = msg;
    m.style.cssText = " background:#000; opacity:0.6; height:auto;min-height: 30px; color:#fff; line-height:30px; text-align:center; border-radius:4px; position:fixed; top:30%; left:50%; z-index:999999;padding: 4px 10px;";
    document.body.appendChild(m);
    setTimeout(function () {
        var d = 0.5;
        m.style.webkitTransition = '-webkit-transform ' + d + 's ease-in, opacity ' + d + 's ease-in';
        m.style.opacity = '0';
        setTimeout(function () {
            document.body.removeChild(m)
        }, d * 1000);
    }, duration);
}

function get_local_file_info(file_url) {
    var ret_data = '';
    $.ajax({
        url: file_url,//json文件位置
        type: "get",//请求方式为get
        async: false,
        // dataType: 'json',
        success: function (ret) {//请求成功完成后要执行的方法
            // console.log('success');
            // console.log(ret);
            ret_data = ret;
        },
        error: function (XMLHttpResponse, textStatus, errorThrown) {
                    console.log("1 异步调用返回失败,XMLHttpResponse.readyState:"+XMLHttpResponse.readyState);
                    console.log("2 异步调用返回失败,XMLHttpResponse.status:"+XMLHttpResponse.status);
                    console.log("3 异步调用返回失败,textStatus:"+textStatus);
                    console.log("4 异步调用返回失败,errorThrown:"+errorThrown);
                }
    });
    // console.log(ret_data);
    return ret_data;
}


function setCookie(name, value) {
    var Days = 30;
    var exp = new Date();
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
    document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();
}

function getCookie(name) {
    var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
    if (arr = document.cookie.match(reg))
        return unescape(arr[2]);
    else
        return null;
}

function delCookie(name) {
    var exp = new Date();
    exp.setTime(exp.getTime() - 1);
    var cval = getCookie(name);
    if (cval != null)
        document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString();
}

$(document).ready(function () {
        // lang_index();

    });

