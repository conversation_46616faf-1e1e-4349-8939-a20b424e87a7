---
title: Events Demo
subTitle: Events
nav: demos
description: Events usage demo
sort: 7
published: true

tags: 
- demo
- core
---

<div class="owl-carousel owl-theme">
	<div class="item"><h4>1</h4></div>
	<div class="item"><h4>2</h4></div>
	<div class="item"><h4>3</h4></div>
	<div class="item"><h4>4</h4></div>
	<div class="item"><h4>5</h4></div>
	<div class="item"><h4>6</h4></div>
	<div class="item"><h4>7</h4></div>
	<div class="item"><h4>8</h4></div>
	<div class="item"><h4>9</h4></div>
	<div class="item"><h4>10</h4></div>
	<div class="item"><h4>11</h4></div>
	<div class="item"><h4>12</h4></div>
</div>
<hr>
<div class="large-12 columns callbacks">
	<div><span class="label secondary initialize">on</span> initialize.owl.carousel </div>
	<div><span class="label secondary initialized">on</span> initialized.owl.carousel </div>
	<div><span class="label secondary resize">on</span> resize.owl.carousel </div>
	<div><span class="label secondary resized">on</span> resized.owl.carousel </div>
	<div><span class="label secondary refresh">on</span> refresh.owl.carousel </div>
	<div><span class="label secondary refreshed">on</span> refreshed.owl.carousel </div>
	<div><span class="label secondary update">on</span> update.owl.carousel </div>
	<div><span class="label secondary updated">on</span> updated.owl.carousel </div>
	<div><span class="label secondary drag">on</span> drag.owl.carousel </div>
	<div><span class="label secondary dragged">on</span> dragged.owl.carousel </div>
	<div><span class="label secondary translate">on</span> translate.owl.carousel </div>
	<div><span class="label secondary translated">on</span> translated.owl.carousel </div>
	<div><span class="label secondary to">on</span> to.owl.carousel </div>
	<div><span class="label secondary changed">on</span> changed.owl.carousel </div>
</div>


{{#markdown }}
### Overview

Notice that `initialize.owl.carousel` and `initialized.owl.carousel` events must be attached before Owl Carousel initialization. This is required only for those two.

`changed.owl.carousel` event is attached to the main Owl Carousel animation method so that every carousel move triggers this callback.

{{/markdown }} 

<script>
jQuery(document).ready(function($) {

	var owl = $('.owl-carousel');
	
	owl.on('initialize.owl.carousel initialized.owl.carousel ' +
		'initialize.owl.carousel initialize.owl.carousel ' +
		'resize.owl.carousel resized.owl.carousel ' + 
		'refresh.owl.carousel refreshed.owl.carousel ' + 
		'update.owl.carousel updated.owl.carousel ' + 
		'drag.owl.carousel dragged.owl.carousel ' + 
		'translate.owl.carousel translated.owl.carousel ' + 
		'to.owl.carousel changed.owl.carousel', function(e){
		$('.' + e.type)
			.removeClass('secondary')
			.addClass('success');

		window.setTimeout(function(){
			$('.' + e.type)
				.removeClass('success')
				.addClass('secondary');
		},500);
	});

	owl.owlCarousel({
		loop:true,
		nav:true,
		lazyLoad:true,
		margin:10,
		video:true,
		responsive:{
			0:{
				items:1
			},
			600:{
				items:3
			},			
			960:{
				items:5,
			},
			1200:{
				items:6
			}
		}
	});
});

</script>