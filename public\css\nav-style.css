/* 导航栏样式设计（整合优化版） */

/* 字体导入 */
@import url('http://fonts.googleapis.com/css?family=Noto+Serif:400,400italic,700|Open+Sans:400,600,700');
@import url('font-awesome.css');
@import url('animate.css');

/* 1. CSS变量定义 */
:root {
    --nav-primary-color: #055999;
    --nav-text-color: #323232;
    --nav-active-text-color: #ffffff;
    --nav-font-family: "Source Han Sans CN-Regular", "Source Han Sans CN", Arial, sans-serif;
    --nav-height: 120px;
    --scale-factor-percent: calc(100vw / 1920);

}

/* 2. 导航栏基础结构 */
header {
    width: 100vw;
    display: flex;
    justify-content: center;
    background: #ffffff;
}

header .navbar {
    position: relative;
    width: 100%;
    max-width: 100%;
    min-height: var(--nav-height);
    padding: 0;
    margin: 0 auto;
    background: #ffffff;
    height: 120px;
    z-index: 1000;
    display: flex;
    align-items: center;
    border: none;
}

/* 导航栏容器样式 */
.navbar .container {
    position: relative; /* 确保相对定位 */
    height: 100%;
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0 auto;
    display: flex;
    align-items: center;
}

/* 3. Logo 样式 */
.navbar-fullscreen .navbar-brand,
.navbar-responsive .navbar-brand {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: block;
    padding: 0;
    margin: 0;
    z-index: 2;
    height: auto;
}

/* 全屏 left */
.navbar-fullscreen .navbar-brand,
.navbar-responsive .navbar-brand {
    left: 192px;
}

.navbar .navbar-header .navbar-brand img {
    width: 165.7px;
    height: 42.26px;
    object-fit: contain;
    display: block;
    opacity: 1;
    margin: 0;
    padding: 0;
}

/* 4. 导航菜单基础样式 */
.navbar-nav {
    float: none;
    margin: 0;
    padding: 0;
    height: var(--nav-height);
    display: flex;
    align-items: center;
}

.navbar-nav > li {
    display: flex;
    align-items: center;
    height: 100%;
    position: static;
}

/* 5. 导航链接基础样式 */
.navbar .nav > li > a {
    /* 文字样式 */
    font-family: "思源黑体", "Source Han Sans CN", sans-serif;
    font-size: 22px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: normal;
    color: #323232;
    
    /* 布局样式 */
    position: relative;
    text-align: center;
    padding: 0;
    text-decoration: none;
    text-transform: none;
    white-space: nowrap;
    transition: all 0.3s ease;
    border-radius: 4px;
}

/* 6. 导航项定位 */
.navbar .nav > li {
    position: relative;
    display: inline-flex;
    align-items: center;
}

.navbar .nav > li > a {
    max-width: 120px;
    min-width: 80px;
    height: 40px;
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 第一个导航项的位置计算 */
.navbar .nav > li:first-child > a {
    margin-left: calc(192px + 165.7px + 127px); /* logo位置 + logo宽度 + 间距 */
}

/* 其他导航项的间距 */
.navbar .nav > li:not(:first-child) > a {
    margin-left: 64px; /* 严格控制导航项之间的间距 */
}

/* 导航栏容器样式 */
.navbar .nav {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
    height: 100%;
    gap: 0;
}

/* 英文版导航项特殊样式 */
html[lang="en"] .navbar .nav > li > a {
    max-width: none; /* 移除最大宽度限制，使宽度自适应内容 */
    min-width: auto; /* 移除最小宽度限制 */
    width: auto; /* 自适应宽度 */
    white-space: nowrap; /* 确保文本不换行 */
}

/* 英文版导航项间距调整 */
html[lang="en"] .navbar .nav > li:not(:first-child) > a {
    margin-left: 60px; /* 减小导航项之间的间距 */
}

/* 7. 交互状态统一样式 */
.navbar .nav > li > a:hover,
.navbar .nav > li > a:focus,
.navbar .nav > li.active > a:hover,
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus,
.navbar-default .navbar-nav > li.active > a,
.navbar-default .navbar-nav > li.active > a:hover,
.navbar-default .navbar-nav > li.active > a:focus {
    background: transparent !important;
    color: #00509F !important;
    transition: all 0.3s ease;
}

/* 8. 语言切换组件样式 */
/* 移动端语言切换模块默认隐藏 */
.mobile-menu-lang {
    display: none !important;
}

/* 桌面端语言切换器默认显示 */
.navbar .nav > li.lang-switcher-container {
    position: absolute;
    right: 200px;
    top: 50%;
    transform: translateY(-50%);
    height: 40px;
    z-index: 1001;
    display: block;
}

.lang-switcher {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    cursor: pointer;
    width: auto;
    padding: 0;
}

.lang-switcher .lang-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    margin-right: 7px;
}

.lang-switcher .lang-text {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 21px;
    color: #333333;
    line-height: 25px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-right: 7px;
}

.lang-switcher .lang-arrow {
    width: 10px;
    height: 5px;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid var(--nav-text-color);
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.lang-switcher .lang-switch {
    display: none;
    position: absolute;
    top: calc(100% + 5px);
    left: 50%;
    transform: translateX(-50%);
    width: 106px;
    background: #FFFFFF;
    border: 1px solid #707070;
    border-radius: 4px;
    z-index: 10000;
    margin-top: -8px;
}

.lang-switcher .lang-switch .lang-option {
    display: block;
    width: 100%;
    padding: 10px 15px;
    font-size: 21px;
    color: var(--nav-text-color);
    font-family: var(--nav-font-family);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 400;
}

.lang-switcher .lang-switch .lang-option:hover {
    background: transparent;
    color: #00509F;
}

/* 移动端适配保持不变 */
@media (max-width: 768px) {
    .navbar-nav > li.lang-switcher-container,
    .lang-switcher-container {
        display: none !important;
        visibility: hidden !important;
    }
}

/* 10. 响应式布局 - 移动端 */
@media (max-width: 768px) {
    header .navbar {
        min-height: 60px;
    }

    .navbar-default {
        background-color: #fff;
        border: none;
    }

    .navbar .navbar-header {
        display: block;
        height: 60px;
    }

    /* logo紧贴导航栏顶部 */
    .navbar-brand {
        position: absolute !important;
        left: 2% !important;
        top: 15px !important;
        transform: none !important;
        margin: 0;
        padding-top: 10px;
    }

    .navbar-brand img {
        width: 124px !important;
        height: auto !important;
        display: block;
    }

    /* 展开的导航栏样式 */
    .navbar-collapse {
        position: absolute;
        top: 60px;
        left: 0;
        right: 0;
        width: 100vw;
        max-width: 100vw;
        background: #fff;
        border-top: 1px solid #e7e7e7;
        min-height: 270px;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        transform: translateX(0);
        z-index: 1000;
        display: none;
    }
    
    /* 确保折叠菜单展开时显示 */
    .navbar-collapse.in,
    .navbar-collapse.collapsing {
        display: block !important;
    }

    /* 移动端导航项容器样式 */
    .navbar-collapse .navbar-nav {
        margin: 0;
        padding: 10px 0;
        width: 100%;
        float: none !important;
        display: block;
        height: auto;
    }

    .navbar-collapse .navbar-nav > li {
        margin: 0;
        padding: 0;
        width: 100%;
        display: block;
        height: auto;
        position: relative;
    }

    /* 移动端导航项样式 */
    .navbar-collapse .navbar-nav > li > a {
        position: relative;
        left: 5%;
        width: 90% !important;
        padding: 12px 15px !important;
        margin: 5px 0;
        background: transparent;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: visible;
        border-radius: 4px;
        display: block;
        text-align: left;
        font-size: 18px;
    }

    /* 移动端导航项hover状态 */
    .navbar-collapse .navbar-nav > li > a:hover,
    .navbar-collapse .navbar-nav > li > a:focus,
    .navbar-collapse .navbar-nav > li > a.active {
        background: transparent;
        color: #00509F;
        width: 90% !important;
    }

    /* 移动端导航项间距 */
    .navbar-collapse .navbar-nav > li:not(:first-child) > a {
        margin-left: 0;
    }

    /* 显示移动端语言切换模块 */
    .mobile-menu-lang {
        display: block !important;
        position: relative !important;
        left: 0 !important;
        width: 100% !important;
        background: transparent;
        margin-top: 10px;
    }

    /* 移动端语言切换模块样式 */
    .mobile-menu-lang a {
        display: flex !important;
        align-items: center;
        padding: 15px 2% !important;
        font-family: var(--nav-font-family);
        font-size: 20px;
        color: var(--nav-text-color) !important;
        text-decoration: none;
        background: transparent !important;
        width: 100% !important;
        height: 40px !important;
        line-height: 40px !important;
    }

    /* 移动端语言切换模块的hover和active状态 */
    .mobile-menu-lang a:hover,
    .mobile-menu-lang a:focus,
    .mobile-menu-lang a:active {
        background-color: transparent !important;
        color: #00509F !important;
    }

    .mobile-menu-lang .globe-icon {
        margin-right: 8px;
        font-size: 24px;
        display: inline-block;
        vertical-align: middle;
    }

    .mobile-menu-lang .lang-text {
        font-family: var(--nav-font-family);
        font-weight: 400;
        color: var(--nav-text-color);
    }

    /* 隐藏桌面端语言切换器 */
    .navbar-nav > li.lang-switcher-container,
    .lang-switcher-container {
        display: none !important;
        visibility: hidden !important;
    }

    /* 调整导航栏最小高度以适应新的布局 */
    .navbar-collapse {
        min-height: 260px;  /* 确保能容纳所有项目 */
    }

    /* 重置第一个导航项的margin-left，确保它在移动端正确显示 */
    .navbar .nav > li:first-child > a {
        margin-left: 0;
    }
}

/* 11. 移动端菜单按钮 */
.navbar-toggle {
    position: absolute;
    right: 2%;
    top: 35px;
    transform: translateY(-50%);
    padding: 9px 10px;
    margin: 0;
    background-color: transparent;
    border: 0;
    border-radius: 4px;
}

.navbar-default .navbar-toggle .icon-bar {
    background-color: var(--nav-primary-color);
    border-radius: 0;
}

.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
    background-color: transparent;
}

/* 添加菜单展开收起的过渡动画 */
.navbar-collapse {
    transition: height 0.3s ease;
}

/* 当菜单打开时禁止body滚动 */
body.menu-open {
    overflow: hidden;
}

/* 非移动端响应式布局 */
@media (min-width: 769px) {
    /* 确保移动端语言切换模块隐藏 */
    .mobile-menu-lang {
        display: none !important;
        visibility: hidden !important;
    }

}

/* 针对微信浏览器的特殊处理 */
@media screen and (min-width: 320px) and (max-width: 768px), 
       screen and (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
    /* 确保移动端显示，桌面端隐藏 */
    .mobile-menu-lang {
        display: block !important;
        visibility: visible !important;
    }
    
    .navbar-nav > li.lang-switcher-container,
    .lang-switcher-container {
        display: none !important;
        visibility: hidden !important;
    }
}


/* 确保激活状态的导航项也使用新的颜色 */
.navbar-default .navbar-nav > li.active > a {
    color: #00509F !important;
    background: transparent !important;
} 

/* 产品导航弹窗样式 */
.product-popup {
    display: none;
    position: absolute;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    width: 1400px;
    height: auto;
    min-height: 477px;
    background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #0050A2;
    z-index: 1001;
    padding: 40px;
    box-sizing: border-box;
}

/* 表格布局 */
.product-popup-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: auto 1fr;
    gap: 42px;
    row-gap: 20px;
    width: 100%;
    height: 100%;
}

/* 标题行样式 */
.product-popup-title {
    width: 185px;
    height: 42px;
    background: #EFEFEF;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-popup-title span {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #646464;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    text-transform: none;
}

.product-popup-title:hover {
    background: #007DDB;
}

.product-popup-title:hover span {
    color: #FFFFFF;
}

/* 内容区域样式 */
.product-popup-content {
    width: 185px;
    height: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.product-popup-content a {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #646464;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-popup-content a:hover {
    color: #0050A2;
    text-decoration: none;
}

/* 行与行之间的间距 */
.product-popup-grid .row-gap {
    height: 20px;
}

/* 移动端适配 */
@media (max-width: 1440px) {
    .product-popup {
        width: 95%;
        max-width: 1400px;
    }
}

@media (max-width: 768px) {
    .product-popup {
        display: none !important; /* 移动端不显示弹窗 */
    }
} 