---
title: Merge Demo
subTitle: Merge
nav: demos
description: Merge Items
sort: 5

tags: 
- demo
- core
---

<div class="owl-carousel owl-theme">
	<div class="item" data-merge="1"><h4>1</h4></div>
	<div class="item" data-merge="2"><h4>2</h4></div>
	<div class="item" data-merge="1"><h4>3</h4></div>
	<div class="item" data-merge="3"><h4>4</h4></div>
	<div class="item" data-merge="6"><h4>6</h4></div>
	<div class="item" data-merge="2"><h4>7</h4></div>
	<div class="item" data-merge="1"><h4>8</h4></div>
	<div class="item" data-merge="3"><h4>9</h4></div>
	<div class="item"><h4>10</h4></div>
	<div class="item"><h4>11</h4></div>
	<div class="item" data-merge="2"><h4>12</h4></div>
	<div class="item"><h4>13</h4></div>
	<div class="item"><h4>14</h4></div>
	<div class="item"><h4>15</h4></div>
</div>

{{#markdown }}
### Overview
Merge option requires `data-merge="number_items_to_merge"` on any child element (can be nested as well). There is a sibling option called `mergeFit` which fits merged elements to screen size. 

See item 6 on breakpoint below and above `1000px` screen width.

### Setup
```
$('.owl-carousel').owlCarousel({
	items:5,
	loop:true,
	margin:10,
	merge:true,
	responsive:{
		678:{
			mergeFit:true
		},
		1000:{
			mergeFit:false
		}
	}
});
```
### html
```
<div class="owl-carousel owl-theme">
	<div class="item" data-merge="1"><h2>1</h2></div>
	<div class="item" data-merge="2"><h2>2</h2></div>
	<div class="item" data-merge="1"><h2>3</h2></div>
	<div class="item" data-merge="3"><h2>4</h2></div>
	<div class="item" data-merge="6"><h2>6</h2></div>
	<div class="item" data-merge="2"><h2>7</h2></div>
	<div class="item" data-merge="1"><h2>8</h2></div>
	<div class="item" data-merge="3"><h2>9</h2></div>
	<div class="item"><h2>10</h2></div>
	<div class="item"><h2>11</h2></div>
	<div class="item" data-merge="2"><h2>12</h2></div>
	<div class="item"><h2>13</h2></div>
	<div class="item"><h2>14</h2></div>
	<div class="item"><h2>15</h2></div>
</div>
```

{{/markdown }} 

<script>
$(document).ready(function(){
	$('.owl-carousel').owlCarousel({
		items:5,
		loop:true,
		margin:10,
		merge:true,
		responsive:{
			678:{
				mergeFit:true
			},
			1000:{
				mergeFit:false
			}
		}
	});
})
</script>