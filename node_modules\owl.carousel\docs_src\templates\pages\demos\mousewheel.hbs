---
title: Mousewheel Demo
subTitle: Mousewheel
nav: demos
description: Mouse<PERSON> usage demo
sort: 1

tags: 
- demo
- external
---

<div class="owl-carousel owl-theme">
	<div class="item"><h4>1</h4></div>
	<div class="item"><h4>2</h4></div>
	<div class="item"><h4>3</h4></div>
	<div class="item"><h4>4</h4></div>
	<div class="item"><h4>5</h4></div>
	<div class="item"><h4>6</h4></div>
	<div class="item"><h4>7</h4></div>
	<div class="item"><h4>8</h4></div>
	<div class="item"><h4>9</h4></div>
	<div class="item"><h4>10</h4></div>
	<div class="item"><h4>11</h4></div>
	<div class="item"><h4>12</h4></div>
</div>

{{#markdown }}
### Overview

To add mouswheel scrolling just include the fantastic plugin jquery.mousewheel.js created by <PERSON>. 
[Link to plugin GitHub page](https://github.com/brandonaaron/jquery-mousewheel)



### Setup
```
var owl = $('.owl-carousel');

owl.owlCarousel({
	loop:true,
	nav:true,
	margin:10,
	responsive:{
		0:{
			items:1
		},
		600:{
			items:3
		},			
		960:{
			items:5
		},
		1200:{
			items:6
		}
	}
});

owl.on('mousewheel', '.owl-stage', function (e) {
    if (e.deltaY>0) {
		owl.trigger('next.owl');
    } else {
		owl.trigger('prev.owl');
    }
    e.preventDefault();
});

```

{{/markdown }} 

<script src="{{assets}}/vendors/jquery.mousewheel.min.js"></script>
<script>
$(document).ready(function(){
	var owl = $('.owl-carousel');

	owl.owlCarousel({
		loop:true,
		nav:true,
		margin:10,
		responsive:{
			0:{
				items:1
			},
			600:{
				items:3
			},			
			960:{
				items:5
			},
			1200:{
				items:6
			}
		}
	});

	owl.on('mousewheel', '.owl-stage', function (e) {
	    if (e.deltaY>0) {
			owl.trigger('next.owl');
	    } else {
			owl.trigger('prev.owl');
	    }
	    e.preventDefault();
	});
})


</script>