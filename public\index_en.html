<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Xiamen Bearkey Technology Co., Ltd. - Official Website</title>
    <meta name="keywords"
          Content="Xiamen Bearkey Technology,Bearkey,Cloud,Smart Meeting System,Wireless Screen Casting,AI,Industrial Motherboard,Face Recognition,Smart Vision,Audio Module,IoT,Open Source Board,Story Machine,AIoT,Audio Video Processing,RK3399Pro,RK1808,Edge Computing">
    <link rel="icon" href="./images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description"
          content="Xiamen Bearkey Technology,Bearkey,Cloud,Smart Meeting System,Wireless Screen Casting,AI,Industrial Motherboard,Face Recognition,Smart Vision,Audio Module,IoT,Open Source Board,Story Machine,AIoT,Audio Video Processing,RK3399Pro,RK1808,Edge Computing"/>
    <meta name="author" content=""/>
    <!-- 语言标识 -->
    <meta name="language" content="en">
    <!-- css -->
    <link href="./css/bootstrap.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="./simple-line-icons/css/simple-line-icons.css">
    <link href="./css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="./css/flexslider.css" rel="stylesheet"/>
    <link href="./js/owl-carousel/owl.carousel.css" rel="stylesheet">
    <link href="./css/style.css" rel="stylesheet"/>
    <link href="./css/footer.css" rel="stylesheet"/>
    <link href="./css/product_recommend.css" rel="stylesheet"/>
    <link href="./css/hot_products.css" rel="stylesheet"/>
    <link href="./css/solution_customize.css" rel="stylesheet"/>
    <link href="./css/others.css" rel="stylesheet"/>
    <!--轮播样式-->
    <link rel="stylesheet" href="./css/ft-carousel.css"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <style>
        /* 轮播图样式 */
        .owl-carousel .item img {
            width: 100%;
            height: auto;
            max-height: 500px;
            object-fit: cover;
        }
        
        /* 轮播导航按钮样式 */
        .owl-nav button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.5) !important;
            color: #000 !important;
            width: 40px;
            height: 40px;
            border-radius: 50% !important;
            font-size: 20px !important;
            line-height: 1 !important;
        }
        
        .owl-nav button:hover {
            background: rgba(255, 255, 255, 0.8) !important;
        }
        
        .owl-nav .owl-prev {
            left: 10px;
        }
        
        .owl-nav .owl-next {
            right: 10px;
        }
        
        /* 轮播点导航样式 */
        .owl-dots {
            position: absolute;
            bottom: 10px;
            left: 0;
            right: 0;
            text-align: center;
        }
        
        .owl-dots .owl-dot span {
            width: 10px;
            height: 10px;
            margin: 5px;
            background: rgba(255, 255, 255, 0.5);
            display: block;
            border-radius: 50%;
        }
        
        .owl-dots .owl-dot.active span {
            background: #fff;
        }
    </style>

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>


</head>
<body style="overflow-x:hidden" class="english-version index_en">
<div id="wrapper" class="home-page" style="display: none">
    <!-- start header -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header>

    <!-- 首页轮播 -->
    <section class="carousel-bg">
        <div class="carousel-container">
            <!-- 轮播内容将由 JavaScript 动态加载 -->
            <div class="carousel-indicators">
                <!-- 轮播指示器将由 JavaScript 动态生成 -->
            </div>
            <div class="carousel-arrows">
                <div class="carousel-arrow-container carousel-arrow-left">
                    <div class="carousel-arrow-btn"></div>
                </div>
                <div class="carousel-arrow-container carousel-arrow-right">
                    <div class="carousel-arrow-btn"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- 产品推荐区域 -->
    <div class="product-recommend">
        <div class="container">
            <h2 class="section-title">Product Series</h2>
            <div class="product-grid">
                <!-- 核心板 -->
                <div class="product-item">
                    <div class="product-image-placeholder"></div>
                    <h3 class="product-title">
                        <a href="/">Core Board</a>
                    </h3>
                </div>
                <!-- 主板 -->
                <div class="product-item">
                    <div class="product-image-placeholder"></div>
                    <h3 class="product-title">
                        <a href="/">Motherboard</a>
                    </h3>
                </div>
                <!-- 终端 -->
                <div class="product-item">
                    <div class="product-image-placeholder"></div>
                    <h3 class="product-title">
                        <a href="/">Terminal</a>
                    </h3>
                </div>
                <!-- AIOT解决方案 -->
                <div class="product-item">
                    <div class="product-image-placeholder"></div>
                    <h3 class="product-title">
                        <a href="/">AIOT Solutions</a>
                    </h3>
                </div>
                <!-- OpenHarmony -->
                <div class="product-item">
                    <div class="product-image-placeholder"></div>
                    <h3 class="product-title">
                        <a href="/">OpenHarmony</a>
                    </h3>
                </div>
                <!-- MineHarmony -->
                <div class="product-item">
                    <div class="product-image-placeholder"></div>
                    <h3 class="product-title">
                        <a href="/">MineHarmony</a>
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- 热门产品区域 -->
    <div class="hot-products">
        <div class="hot-products-side-box left">
            <div class="hot-products-arrow left"></div>
        </div>
        <div class="hot-products-side-box right">
            <div class="hot-products-arrow right"></div>
        </div>
        <div class="container">
            <h2 class="hot-products-title">Hot Products</h2>
            <div class="hot-products-grid">
                <!-- 热门产品内容将由JavaScript动态加载 -->
            </div>
            <div class="hot-products-indicators">
                <!-- 指示器将由JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 方案定制区域 -->
    <section class="solution-customize">
        <h2 class="solution-customize-title">Solution Customization</h2>
        <div class="solution-customize-content">
            <img src="./images/home/<USER>" alt="Solution Customization" class="solution-customize-image">
            <div class="solution-customize-info">
                <p class="solution-customize-text">Comprehensive customization services, supporting hardware customization, software customization, complete machine customization, solution customization..........</p>
                <a href="#" class="customize-button"><span>Customize Now</span></a>
            </div>
        </div>
    </section>

    <!-- 其他模块 -->
    <section class="others-section">
        <div class="others-container">
            <!-- 贝启科技动态 -->
            <div class="news-section">
                <div class="news-header">
                    <h3 class="news-title">News</h3>
                    <a href="#" class="more-news">More News</a>
                </div>
                <div class="news-list">
                    <!-- 动态内容将由 JavaScript 加载 -->
                </div>
            </div>
            
            <!-- 关于贝启科技 -->
            <div class="about-section">
                <div class="about-left">
                    <div class="about-header">
                        <h3 class="about-title">About Us</h3>
                        <a href="#" class="learn-more">Learn More</a>
                    </div>
                    <div class="about-content">
                        <p class="about-text">Xiamen Bearkey Technology Co., Ltd. was established in 2015. The company brings together a team of industry veterans led by doctors and composed of ARM R&D team from Star-Net and members from Nanjing University of Aeronautics and Astronautics, who are experienced Rockchip solution application experts, dedicated to the development, verification, production, sales and service of military-to-civilian software. The company's customized products and solutions are widely applied in various industries, including drones, industrial IoT, robotics, smart vision, smart security, smart communities, marine applications, multimedia, commercial display, automotive, education, healthcare, and more. Currently, products based on the OpenHarmony system have been put into mass production, and have accumulated reliable and extensive commercial and industry empowerment capabilities.</p>
                        <img src="./images/home/<USER>" alt="About Bearkey Technology" class="about-text-image">
                    </div>
                </div>
                <div class="about-right">
                    <div class="about-image-container">
                        <img src="./images/home/<USER>" alt="Bearkey Technology" class="about-image">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <!-- 页脚内容将通过footer.js动态加载 -->
    </footer>
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>
<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="js/jquery.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.fancybox.pack.js"></script>
<script src="js/jquery.fancybox-media.js"></script>
<script src="js/portfolio/jquery.quicksand.js"></script>
<script src="js/portfolio/setting.js"></script>
<script src="js/jquery.flexslider.js"></script>
<script src="js/animate.js"></script>
<script src="js/custom.js"></script>
<script src="js/owl-carousel/owl.carousel.min.js"></script>
<script src="js/tools.js"></script>
<script src="js/ft-carousel.min.js"></script>
<script src="js/others.js"></script>
<script src="js/main.js"></script>
<script src="js/nav.js"></script>
<script src="js/floating-toolbar.js"></script>
<script src="js/toolbar-data.js"></script>
<script>
    // 页面加载完成后初始化所有功能
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化轮播图
        initCarousel();
        
        // 初始化热门产品
        fetchHotProducts();
        
        // 初始化关于贝启科技
        fetchAboutCompany();
        
        // 注册导航栏加载完成事件监听器
        document.addEventListener('navbarLoaded', function() {
            console.log('Navigation bar loaded, reinitializing page data');
            // 重新获取数据以确保所有内容都已正确加载
            fetchAboutCompany();
        });
    });
</script>
<script src="js/footer.js"></script>
</body>
</html> 