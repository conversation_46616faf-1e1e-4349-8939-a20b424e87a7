/**
 * 合作伙伴前端显示脚本
 * 根据数据库中的合作伙伴信息动态显示合作伙伴列表
 */

// 判断当前是否为英文网站
function isEnglishSite() {
    // 检查URL中是否包含_en.html或其他英文标识
    var isEnglish = window.location.pathname.includes('_en.html') || 
                   window.location.pathname.includes('/en/') ||
                   window.location.hostname.includes('en.') || 
                   window.location.search.includes('lang=en');
    
    console.log("页面类型检测:", {
        "URL路径": window.location.pathname,
        "是否包含_en.html": window.location.pathname.includes('_en.html'),
        "是否包含/en/": window.location.pathname.includes('/en/'),
        "是否为英文网站": isEnglish
    });
    
    return isEnglish;
}

// 获取合作伙伴列表
function getPartners() {
    console.log("开始获取合作伙伴数据...");
    
    // 清空旧的合作伙伴内容
    var partnerContainer = document.getElementById('partner');
    if (partnerContainer) {
        console.log("清空合作伙伴容器");
        partnerContainer.innerHTML = '';
    } else {
        console.error("未找到合作伙伴容器");
        return;
    }
    
    // 发送请求获取合作伙伴列表
    $.ajax({
        type: "post",
        url: "/apis/get_partner_pic/",
        async: true,
        data: {
            page: 1,
            page_size: 100, // 获取足够多的记录
            filters: JSON.stringify({show: true}) // 只获取需要显示的
        },
        success: function(data) {
            console.log("获取合作伙伴响应:", data);
            
            if (data.status === 'ok' && data.data_list && data.data_list.length > 0) {
                console.log("成功获取合作伙伴，数量:", data.data_list.length);
                
                // 渲染合作伙伴
                renderPartners(data.data_list);
            } else {
                console.error("获取合作伙伴失败:", data);
                // 保留原有的静态合作伙伴内容
                console.log("使用默认的静态合作伙伴内容");
            }
        },
        error: function(xhr, status, error) {
            console.error("获取合作伙伴请求失败:", status, error);
            // 保留原有的静态合作伙伴内容
            console.log("使用默认的静态合作伙伴内容");
        }
    });
}

// 渲染合作伙伴
function renderPartners(partners) {
    // 获取合作伙伴容器
    var container = document.getElementById('partner');
    if (!container) {
        console.error("未找到合作伙伴容器");
        return;
    }
    
    console.log("渲染合作伙伴数据...");
    
    // 清空容器
    container.innerHTML = '';
    
    // 按照pic_idx排序
    partners.sort(function(a, b) {
        return (a.pic_idx || 0) - (b.pic_idx || 0);
    });
    
    // 创建合作伙伴HTML
    partners.forEach(function(partner) {
        console.log("渲染合作伙伴:", {
            id: partner.id,
            url: partner.url,
            pic_idx: partner.pic_idx
        });
        
        var partnerDiv = document.createElement('div');
        partnerDiv.style.float = 'left';
        
        if (partner.url) {
            var link = document.createElement('a');
            link.href = partner.url;
            link.target = '_blank';
            
            var img = document.createElement('img');
            img.src = partner.pic;
            img.alt = '合作伙伴';
            
            link.appendChild(img);
            partnerDiv.appendChild(link);
        } else {
            var img = document.createElement('img');
            img.src = partner.pic;
            img.alt = '合作伙伴';
            
            partnerDiv.appendChild(img);
        }
        
        container.appendChild(partnerDiv);
    });
    
    console.log("合作伙伴渲染完成");
}

// 页面加载完成后获取合作伙伴
$(document).ready(function() {
    console.log("页面加载完成，开始获取合作伙伴");
    
    // 确保只执行一次
    if (window.partnersInitialized) {
        console.log("合作伙伴已初始化，跳过");
        return;
    }
    
    window.partnersInitialized = true;
    
    // 获取新的合作伙伴
    getPartners();
}); 