/* 只在body上添加overflow-x: hidden */
body {
    overflow-x: hidden;
}

/* 修改development-concept,只处理水平溢出 */
.development-concept {
    width: 100%;
    max-width: 1912px;
    height: 1468px;
    background: #F8F8F8;
    border-radius: 0px 0px 0px 0px;
    padding: 42.5px 0 56px 0;
    text-align: center;
    margin: 0 auto;
}

/* 修改company-culture,只处理水平溢出 */
.company-culture {
    width: 100%;
    padding: 42.5px 0 80px 0;
    margin: 0;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 页面标题背景板 */
.page-title-banner {
    width: 100%;
    height: 6.25vw; /* 120px */
    background: #F8F8F8;
    border-radius: 0;
    display: flex;
    align-items: center;
    margin-bottom: 2.5vw; /* 48px */
}

.page-title {
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 500;
    font-size: 2.29vw; /* 44px */
    color: #00509E;
    line-height: 4.43vw; /* 85px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0;
}

/* 面包屑导航样式 */
.breadcrumb {
    margin: 0 0; /* 20px */
    padding: 0;
    background: none;
    display: flex;
    align-items: center;
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 0.83vw; /* 16px */
    line-height: 1.51vw; /* 29px */
    font-style: normal;
    text-transform: none;
}

.breadcrumb a {
    color: #333333;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #055999;
}

.breadcrumb .separator {
    margin: 0 0.42vw; /* 8px */
    color: #333333;
}

.breadcrumb .current {
    color: #333333;
}

/* 内容区域样式 */
.about-content {
    padding: 0 0; /* 40px */
    max-width: 83.33vw; /* 1600px */
    margin: 0 auto;
    position: relative;
}

/* 新的简介背景图片样式 */
.about-background-image {
    width: 100%;
    margin-top: 51px; /* 面包屑导航下方间距 */
    display: flex;
    justify-content: center;
}

.about-bg-img {
    width: 1423px;
    height: 1067px;
    display: block;
}

/* 新的简介文本框样式 */
.about-text-box {
    width: 1028px;
    height: 609px;
    background: #FFFFFF;
    border-radius: 28px;
    border: 1px solid #0062BA;
    position: absolute;
    top: 590px; /* 距离图片顶部的位置 */
    left: 50%;
    transform: translateX(-50%);
    padding: 0px 38px 0px 38px;
    box-sizing: border-box;
}

.about-text-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #323232;
    line-height: 85px;
    text-align: center; /* 将文本对齐方式从left改为center */
    font-style: normal;
    text-transform: none;
    margin-top: 0;
    margin-bottom: 0px;
}

.about-text-content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 18px;
    color: #323232;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    overflow-y: auto;
}



/* 发展理念板块样式 */
.development-concept {
    width: 100%;
    max-width: 1912px;
    height: 1468px;
    background: #F8F8F8;
    border-radius: 0px 0px 0px 0px;
    padding: 42.5px 0 56px 0;
    text-align: center;
    margin: 0 auto;
}

.concept-title {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 40px;
    color: #323232;
    line-height: 1;
    text-align: center;
    margin-bottom: 50px;
    padding: 0;
    display: block;
}

.development-wrapper {
    width: 100%;
    margin: 0 auto;
    padding: 0;
    display: flex;
    justify-content: flex-start;
    padding-left: 195px;
}

.development-image {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 1455px;
    height: 1317px;
    margin: 0;
    padding: 0;
    position: relative;
}

.development-image img {
    display: block;
    margin: 0;
    padding: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    object-fit: contain;
}

/* 企业文化板块样式 */
.company-culture {
    width: 100%;
    padding: 42.5px 0 80px 0;
    margin: 0;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.culture-title {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 40px;
    color: #323232;
    line-height: 1;
    text-align: center;
    margin-bottom: 39px;
    padding: 0;
}

.culture-content {
    width: 1605px;
    margin: 0;
    padding-left: 315px;
    display: grid;
    grid-template-columns: 667px 938px;
    grid-template-rows: auto auto;
    row-gap: 29px;
    column-gap: 0;
    justify-self: start;
    align-self: start;
}

.culture-wrapper {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: flex-start;
}

.culture-item {
    display: flex;
    align-items: flex-start;
}

.culture-icon {
    width: 50px;
    height: 50px;
    margin-right: 17px;
    flex-shrink: 0;
    margin-top: 19px;
}

.culture-icon-img {
    width: 50px;
    height: 50px;
    display: block;
}

.culture-text-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    margin-top: 0;
}

.culture-item-title {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 32px;
    color: #0050A2;
    line-height: 46px;
    font-style: normal;
    text-transform: none;
    margin-bottom: 6px;
    margin-top: 0;
    padding-top: 0;
}

.culture-item-text {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 20px;
    color: #323232;
    line-height: 29px;
    font-style: normal;
    text-transform: none;
    margin: 0;
}

/* 发展理念和荣誉的文本图片 */
.concept-text-image,
.honor-text-image {
    display: none;
    width: 100%;
    height: auto;
    border-radius: 0.416667vw; /* 8px */
    object-fit: contain;
}

/* 荣誉部分 */
.honor-section {
    width: 100%;
    max-width: 1912px;
    height: 701px;
    background: #F8F8F8;
    border-radius: 0;
    padding: 0;
    text-align: center;
    margin: 0 auto;
    margin-top: 189px;
    position: relative;
    box-sizing: border-box;
}

.honor-title {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 40px;
    color: #323232;
    line-height: 1;
    text-align: center;
    margin: 0;
    position: absolute;
    top: 52.5px; /* (701px - 500px - 56px) ÷ 2 = 72.5px */
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
}

.honor-carousel {
    width: 100%;
    max-width: 1400px;
    height: 500px;
    background: #FFFFFF;
    border-radius: 28px;
    border: 1px solid #ABABAB;
    margin: 0 auto;
    overflow: hidden;
    position: absolute;
    bottom: 56px;
    left: 50%;
    transform: translateX(-50%);
    box-sizing: border-box;
    padding: 29px 15px 30px 28px;
}

/* 荣誉胶卷样式 */
.honor-film-strip {
    height: 100%;
    display: flex;
    align-items: center;
    transition: transform 0.1s linear;
    will-change: transform; /* 优化动画性能 */
}

.honor-image-container {
    height: 90%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.honor-image-container img {
    height: 100%;
    object-fit: contain;
}

footer {
    clear: both;
    width: 100%;
    float: left;
}

/* 合作伙伴部分 - 完全消除所有间距 */
.partner-section {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 42.5px 0 56px 0;
    text-align: center;
    font-size: 0; /* 消除内联元素间的空白 */
    line-height: 0; /* 消除行高导致的空白 */
}

.partner-title {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 40px;
    color: #323232;
    line-height: 1;
    text-align: center;
    padding: 0;
    display: block; /* 确保块级显示 */
    margin-bottom: 52.5px;
}

.partner-content {
    width: 100%;
    margin: 0;
    padding: 0;
    font-size: 0; /* 消除内联元素间的空白 */
    line-height: 0; /* 消除行高导致的空白 */
}




/* 小屏幕样式 */
@media screen and (max-width: 491px) {
    .about-text {
        height: auto; /* 高度自适应内容 */
        font-size: 8px !important; /* 设置字体大小为8px */
        line-height: 1; /* 设置基础行高 */
    }

    /* 英文界面文字样式 */
    html[lang="en"] .about-text {
        line-height: 8px !important;
        height: auto !important;
        white-space: normal !important;
        overflow: visible !important;
    }

    /* 发展理念背景板和标题间距调整 */
    .development-concept {
        padding: 0.89844vw 20vw 4.583333vw 20vw; /* 上内边距减为1/4: 69px->17.25px */
    }
    
    /* 英文界面发展理念背景板和标题间距调整为0 */
    html[lang="en"] .development-concept {
        padding: 0 20vw 4.583333vw 20vw !important; /* 上内边距设为0 */
    }
    html[lang="en"] .concept-title {
        line-height: 5px !important;
        margin-top: 10px;
    }
     /* 发展理念板块样式 */
    html[lang="en"] .development-concept {
        height: 30.052vw !important;
}
    html[lang="en"] .honor-title {
        line-height: 10px !important;
        margin-top: 8px;
    }
    /* 英文界面荣誉背景板和标题间距调整为0 */
    html[lang="en"] .honor-section {
        padding: 0 17.552083vw 4.0625vw 17.552083vw; /* 上内边距设为0 */
        height:  22.135vw !important;
    }
    
    /* 英文界面下图片容器高度调整 */
    html[lang="en"] .concept-mobile-image,
    html[lang="en"] .honor-mobile-image {
        height: 90%;
    }
    
    html[lang="en"] .concept-mobile-image img,
    html[lang="en"] .honor-mobile-image img {
        height: 100%;
    }
    html[lang="en"] .honor-mobile-image img{
        margin-left: -60px;
    }*/
    
    /* 发展理念内容居中显示 */
    .concept-row {
        width: 100%; /* 使用100%宽度 */
        margin: 0 auto; /* 水平居中 */
        justify-content: center; /* 水平居中对齐 */
    }
    
    /* 发展理念文字样式 - 只缩小文字，保持外框不变 */
    .concept-label, .concept-content {
        display: flex;
        align-items: center; /* 垂直居中 */
        justify-content: flex-start; /* 水平左对齐 */
        padding: 0 0 0 10px; /* 添加左内边距 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 0; /* 确保flex子元素可以缩小 */
    }
    
    /* 只针对文字应用缩放样式 */
    .concept-text {
        font-size: 8px !important;
        transform: scale(0.66);
        transform-origin: left center; /* 从左侧中心开始变换 */
        line-height: 1;
        width: 100%;
        text-align: center;
        white-space: nowrap; /* 防止文本换行 */
        display: block; /* 确保占据整个宽度 */
        overflow: visible; /* 允许内容溢出 */
        text-overflow: ellipsis; /* 文本溢出显示省略号 */
        max-width: 100%; /* 限制最大宽度 */
        margin-left: -5px; /* 向左偏移一点，抵消transform产生的空白 */
    }
    
    /* 荣誉背景板和标题间距调整 */
    .honor-section {
        padding: 0.833333vw 17.552083vw 4.0625vw 17.552083vw; /* 上内边距减为1/4: 64px->16px */
    }
    
    /* 荣誉项文字样式 - 只缩小文字，保持外框不变 */
    .honor-item {
        display: flex;
        text-align: center;
        align-items: center; /* 垂直居中 */
        justify-content: flex-start; /* 水平左对齐 */
        padding: 0 0 0 10px; /* 添加左内边距 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 0; /* 确保flex子元素可以缩小 */
    }
    
    /* 荣誉项文字样式 */
    .honor-item span {
        font-size: 8px !important;
        transform: scale(0.66);
        transform-origin: left center; /* 从左侧中心开始变换 */
        line-height: 1;
        width: 100%;
        text-align: center;
        white-space: nowrap; /* 防止文本换行 */
        display: block; /* 确保占据整个宽度 */
        overflow: visible; /* 允许内容溢出 */
        text-overflow: ellipsis; /* 文本溢出显示省略号 */
        max-width: 100%; /* 限制最大宽度 */
        margin-left: -5px; /* 向左偏移一点，抵消transform产生的空白 */
    }
    
    /* 合作伙伴背景板和标题间距调整 */
    .partner-section {
        padding: 0.89844vw 0 3.802083vw 0; /* 上内边距减为1/4: 69px->17.25px */
    }

    /* 英文版下的特殊处理 - 隐藏文本，显示图片 */
    html[lang="en"] .concept-items,
    html[lang="en"] .honor-list {
        display: none !important; /* 使用!important确保隐藏 */
    }

    html[lang="en"] .concept-mobile-image,
    html[lang="en"] .honor-mobile-image {
        display: block;
    }

    /* 英文发展理念文字容器 */
    html[lang="en"] .concept-label-en,
    html[lang="en"] .concept-content-en {
        position: relative;
        color: transparent;
        font-size: 0;
        overflow: visible;
        margin-left: -62px; /* 与荣誉部分保持一致 */
        box-sizing: border-box;
        width: auto;
        display: inline-block;
    }

    /* 英文发展理念标签（左列） */
    html[lang="en"] .concept-label-en {
        width: auto;
        min-width: 50px; /* 与荣誉左列保持一致 */
        margin-right: 67px; /* 添加右侧间距 */
        
    }

    /* 英文发展理念内容（右列） */
    html[lang="en"] .concept-content-en {
        width: auto;
        min-width: 240px;
    }

    /* 英文发展理念文字内容 */
    html[lang="en"] .concept-label-en::before,
    html[lang="en"] .concept-content-en::before {
        content: attr(data-text);
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) scale(0.55);
        transform-origin: left center;
        width: auto;
        font-size: 5px !important;
        white-space: nowrap;
        text-align: left;
        color: #323232;
        line-height: 1;
    }

    /* 内容部分文字特殊处理 */
    html[lang="en"] .concept-content-en::before {
        width: max-content;
        max-width: none;
    }

    /* 英文荣誉项容器 */
    html[lang="en"] .honor-item-en {
        position: relative;
        color: transparent;
        font-size: 0;
        overflow: visible;
        margin-left: -50px; 
        box-sizing: border-box;
        width: auto;
        display: inline-block;
    }

    /* 荣誉左列项 */
    html[lang="en"] .honor-item-en:nth-child(1) {
        width:auto;
        min-width: 57px;
    }

    /* 荣誉中列项 */
    html[lang="en"] .honor-item-en:nth-child(2) {
        width: 30%;
        min-width: 100px;
    }

    /* 荣誉右列项 */
    html[lang="en"] .honor-item-en:nth-child(3) {
        width: auto;
        min-width: 160px;
    }

    /* 英文荣誉项内容 */
    html[lang="en"] .honor-item-en::before {
        content: attr(data-text);
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) scale(0.55);
        transform-origin: left center;
        width: auto;
        font-size: 5px !important;
        white-space: nowrap;
        text-align: left;
        color: #323232;
        line-height: 1;
        max-width: none;
        width: max-content;
    }

    /* 删除图片相关样式 */
    html[lang="en"] .concept-items,
    html[lang="en"] .honor-list {
        display: flex !important;
    }

    html[lang="en"] .concept-mobile-image,
    html[lang="en"] .honor-mobile-image {
        display: none !important;
    }
}

html[lang="en"] .culture-content {
    grid-template-columns: 667px 700px;
}



