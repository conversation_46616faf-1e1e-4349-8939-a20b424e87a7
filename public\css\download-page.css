/* download-page.css
   样式来源于 download.html 和 download_en.html 的 <style> 块，专用于下载页面。 */
:root {
    --primary-blue: #0066CC;
    --primary-light-blue: #E6F0F9;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #E5E5E5;
    --background-light: #F8F8F8;
    --white: #FFFFFF;
}

/* 只在body上添加overflow-x: hidden，防止横向滚动条 */
body {
    overflow-x: hidden;
}

/* 页面标题背景板 */
.page-title-banner {
    width: 100%;
    height: 6.25vw; /* 120px */
    background: #F8F8F8;
    border-radius: 0;
    display: flex;
    align-items: center;
    margin-bottom: 2.5vw; /* 48px */
}

.page-title {
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 500;
    font-size: 2.29vw; /* 44px */
    color: #00509E;
    line-height: 4.43vw; /* 85px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0;
}

/* 修复container样式冲突 */
.page-title-banner .container {
    margin: 0 auto; /* 添加自动边距使内容居中 */
}

/* 主要内容区域 */
.download-main {
    max-width: 62.5vw; /* 1200px */
    position: relative;
    margin-left: -1.5625vw; /* -30px */
    min-height: calc(100vh - 18.229167vw); /* 350px */
    padding-bottom: 2.604167vw; /* 50px */
}

/* 左侧选择区域 */
.download-sidebar {
    width: 14.583333vw; /* 280px */
    position: absolute;
    z-index: 10; /* 确保菜单在前面 */
}

.sidebar-category {
    position: relative;
}

.category-select {
    width: 14.583333vw; /* 280px */
    height: 3.072917vw; /* 59px */
    border-radius: 0.625vw; /* 12px */
    background: #F8F8F8;
    box-sizing: border-box;
    border: 0.104167vw solid #646464; /* 2px */
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    line-height: 1.510417vw; /* 29px */
    text-align: left;
    letter-spacing: normal;
    color: #646464;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    background-repeat: no-repeat;
    background-position: right 1.51042vw center; /* 29px */
    background-size: 0.833333vw 0.416667vw; /* 16px 8px */
    transition: all 0.3s ease;
}

/* 隐藏select下拉箭头在IE10+浏览器中 */
.category-select::-ms-expand {
    display: none;
}

/* 禁用原生下拉功能但保持外观 */
.category-select option {
    display: none;
}

.category-select:hover {
    border-color: #0050A2;
    color: #0050A2;
}

/* 单一菜单框展开效果 */
.expandable-menu {
    width: 14.583333vw; /* 280px */
    border-radius: 0.625vw; /* 12px */
    margin-bottom: 0.3125vw; /* 6px */
    border: 0.104167vw solid #646464; /* 1.5px */
    background: #F8F8F8;
    transition: all 0.3s ease;
    overflow: visible; /* 改为visible */
    height: auto; /* 改为auto */
    min-height: 3.072917vw; /* 添加最小高度 */
}

.expandable-menu:hover {
    border-color: #0050A2;
}

/* 展开时的高度和边框颜色 */
.expandable-menu.expanded {
    border-color: #0050A2;
    height: auto;
    padding-bottom: 0.78125vw; /* 15px */
}

/* 菜单标题 */
.menu-title {
    height:3.072917vw; /* 59px */
    display: flex;
    align-items: center;
    padding-left: 1.458333vw; /* 28px */
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    color: #646464;
    position: relative;
    cursor: pointer;
}

.expandable-menu:hover .menu-title {
    color: #0050A2;
}

.expandable-menu.expanded .menu-title {
    color: #0050A2;
}

/* 箭头图标 */
.arrow-icon {
    position: absolute;
    right: 1.51042vw; /* 29px */
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    pointer-events: none; /* 防止箭头捕获点击事件 */
    width: 0.833333vw; /* 16px */
    height: 0.416667vw; /* 8px */
}

.expandable-menu:hover .arrow-icon {
    content: var(--arrow-hover);
}

.expandable-menu.expanded .arrow-icon {
    content: var(--arrow-hover);
    transform: translateY(-50%) rotate(180deg);
}

/* 分隔线 - 在同一个文本框内 */
.menu-divider {
    width: 11.979167vw; /* 230px */
    height: 0;
    border-top: 1px solid #0050A2; /* 2px */
    margin: 0 0 0.677083vw 1.302083vw; /* 13px 25px */
    display: none; /* 默认隐藏 */
}
/* 展开时显示分割线 */
.expandable-menu.expanded .menu-divider {
    display: block;
}

/* 菜单项容器 */
.menu-items {
    display: flex;
    flex-direction: column;
    max-height: none; /* 移除最大高度限制 */
    overflow: visible; /* 确保内容可见 */
}

/* 菜单项 */
.menu-item {
    padding: 0 0 0 1.458333vw; /* 0 0 0 28px */
    font-family: "思源黑体";
    font-size: 0.833333vw; /* 16px */
    font-weight: normal;
    line-height: 33px;
    letter-spacing: normal;
    color: #646464;
    cursor: pointer;
    transition: color 0.3s ease;
}

/* 英文界面下菜单项字体大小调整 */
html[lang="en"] .menu-item {
    font-size: 0.729167vw; /* 14px，比中文版小一号 */
    letter-spacing: -0.010417vw; /* -0.2px，稍微调整字间距 */
}

.menu-item a {
    display: block;
    color: #646464;
    text-decoration: none;
    transition: color 0.3s ease;
}

.menu-item:hover, .menu-item.active {
    color: #0050A2;
}

.menu-item:hover a, .menu-item.active a {
    color: #0050A2;
}

.menu-item:not(:last-child) {
    margin-bottom: -0.260417vw; /* -5px */
}

/* 右侧内容区域 */
.download-content {
    margin-left: 16.09375vw; /* 309px */
}

/* 产品展示区域 */
.download-product {
    display: flex;
    align-items: center;
    background: #F8F8F8;
    border-radius: 0.625vw; /* 12px */
    padding: 0 1.666667vw; /* 25px 32px */
    width: 46.354167vw; /* 890px */
    height: 9.375vw; /* 180px */
}

.product-image {
    width: 8.154167vw; /* 156.52px */
    height: 6.197917vw; /* 119px */
    margin-right: 1.71875vw; /* 33px */
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border-radius: 0; /* 4px */
    overflow: hidden;
}

.product-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.product-actions {
    display: flex;
    align-items: center;
    gap: 0.833333vw; /* 16px */
}

.product-name {
    width: 14.739583vw; /* 283px */
    height: 2.708333vw; /* 52px */
    border-radius: 0.416667vw; /* 8px */
    background: #FFFFFF;
    box-sizing: border-box;
    border: 0.104167vw solid #0050A2; /* 2px */
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 1.041667vw; /* 20px */
    font-weight: 400;
    line-height: 2.708333vw; /* 52px */
    text-align: center;
    letter-spacing: normal;
    color: #0050A2;
    margin-bottom: 0;
}

/* 英文界面下产品名称字体大小调整 */
html[lang="en"] .product-name {
    font-size: 0.9375vw; /* 18px */
}


.product-buttons {
    display: flex;
    gap: 0.833333vw; /* 16px */
    align-items: center;
}

.btn-product {
    height: 2.708333vw; /* 52px */
    border-radius: 0.416667vw; /* 8px */
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    line-height: 2.708333vw; /* 52px */
    text-align: center;
    letter-spacing: normal;
    color: #FFFFFF;
    background: #0050A2;
    border: none;
    padding: 0;
    display: inline-block;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.btn-product:first-child {
    width: 8.697917vw; /* 167px */
    background: #0050A2;
    color: #FFFFFF;
    border: none;
}

.btn-product:first-child:hover {
    background: #007DDB;
}

.btn-product:last-child {
    width: 7.8125vw; /* 150px */
    background: #0050A2;
    color: #FFFFFF;
    border: none;
}

.btn-product:last-child:hover {
    background: #007DDB;
}

/* 下载列表相关样式 */
.download-container {
    display: flex;
    gap: 1.71875vw; /* 33px */
    justify-content: center;
    margin: 0 auto;
    width: 41.510417vw; /* 797px */
    overflow: hidden;
    margin-bottom: 5.546875vw; /* 106.5px */
}

.download-column {
    width: 19.895833vw; /* 382px */
    overflow: hidden;
}

/* 新增布局相关样式 */
.download-section {
    margin: 0;
    padding: 0;
    position: relative; /* 添加相对定位 */
}

.download-section:after {
    content: '';
    display: block;
    clear: both;
}

.section-group {
    width: 100%;
    margin-bottom: 1.041667vw; /* 20px */
}

.section-title {
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #323232;
    margin-top: 1.875vw; /* 36px */
    margin-bottom: -0.416667vw; /* -8px */
}

.download-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.807292vw; /* 15.5px */
    position: relative;
    width: 100%;
}

.download-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    border-top: 0.052083vw solid #CCCCCC; /* 1px */
}

.download-item:last-child {
    border-bottom: none;
}

.item-info {
    flex: 1;
    min-width: 0;
    width: 100%;
    max-width: 18.385417vw; /* 353px */
    margin-left: -0.822917vw; /* -15.8px */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.item-name {
    font-family: "思源黑体";
    font-size: 0.833333vw; /* 16px */
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #007DDB;
}

.item-note {
    font-family: "思源黑体";
    font-size: 0.729167vw; /* 14px */
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #929292;
    text-align: left;
    max-width: 15.729167vw; /* 302px */
    white-space: pre-wrap;  /* 保留空格和换行 */
    word-wrap: break-word;  /* 确保长词能够换行 */
}

.download-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-action img {
    width: 1.041667vw; /* 20px */
    height: 1.041667vw; /* 20px */
    margin-bottom: 0.104167vw; /* 2px */
    transition: all 0.3s ease;
    content: var(--download-icon);
}

.download-action span {
    font-size: 0.729167vw; /* 14px */
    color: #929292;
    transition: color 0.3s ease;
}

/* 悬停效果 - 使用蓝色版本的SVG */
.download-action:hover img {
    content: var(--download-icon-hover);
}

.download-action:hover span {
    color: #0050A2;
}

/* 下载列表布局 */
.download-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    align-items: start;
}

.download-list {
    width: 100%;
}

.download-banner {
    width: 100%;
    height: 6.25vw; /* 120px */
    background: #F8F8F8;
    display: flex;
    justify-content: center;
    align-items: center;
}
.download-banner .title {
    font-family: "思源黑体", "Source Han Sans CN", sans-serif;
    font-size: 2.291667vw; /* 44px */
    font-weight: 500;
    line-height: 2.291667vw; /* 44px */
    letter-spacing: -0.045em;
    color: #00509E;
}
.download-banner .desc {
    font-size: 1.3rem;
    opacity: 0.95;
}
.download-main {
    display: flex;
    padding: 1.041667vw; /* 20px */
    gap: 1.041667vw; /* 20px */
    max-width: 62.5vw; /* 1200px */
    margin: 0 auto;
}
.download-sidebar {
    width: 12.5vw; /* 240px */
    display: flex;
    flex-direction: column;
    gap: 0.520833vw; /* 10px */
}
.download-sidebar ul {
    background: #f7f7f7;
    border-radius: 0.625vw 0 0 0.625vw; /* 12px 0 0 12px */
    box-shadow: none;
    margin: 0;
    padding: 0;
}
.download-sidebar .sidebar-title {
    background: #bcbcbc;
    color: #fff;
    font-weight: bold;
    font-size: 1.32rem;
    padding: 0.9375vw 1.979167vw; /* 18px 38px */
    border-radius: 0.625vw 0 0 0; /* 12px 0 0 0 */
    cursor: default;
    border-left: none;
    margin-bottom: 0.104167vw; /* 2px */
    letter-spacing: 0.052083vw; /* 1px */
}
.download-sidebar .main-group {
    display: flex;
    align-items: center;
    font-size: 1.32rem;
    color: #174ea6;
    font-weight: bold;
    padding: 0.833333vw 1.666667vw 0.833333vw 1.666667vw; /* 16px 32px 16px 32px */
    background: #f7f7f7;
    border-left: 0.260417vw solid transparent; /* 5px */
    margin-top: 0.416667vw; /* 8px */
    margin-bottom: 0;
    border-radius: 0;
    cursor: pointer;
    position: relative;
    transition: background 0.2s, color 0.2s, border-color 0.2s;
}
.download-sidebar .main-group .arrow {
    font-size: 1.041667vw; /* 20px */
    margin-left: auto;
    color: #2176ff;
    transition: transform 0.3s;
}
.download-sidebar .main-group.active {
    background: #eaf3fb;
    color: #2176ff;
    border-left: 0.260417vw solid #2176ff; /* 5px */
}
.download-sidebar .main-group.active .arrow {
    transform: rotate(90deg) scale(1.2);
    color: #2176ff;
}
.download-sidebar .submenu-list {
    list-style: none;
    margin: 0 0 0 0;
    padding: 0 0 0.416667vw 0; /* 0 0 8px 0 */
    background: #fff;
    display: none;
}
.download-sidebar .submenu-list.show {
    display: block;
}
.download-sidebar .submenu-list li {
    padding: 0.625vw 1.666667vw 0.625vw 2.5vw; /* 12px 32px 12px 48px */
    font-size: 1.18rem;
    color: #000;
    border-left: 0.15625vw solid transparent; /* 3px */
    background: none;
    font-weight: bold;
    transition: background 0.2s, color 0.2s, border-color 0.2s;
}
.download-sidebar .submenu-list li.active,
.download-sidebar .submenu-list li:hover {
    background: #e0e9f5;
    border-left: 0.15625vw solid #2176ff; /* 3px */
    color: #2176ff;
}
.download-sidebar .submenu-list li a {
    color: inherit;
    text-decoration: none;
    display: block;
    width: 100%;
}
.download-sidebar li.active > span {
    color: #1a3e72;
    font-weight: bold;
}
.download-sidebar li.active:not(.sidebar-title) {
    background: #dbe8fa;
    border-left: 0.260417vw solid #2176ff; /* 5px */
    color: #2176ff;
}
.download-sidebar li.has-submenu:not(:last-child) {
    border-bottom: 0.052083vw solid #e0e0e0; /* 1px */
}
.download-sidebar li:not(.sidebar-title):after {
    display: none;
}
.download-sidebar li, 
.download-sidebar li span,
.download-sidebar li i,
.download-sidebar .submenu li {
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
}
.download-product-info {
    flex: 1;
}
.download-product-title {
    font-size: 1.6rem;
    font-weight: bold;
    color: var(--main-blue);
    letter-spacing: 0.052083vw; /* 1px */
}
.download-product-desc {
    color: #444;
    margin-top: 0.520833vw; /* 10px */
    font-size: 1.08rem;
}
.download-product-btns {
    margin-left: 1.666667vw; /* 32px */
}
.download-product-btns .btn {
    background: var(--main-blue-btn);
    color: #fff;
    border: none;
    border-radius: 0.260417vw; /* 5px */
    margin-right: 0.729167vw; /* 14px */
    padding: 0.520833vw 1.458333vw; /* 10px 28px */
    font-size: 1.08rem;
    font-weight: 500;
    transition: background 0.2s;
    box-shadow: 0 0.104167vw 0.3125vw rgba(33,118,255,0.08); /* 2px 6px */
}
.download-product-btns .btn:hover {
    background: var(--main-blue-btn-hover);
}
.download-module {
    border: 0.052083vw solid #e0e0e0; /* 1px */
    border-radius: 0.416667vw; /* 8px */
    background: #fff;
    margin: 0;
    padding: 0;
    width: 100%;
}
.module-header {
    display: flex;
    align-items: center;
    padding: 0.78125vw; /* 15px */
    border-bottom: 0.052083vw solid #eaf3fb; /* 1px */
    background: #f5f8fc;
}
.module-icon {
    color: var(--main-blue);
    font-size: 1.3rem;
    margin-right: 0.416667vw; /* 8px */
}
.module-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--main-blue);
    letter-spacing: 0.026042vw; /* 0.5px */
}
.module-content {
    padding: 0;
}
.download-item {
    padding: 0.78125vw; /* 15px */
    border-bottom: 0.052083vw solid #eaf3fb; /* 1px */
    margin: 0;
}
.download-item:last-child {
    border-bottom: none;
}
.item-title {
    font-size: 0.729167vw; /* 14px */
    color: var(--main-blue-dark);
    margin-bottom: 0.416667vw; /* 8px */
}
.item-desc {
    color: #666;
    font-size: 0.677083vw; /* 13px */
    margin-bottom: 0.416667vw; /* 8px */
}
.item-download {
    text-align: right;
}
.item-download a {
    color: var(--main-blue);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}
.item-download a:hover {
    color: var(--main-blue-btn-hover);
}
.item-download i {
    margin-right: 0.260417vw; /* 5px */
    font-size: 0.833333vw; /* 16px */
}

.modal-backdrop {
    position: fixed;
    top: 0; right: 0; bottom: 0; left: 0;
    z-index: 1040;
    background: #000;
    opacity: 0.35;
    filter: alpha(opacity=35);
}
.modal {
    z-index: 1050 !important;
}
/* 防止 backdrop 遮挡 modal */
.modal.in {
    display: block;
}
/* 修正 body 灰色问题 */
body.modal-open {
    overflow: hidden;
}
/* 工具下载弹框自定义样式 */
.tool-modal-content {
    border-radius: 0.520833vw; /* 10px */
}
.tool-modal-header {
    border-bottom: 0.052083vw solid #eee; /* 1px */
    padding: 0.78125vw 1.041667vw; /* 15px 20px */
}
.tool-modal-title {
    font-size: 0.9375vw; /* 18px */
    font-weight: 500;
}
.tool-modal-body {
    padding: 1.041667vw; /* 20px */
}
.tool-modal-toolname {
    font-size: 0.833333vw; /* 16px */
    font-weight: 500;
    margin-bottom: 0.520833vw; /* 10px */
}
.tool-modal-code {
    color: #666;
    margin: 0.520833vw 0; /* 10px 0 */
}
.tool-modal-code span {
    color: #007bff;
    font-weight: 500;
}
.tool-modal-divider {
    margin: 0.78125vw 0; /* 15px 0 */
    border-top: 0.052083vw solid #eee; /* 1px */
}
.tool-modal-btns {
    display: flex;
    gap: 0.833333vw; /* 16px */
    justify-content: center;
    width: 100%;
}
.tool-modal-btn {
    width: 8.697917vw; /* 167px */
    height: 2.708333vw; /* 52px */
    padding: 0;  /* 移除内边距，使用固定宽度 */
    border-radius: 0.416667vw; /* 8px */
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    line-height: 2.708333vw; /* 52px */
    text-align: center;
    letter-spacing: normal;
    color: #FFFFFF;
    background: #0050A2;
    text-decoration: none;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;
    white-space: nowrap;  /* 防止文字换行 */
    overflow: hidden;     /* 超出部分隐藏 */
    text-overflow: ellipsis;  /* 超出显示省略号 */
}
.tool-modal-btn:hover {
    background: #007DDB;
    color: #FFFFFF;
    text-decoration: none;
}
.tool-modal-btn-active {
    background: #0050A2;
    color: #FFFFFF;
}
.tool-modal-btn-disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}
.tool-modal-empty {
    color: #666;
    text-align: center;
    padding: 1.041667vw 0; /* 20px 0 */
}
/* 面包屑导航样式 */
.breadcrumb {
    margin: 0;
    padding: 0;
    background: none;
    display: flex;
    align-items: center;
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 0.83vw; /* 16px */
    line-height: 1.51vw; /* 29px */
    font-style: normal;
    text-transform: none;
    margin-bottom: 1.458333vw; /* 28px */
    margin-left: 1.09375vw;  /* 21px */
}

.breadcrumb a {
    color: #333333;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #055999;
}

.breadcrumb .separator {
    margin: 0 0.42vw; /* 8px */
    color: #333333;
}

.breadcrumb .current {
    color: #333333;
}

/* 页脚间距 */
footer {
    margin-top: 5.208333vw; /* 100px */
    position: relative;
    clear: both;
}
.containert {
    margin-left: 17.760417vw; /* 341px */
    margin-right: 19.791667vw; /* 380px */
}

/* 下载按钮容器 */
.download-buttons {
    display: flex;
    gap: 0.833333vw;  /* 16px */
    margin-top: 1.041667vw;  /* 20px */
}

/* 下载按钮样式 */
.download-btn {
    height: 2.708333vw; /* 52px */
    padding: 0 1.666667vw; /* 32px */
    border-radius: 0.416667vw; /* 8px */
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    line-height: 2.708333vw; /* 52px */
    text-align: center;
    letter-spacing: normal;
    color: #FFFFFF;
    background: #0050A2;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.download-btn:hover {
    background: #007DDB;
    color: #FFFFFF;
    text-decoration: none;
}

/* 针对491px以下屏幕的媒体查询 */
@media screen and (max-width: 491px) {
    /* 基础样式 */
    body {
        font-size: 6px; /* 原来20px的0.3倍 */
    }
    
    /* 页面标题背景板 */
    .page-title-banner {
        height: 36px; /* 120px的0.3倍 */
        margin-bottom: 14.4px; /* 48px的0.3倍 */
    }
    
    .page-title {
        font-size: 13.2px; /* 44px的0.3倍 */
        line-height: 25.5px; /* 85px的0.3倍 */
    }
    
    /* 主要内容区域 */
    .download-main {
        max-width: 360px; /* 1200px的0.3倍 */
        margin-left: -9px; /* -30px的0.3倍 */
        min-height: calc(100vh - 105px); /* 350px的0.3倍 */
        padding-bottom: 15px; /* 50px的0.3倍 */
        padding-left: 6px; /* 左侧间距缩小一半 */
        padding-right: 6px; /* 右侧间距缩小一半 */
    }
    
    /* 左侧选择区域 */
    .download-sidebar {
        width: 87.9px; /* 293px的0.3倍 */
    }
    
    .sidebar-category {
        margin-bottom: 1.8px; /* 6px的0.3倍 */
    }
    
    .category-select {
        width: 87.9px; /* 293px的0.3倍 */
        height: 18.9px; /* 63px的0.3倍 */
        border-radius: 3.6px; /* 12px的0.3倍 */
        border-width: 0.45px; /* 1.5px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
        line-height: 6px; /* 20px的0.3倍 */
        padding: 0 0 0 9.6px; /* 32px的0.3倍 */
        background-position: right 8.25px center; /* 27.48px的0.3倍 */
        background-size: 4.8px 4.8px; /* 16px的0.3倍 */
    }
    
    /* 单一菜单框 */
    .expandable-menu {
        width: 87.9px; /* 293px的0.3倍 */
        border-radius: 3.6px; /* 12px的0.3倍 */
        margin-bottom: 1.8px; /* 6px的0.3倍 */
        border-width: 0.45px; /* 1.5px的0.3倍 */
        height: 18.9px; /* 63px的0.3倍 */
    }
    
    .expandable-menu.expanded {
        padding-bottom: 5.7px; /* 19px的0.3倍 */
    }
    
    /* 菜单标题 */
    .menu-title {
        height: 18.9px; /* 63px的0.3倍 */
        padding-left: 9.6px; /* 32px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
    }
    
    /* 箭头图标 */
    .arrow-icon {
        right: 8.25px; /* 27.48px的0.3倍 */
        width: 4.8px; /* 16px的0.3倍 */
        height: 4.8px; /* 16px的0.3倍 */
    }
    
    /* 分隔线 */
    .menu-divider {
        width: 70.2px; /* 234px的0.3倍 */
        border-top-width: 0.45px; /* 1.5px的0.3倍 */
        margin: 0 0 4.5px 9.45px; /* 15px和31.5px的0.3倍 */
    }
    
    /* 菜单项 */
    .menu-item {
        padding: 2.4px 0 2.4px 9.6px; /* 8px 0 8px 32px的0.3倍 */
        font-size: 4.8px; /* 16px的0.3倍 */
    }
    
    .menu-item:not(:last-child) {
        margin-bottom: -1.5px; /* -5px的0.3倍 */
    }
    
    /* 右侧内容区域 */
    .download-content {
        margin-left: 92.7px; /* 309px的0.3倍 */
    }
    
    /* 产品展示区域 */
    .download-product {
        border-radius: 3.6px; /* 12px的0.3倍 */
        padding: 7.5px 9.6px; /* 25px 32px的0.3倍 */
        width: 267px; /* 890px的0.3倍 */
        height: 57.9px; /* 193px的0.3倍 */
    }
    
    .product-image {
        width: 46.95px; /* 156.52px的0.3倍 */
        height: 35.7px; /* 119px的0.3倍 */
        margin-right: 10.05px; /* 33.48px的0.3倍 */
        border-radius: 1.2px; /* 4px的0.3倍 */
    }
    
    .product-actions {
        gap: 4.8px; /* 16px的0.3倍 */
    }
    
    .product-name {
        width: 84.9px; /* 283px的0.3倍 */
        height: 15.6px; /* 52px的0.3倍 */
        border-radius: 2.4px; /* 8px的0.3倍 */
        border-width: 0.45px; /* 1.5px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
        line-height: 15.6px; /* 52px的0.3倍 */
    }
    
    .product-buttons {
        gap: 4.8px; /* 16px的0.3倍 */
    }
    
    .btn-product {
        height: 15.6px; /* 52px的0.3倍 */
        border-radius: 2.4px; /* 8px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
        line-height: 15.6px; /* 52px的0.3倍 */
    }
    
    .btn-product:first-child {
        width: 50.1px; /* 167px的0.3倍 */
    }
    
    .btn-product:last-child {
        width: 45px; /* 150px的0.3倍 */
    }
    
    /* 下载列表相关样式 */
    .download-container {
        gap: 9.9px; /* 33px的0.3倍 */
        width: 239.1px; /* 797px的0.3倍 */
        margin-bottom: 31.95px; /* 106.5px的0.3倍 */
    }
    
    .download-column {
        width: 114.6px; /* 382px的0.3倍 */
    }
    
    .section-group {
        margin-bottom: 6px; /* 20px的0.3倍 */
    }
    
    .section-title {
        font-size: 6px; /* 20px的0.3倍 */
        margin-top: 10.8px; /* 36px的0.3倍 */
        margin-bottom: -2.4px; /* -8px的0.3倍 */
    }
    
    .download-item {
        margin-bottom: 4.65px; /* 15.5px的0.3倍 */
    }
    
    .download-item::after {
        border-top-width: 0.3px; /* 1px的0.3倍 */
    }
    
    .item-info {
        max-width: none;
        margin-left: -2px;
        width: calc(100% - 20px);
        position: relative;
    }
    
    .item-name {
        font-size: 8px !important;
        transform: scale(0.7);
        transform-origin: left top;
        display: inline-block; /* 使用inline-block而不是block以维持在一行 */
        line-height: 1; /* 使用最小的行高 */
        margin-bottom: 3px; /* 添加底部边距以确保与下方内容分开 */
    }
    
    .item-note {
        font-size: 8px !important;
        transform: scale(0.7);
        transform-origin: left top;
        display: block;
        line-height: 1;
        width: 350%;
        word-break: break-all;
        word-wrap: break-word;
        white-space: pre-wrap;
    }
    
    .download-action img {
        width: 6px; /* 20px的0.3倍 */
        height: 6px; /* 20px的0.3倍 */
        margin-bottom: 0.6px; /* 2px的0.3倍 */
    }
    
    .download-action span {
        font-size: 4.2px; /* 14px的0.3倍 */
    }
    
    /* 下载列表布局 */
    .download-sections {
        gap: 7.2px; /* 24px的0.3倍 */
    }
    
    /* 下载banner */
    .download-banner {
        height: 36px; /* 120px的0.3倍 */
    }
    
    .download-banner .title {
        font-size: 13.2px; /* 44px的0.3倍 */
        line-height: 13.2px; /* 44px的0.3倍 */
    }
    
    /* 下载按钮 */
    .download-btn {
        height: 15.6px; /* 52px的0.3倍 */
        padding: 0 9.6px; /* 32px的0.3倍 */
        border-radius: 2.4px; /* 8px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
        line-height: 15.6px; /* 52px的0.3倍 */
    }
    
    /* 面包屑导航 */
    .breadcrumb {
        margin-bottom: 8.4px; /* 28px的0.3倍 */
        margin-left: 6.3px; /* 21px的0.3倍 */
        font-size: 4.8px; /* 16px的0.3倍 */
        line-height: 8.7px; /* 29px的0.3倍 */
    }
    
    .breadcrumb .separator {
        margin: 0 2.4px; /* 8px的0.3倍 */
    }
    
    /* 页脚间距 */
    footer {
        margin-top: 30px; /* 100px的0.3倍 */
    }
    
    .containert {
        margin-left: 0px; /* 341px的0.3倍 */
        margin-right: 0px; /* 380px的0.3倍 */
    }
    
    /* 下载按钮容器 */
    .download-buttons {
        gap: 4.8px; /* 16px的0.3倍 */
        margin-top: 6px; /* 20px的0.3倍 */
    }
    
    /* 特殊缩放处理下载项的版本号和提取码等文本 */
    .item-note,
    .download-item .version-number,
    .download-item .extract-code {
        font-size: 8px !important;
        transform: scale(0.7);
        transform-origin: left top;
        display: block; /* 改为block以允许换行 */
        line-height: 1; /* 使用最小的行高 */
    }
    
    /* 英文界面下载项详情内容的文字样式 */
    html[lang="en"] .item-name,
    html[lang="en"] .item-note,
    html[lang="en"] .item-desc,
    html[lang="en"] .download-item .version-number,
    html[lang="en"] .download-item .extract-code {
        font-size: 8px !important;
        transform: scale(0.7);
        transform-origin: left top;
        display: block; /* 改为block以允许换行 */
        line-height: 1; /* 使用最小的行高 */
    }
    html[lang="en"] .product-name {
        width: 84.9px;  /* 283px的0.3倍 */
        height: 15.6px; /* 52px的0.3倍 */
        border-radius: 2.4px;
        border: 0.45px solid #0050A2;
        position: relative;
        overflow: visible;
        color: transparent; /* 隐藏原始文本 */
        font-size: 0; /* 确保原始文本不占空间 */
    }

    html[lang="en"] .product-name::before {
        content: attr(data-text);
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) scale(0.68);
        transform-origin: left center;
        width: 150%;
        font-size: 8px;
        white-space: nowrap;
        text-align: center;
        color: #0050A2;
    }
    
    /* 弹窗按钮在小屏幕下的自适应样式 */
    .tool-modal-btns {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .tool-modal-btn {
        width: auto; /* 自动宽度 */
        min-width: 50px; /* 最小宽度 */
        max-width: 100%; /* 最大宽度 */
        height: 15.6px; /* 52px的0.3倍 */
        padding: 0 5px; /* 水平内边距 */
        border-radius: 2.4px; /* 8px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
        line-height: 15.6px; /* 52px的0.3倍 */
        white-space: nowrap;
        overflow: visible; /* 允许内容溢出 */
    }
    
    .tool-modal-title {
        font-size: 8px;
    }
    
    .tool-modal-toolname {
        font-size: 7px;
    }
    
    .tool-modal-code {
        font-size: 6px;
    }
}

