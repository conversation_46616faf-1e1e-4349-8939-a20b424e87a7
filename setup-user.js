const mysql = require('mysql2');

// 数据库连接配置
const db = mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'beiqifxl',  // 使用与server.js相同的密码
    database: 'company_website'
});

// 连接数据库
db.connect((err) => {
    if (err) {
        console.error('数据库连接失败:', err);
        process.exit(1);
    }
    console.log('数据库连接成功');
    
    // 创建passwords表（如果不存在）
    const createTableSQL = `
    CREATE TABLE IF NOT EXISTS passwords (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`;
    
    db.query(createTableSQL, (err) => {
        if (err) {
            console.error('创建表失败:', err);
            db.end();
            process.exit(1);
        }
        console.log('passwords表已创建或已存在');
        
        // 检查用户是否已存在
        const username = 'admin';  // 默认用户名
        const password = '123';  // 默认密码，修改为123
        
        db.query('SELECT * FROM passwords WHERE username = ?', [username], (err, results) => {
            if (err) {
                console.error('查询用户失败:', err);
                db.end();
                process.exit(1);
            }
            
            if (results.length > 0) {
                console.log(`用户 ${username} 已存在，更新密码为 ${password}`);
                // 更新现有用户的密码
                db.query('UPDATE passwords SET password = ? WHERE username = ?', 
                    [password, username], 
                    (err, result) => {
                        if (err) {
                            console.error('更新密码失败:', err);
                            db.end();
                            process.exit(1);
                        }
                        console.log(`已成功更新用户 ${username} 的密码为: ${password}`);
                        db.end();
                        process.exit(0);
                    }
                );
            } else {
                // 添加新用户
                db.query('INSERT INTO passwords (username, password) VALUES (?, ?)', 
                    [username, password], 
                    (err, result) => {
                        if (err) {
                            console.error('添加用户失败:', err);
                            db.end();
                            process.exit(1);
                        }
                        console.log(`已成功添加用户: ${username}，密码: ${password}`);
                        db.end();
                        process.exit(0);
                    }
                );
            }
        });
    });
}); 