{"requireCurlyBraces": ["if", "else", "for", "while", "do"], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return"], "disallowKeywords": ["with"], "disallowKeywordsOnNewLine": ["else"], "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpaceAfterObjectKeys": true, "requireMultipleVarDecl": "onevar", "disallowMixedSpacesAndTabs": "smart", "disallowTrailingWhitespace": true, "requireSpacesInsideObjectBrackets": "all", "requireSpacesInsideArrayBrackets": "all", "requireSpacesInConditionalExpression": true, "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpaceAfterBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "disallowSpaceAfterPrefixUnaryOperators": ["++", "--"], "disallowSpaceBeforePostfixUnaryOperators": ["++", "--"], "disallowSpaceBeforeBinaryOperators": [",", ":"], "disallowMultipleLineBreaks": true, "requireLineFeedAtFileEnd": true, "validateLineBreaks": "LF"}