const https = require('https');
const fs = require('fs');

// 固定的access_token
const ACCESS_TOKEN = '94_cZaKNzwd-xWFHdcoztiX57ARRxMCsL44BmGT5B09juhpa-4P3pp3Bi_NC31VndlZyphafA6rThzS6Gkv9MYHA-opG7Gjsk2GyeEhv_bhKjOomiT4QMEDUEqcX9kMTJgAEAVCS';

/**
 * 获取已发布的消息列表
 * @param {number} offset 偏移位置，0表示从第一个开始
 * @param {number} count 返回数量，取值1-20
 * @param {number} noContent 是否返回content字段，1不返回，0返回
 * @returns {Promise<Object>} 文章列表数据
 */
async function getPublishedArticleList(offset = 0, count = 20, noContent = 1) {
    return new Promise((resolve, reject) => {
        try {
            const url = `https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token=${ACCESS_TOKEN}`;

            const postData = JSON.stringify({
                offset: offset,
                count: count,
                no_content: noContent
            });

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };

            console.log(`正在获取文章列表 (offset: ${offset}, count: ${count})...`);

            const req = https.request(url, options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        const result = JSON.parse(data);

                        // 检查是否有错误码
                        if (result.errcode && result.errcode !== 0) {
                            const errorMsg = getErrorMessage(result.errcode, result.errmsg);
                            console.error(`❌ 微信API返回错误: ${errorMsg} (错误码: ${result.errcode})`);
                            reject(new Error(`获取文章列表失败: ${errorMsg} (错误码: ${result.errcode})`));
                        } else if (result.total_count !== undefined) {
                            // 成功获取数据
                            console.log(`✅ 成功获取文章列表! 总数: ${result.total_count}, 本次获取: ${result.item_count}`);
                            resolve(result);
                        } else {
                            // 未知响应格式
                            console.error(`❌ 未知的响应格式:`, result);
                            reject(new Error(`获取文章列表失败: 未知的响应格式`));
                        }
                    } catch (error) {
                        console.error(`❌ 解析JSON失败:`, error);
                        console.error(`原始响应数据:`, data);
                        reject(new Error(`解析微信接口响应失败: ${error.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`请求微信接口失败: ${error.message}`));
            });

            req.write(postData);
            req.end();

        } catch (error) {
            reject(error);
        }
    });
}

/**
 * 获取所有已发布文章的ID列表
 * @returns {Promise<Array>} 所有文章ID的数组
 */
async function getAllArticleIds() {
    const allArticleIds = [];
    let offset = 0;
    const count = 20; // 每次获取20个

    try {
        while (true) {
            const result = await getPublishedArticleList(offset, count, 1); // no_content=1，只获取ID

            if (result.item && result.item.length > 0) {
                // 提取article_id
                const articleIds = result.item.map(item => item.article_id);
                allArticleIds.push(...articleIds);

                console.log(`已获取 ${allArticleIds.length}/${result.total_count} 个文章ID`);

                // 如果已经获取完所有文章，退出循环
                if (allArticleIds.length >= result.total_count) {
                    break;
                }

                offset += count;
            } else {
                break;
            }
        }

        return allArticleIds;
    } catch (error) {
        throw error;
    }
}

/**
 * 获取微信公众号已发布的图文信息
 * @param {string} articleId 文章ID
 * @returns {Promise<Object>} 文章数据
 */
async function getWechatArticle(articleId) {
    return new Promise((resolve, reject) => {
        try {
            const url = `https://api.weixin.qq.com/cgi-bin/freepublish/getarticle?access_token=${ACCESS_TOKEN}`;
            
            const postData = JSON.stringify({
                article_id: articleId
            });
            
            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };
            
            console.log(`正在获取文章 ${articleId} 的信息...`);
            
            const req = https.request(url, options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const result = JSON.parse(data);
                        
                        // 检查是否有错误码
                        if (result.errcode && result.errcode !== 0) {
                            const errorMsg = getErrorMessage(result.errcode, result.errmsg);
                            console.error(`❌ 获取文章失败: ${errorMsg} (错误码: ${result.errcode})`);
                            reject(new Error(`获取文章失败: ${errorMsg} (错误码: ${result.errcode})`));
                        } else if (result.news_item) {
                            // 成功获取文章数据
                            console.log('✅ 成功获取文章信息!');
                            resolve(result);
                        } else {
                            // 未知响应格式
                            console.error(`❌ 未知的文章响应格式:`, result);
                            reject(new Error(`获取文章失败: 未知的响应格式`));
                        }
                    } catch (error) {
                        reject(new Error(`解析微信接口响应失败: ${error.message}`));
                    }
                });
            });
            
            req.on('error', (error) => {
                reject(new Error(`请求微信接口失败: ${error.message}`));
            });
            
            req.write(postData);
            req.end();

        } catch (error) {
            reject(error);
        }
    });
}

/**
 * 获取错误码对应的中文说明
 */
function getErrorMessage(errcode, errmsg) {
    const errorMap = {
        0: '成功',
        48001: 'API功能未授权，请确认公众号已获得该接口权限',
        53600: '无效的文章ID',
        40001: 'access_token过期或无效',
        40014: '不合法的access_token',
        41001: '缺少access_token参数',
        42001: 'access_token超时'
    };
    
    return errorMap[errcode] || errmsg || '未知错误';
}

/**
 * 格式化显示文章信息
 */
function displayArticleInfo(articleData) {
    console.log('\n' + '='.repeat(60));
    console.log('📄 文章信息');
    console.log('='.repeat(60));

    if (articleData.news_item && articleData.news_item.length > 0) {
        articleData.news_item.forEach((item, index) => {
            console.log(`\n📰 文章 ${index + 1}:`);
            console.log(`标题: ${item.title || '无标题'}`);
            console.log(`作者: ${item.author || '无作者'}`);
            console.log(`摘要: ${item.digest || '无摘要'}`);
            console.log(`原文链接: ${item.content_source_url || '无链接'}`);
            console.log(`封面图片: ${item.thumb_url || '无封面'}`);
            console.log(`是否已删除: ${item.is_deleted ? '是' : '否'}`);
            console.log(`评论设置: ${item.need_open_comment ? '开启评论' : '关闭评论'}`);
            console.log(`临时链接: ${item.url || '无链接'}`);

            if (item.content) {
                console.log(`内容长度: ${item.content.length} 字符`);
                // 显示内容的前200个字符
                const preview = item.content.replace(/<[^>]*>/g, '').substring(0, 200);
                console.log(`内容预览: ${preview}${item.content.length > 200 ? '...' : ''}`);
            }

            console.log('-'.repeat(40));
        });
    } else {
        console.log('❌ 没有找到文章信息');
    }

    console.log('='.repeat(60));
}

/**
 * 生成单篇文章的HTML文件
 * @param {Object} articleData 文章数据
 * @param {string} outputDir 输出目录
 * @returns {Array} 生成的HTML文件路径列表
 */
function generateArticleHTML(articleData, outputDir = './html_articles') {
    const htmlFiles = [];

    // 确保输出目录存在
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    if (articleData.news_item && articleData.news_item.length > 0) {
        articleData.news_item.forEach((item, index) => {
            const htmlTemplate = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${item.title || '微信公众号文章'}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 100%;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .article-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .article-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .article-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        .article-meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .article-digest {
            color: #888;
            font-style: italic;
            margin-bottom: 15px;
        }
        .article-content {
            font-size: 16px;
            line-height: 1.8;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 15px auto;
        }
        .article-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        /* 保持微信原有样式 */
        .article-content section {
            margin: 0;
            padding: 0;
        }
        .article-content p {
            margin: 10px 0;
        }
        /* 响应式设计 */
        @media (max-width: 768px) {
            .article-container {
                margin: 10px;
                padding: 15px;
            }
            .article-title {
                font-size: 20px;
            }
            .article-content {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="article-container">
        <div class="article-header">
            <h1 class="article-title">${item.title || '无标题'}</h1>
            <div class="article-meta">
                <span>作者: ${item.author || '未知'}</span>
                ${item.content_source_url ? ` | <a href="${item.content_source_url}" target="_blank">原文链接</a>` : ''}
            </div>
            ${item.digest ? `<div class="article-digest">${item.digest}</div>` : ''}
        </div>
        <div class="article-content">
            ${item.content || '<p>暂无内容</p>'}
        </div>
        <div class="article-footer">
            <p>文章ID: ${articleData.article_id}</p>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            ${item.thumb_url ? `<p>封面图片: <a href="${item.thumb_url}" target="_blank">查看</a></p>` : ''}
        </div>
    </div>
</body>
</html>`;

            // 生成安全的文件名
            const safeTitle = (item.title || `article_${index + 1}`)
                .replace(/[<>:"/\\|?*]/g, '_')
                .substring(0, 50);
            const filename = `${articleData.article_id}_${index + 1}_${safeTitle}.html`;
            const filepath = `${outputDir}/${filename}`;

            fs.writeFileSync(filepath, htmlTemplate, 'utf8');
            htmlFiles.push(filepath);

            console.log(`✅ 生成HTML文件: ${filename}`);
        });
    }

    return htmlFiles;
}

/**
 * 批量生成所有文章的HTML文件
 * @param {Array} allArticles 所有文章数据
 * @param {string} outputDir 输出目录
 * @returns {Object} 生成结果统计
 */
function generateAllArticlesHTML(allArticles, outputDir = './html_articles') {
    console.log(`\n📄 开始生成HTML文件到目录: ${outputDir}`);

    let totalFiles = 0;
    let successCount = 0;
    const failedArticles = [];

    allArticles.forEach((article, index) => {
        try {
            console.log(`\n[${index + 1}/${allArticles.length}] 生成文章HTML: ${article.article_id}`);
            const htmlFiles = generateArticleHTML(article, outputDir);
            totalFiles += htmlFiles.length;
            successCount++;
        } catch (error) {
            console.error(`❌ 生成文章 ${article.article_id} 的HTML失败: ${error.message}`);
            failedArticles.push({
                article_id: article.article_id,
                error: error.message
            });
        }
    });

    const result = {
        totalArticles: allArticles.length,
        successCount,
        failedCount: failedArticles.length,
        totalHtmlFiles: totalFiles,
        failedArticles,
        outputDir
    };

    console.log('\n' + '='.repeat(60));
    console.log('📊 HTML生成完成统计');
    console.log('='.repeat(60));
    console.log(`处理文章数: ${result.totalArticles}`);
    console.log(`成功生成: ${result.successCount}`);
    console.log(`失败数量: ${result.failedCount}`);
    console.log(`HTML文件数: ${result.totalHtmlFiles}`);
    console.log(`输出目录: ${result.outputDir}`);
    console.log(`成功率: ${((result.successCount / result.totalArticles) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));

    if (failedArticles.length > 0) {
        console.log('\n❌ 失败的文章:');
        failedArticles.forEach(item => {
            console.log(`  - ${item.article_id}: ${item.error}`);
        });
    }

    return result;
}

// 如果直接运行此文件
if (require.main === module) {
    console.log('🚀 开始获取所有微信公众号文章...');
    console.log(`使用Token: ${ACCESS_TOKEN.substring(0, 20)}...`);
    console.log('');
    console.log('📝 注意：此接口只能获取已发布的文章，不包括草稿状态的文章');
    console.log('');

    async function main() {
        try {
            // 第一步：获取所有文章ID
            console.log('📋 第一步：获取所有文章ID列表...');
            const articleIds = await getAllArticleIds();

            console.log(`\n✅ 成功获取到 ${articleIds.length} 个文章ID:`);
            articleIds.forEach((id, index) => {
                console.log(`  ${index + 1}. ${id}`);
            });

            // 保存文章ID列表
            const idsFilename = `article_ids_${Date.now()}.json`;
            fs.writeFileSync(idsFilename, JSON.stringify(articleIds, null, 2), 'utf8');
            console.log(`\n💾 文章ID列表已保存到: ${idsFilename}`);

            // 第二步：获取所有文章详情
            console.log('\n📄 第二步：获取所有文章详细内容...');
            const allArticles = [];

            for (let i = 0; i < articleIds.length; i++) {
                const articleId = articleIds[i];
                console.log(`\n[${i + 1}/${articleIds.length}] 正在获取文章: ${articleId}`);

                try {
                    const articleDetail = await getWechatArticle(articleId);
                    allArticles.push({
                        article_id: articleId,
                        ...articleDetail
                    });

                    console.log(`✅ 成功获取文章 ${articleId}`);

                    // 显示文章基本信息
                    if (articleDetail.news_item && articleDetail.news_item.length > 0) {
                        const firstItem = articleDetail.news_item[0];
                        console.log(`   标题: ${firstItem.title || '无标题'}`);
                        console.log(`   作者: ${firstItem.author || '无作者'}`);
                    }

                    // 添加延迟，避免请求过于频繁
                    if (i < articleIds.length - 1) {
                        console.log('   等待1秒...');
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }

                } catch (error) {
                    console.error(`❌ 获取文章 ${articleId} 失败: ${error.message}`);
                    // 继续处理下一篇文章
                }
            }

            console.log(`\n✅ 成功获取到 ${allArticles.length} 篇文章的详细内容`);

            // 保存所有文章详情
            const articlesFilename = `all_articles_${Date.now()}.json`;
            fs.writeFileSync(articlesFilename, JSON.stringify(allArticles, null, 2), 'utf8');
            console.log(`💾 所有文章详情已保存到: ${articlesFilename}`);

            // 第三步：生成HTML文件
            console.log('\n🌐 第三步：生成HTML文件...');
            const htmlResult = generateAllArticlesHTML(allArticles, `./html_articles_${Date.now()}`);

            // 显示统计信息
            console.log('\n' + '='.repeat(60));
            console.log('📊 获取完成统计');
            console.log('='.repeat(60));
            console.log(`总文章数: ${articleIds.length}`);
            console.log(`成功获取: ${allArticles.length}`);
            console.log(`失败数量: ${articleIds.length - allArticles.length}`);
            console.log(`成功率: ${((allArticles.length / articleIds.length) * 100).toFixed(1)}%`);
            console.log(`HTML文件数: ${htmlResult.totalHtmlFiles}`);
            console.log(`HTML输出目录: ${htmlResult.outputDir}`);
            console.log('='.repeat(60));

            // 显示部分文章信息作为预览
            if (allArticles.length > 0) {
                console.log('\n📄 文章预览 (前3篇):');
                allArticles.slice(0, 3).forEach((article, index) => {
                    if (article.news_item && article.news_item.length > 0) {
                        const item = article.news_item[0];
                        console.log(`\n${index + 1}. ${item.title || '无标题'}`);
                        console.log(`   ID: ${article.article_id}`);
                        console.log(`   作者: ${item.author || '无作者'}`);
                        console.log(`   摘要: ${(item.digest || '无摘要').substring(0, 50)}...`);
                    }
                });
            }

        } catch (error) {
            console.error('\n❌ 获取文章失败:');
            console.error(error.message);
            console.log('\n常见问题排查:');
            console.log('1. 检查access_token是否有效');
            console.log('2. 检查公众号是否有该接口权限');
            console.log('3. 检查网络连接是否正常');
        }
    }

    main();
}

module.exports = {
    getPublishedArticleList,
    getAllArticleIds,
    getWechatArticle,
    displayArticleInfo,
    generateArticleHTML,
    generateAllArticlesHTML
};
