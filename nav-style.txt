/* 导航栏样式设计（精简优化版） */

/* 字体导入 */
@import url('http://fonts.googleapis.com/css?family=Noto+Serif:400,400italic,700|Open+Sans:400,600,700');
@import url('font-awesome.css');
@import url('animate.css');

/* 1. 导航栏基础结构 */
header {
    width: 100vw;
    display: flex;
    justify-content: center;
    background: #ffffff;
}
header .navbar {
    position: relative;
    width: 100%;
    max-width: 100%;
    min-height: 200px;
    padding: 0;
    margin: 0 auto;
    background: #ffffff;
    z-index: 1000;
    display: flex;
    align-items: center;
    border: none;
}

.navbar .container {
    position: static;
    height: 100%;
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0 auto;
    display: flex;
    align-items: center;
}

/* 2. Logo 样式 */
.navbar-brand {
    position: absolute !important;
    left: 260px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    display: block !important;
    padding: 0 !important;
    margin: 0 !important;
    z-index: 2;
    height: auto !important;
    display: flex;
    align-items: center;
}
.navbar-brand img {
    width: 165.7px !important;
    height: 42.26px !important;
    object-fit: contain !important;
    display: block !important;
    opacity: 1;
    margin: 0 !important;
    padding: 0 !important;
}

/* 3. 导航项样式 */
.navbar-nav {
    float: none;
    margin: 0;
    padding: 0;
    height: 200px;
    display: flex;
    align-items: center;
}
.navbar-nav > li {
    display: flex;
    align-items: center;
    height: 100%;
}

.navbar .nav > li > a {
    position: relative;
    font-family: "Source Han Sans CN-Regular", "Source Han Sans CN", Arial, sans-serif;
    font-weight: 400;
    font-size: 24px;
    color: #323232;
    line-height: 29px;
    text-align: left;
    padding: 0 15px;
    text-decoration: none;
    text-transform: none;
    white-space: nowrap;
    background: transparent;
    border: 1px solid rgba(255,255,255,0);
    margin: 0 2px;
    display: inline-block;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* 4. 导航项定位（按需调整left值） */
.navbar .nav > li > a[href*="product"] { left: 600px; width: 96px; position: absolute; top: 50%; transform: translateY(-50%); text-align: center; }
.navbar .nav > li > a[href*="download"] { left: 768px; width: 96px; position: absolute; top: 50%; transform: translateY(-50%); text-align: center; }
.navbar .nav > li > a[href*="bearkey"] { left: 936px; width: 96px; position: absolute; top: 50%; transform: translateY(-50%); text-align: center; }
.navbar .nav > li > a[href*="wiki"] { left: 1104px; width: 96px; position: absolute; top: 50%; transform: translateY(-50%); text-align: center; }
.navbar .nav > li > a[href*="shop"] { left: 1272px; width: 48px; position: absolute; top: 50%; transform: translateY(-50%); text-align: center; }

/* 5. 悬停与激活状态（统一写法） */
.navbar .nav > li > a:hover,
.navbar .nav > li > a:focus,
.navbar .nav > li.active > a,
.navbar .nav > li.active > a:hover,
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
    background: #055999 !important;
    color: #ffffff !important;
    transition: all 0.3s ease;
    z-index: 1000;
}

/* 6. 活动状态 */
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
    color: #FFFFFF !important;
    background: #055999 !important;
    border-radius: 0;
}

/* 7. 语言切换按钮及下拉 */
.navbar .nav > li.lang-switcher-container {
    position: absolute;
    left: 1556.57px;
    top: 88px;
    height: 24.14px;
    width: 24.14px;
}
.navbar .nav > li > a#lang-switcher-link .lang-icon {
    position: absolute;
    left: 1556.57px;
    top: 88px;
    width: 24.14px;
    height: 24.14px;
    display: block;
    z-index: 1001;
    cursor: pointer;
}
.navbar .nav > li > a#lang-switcher-link .lang-text {
    position: absolute;
    left: 1591.18px;
    top: 85px;
    font-family: "Source Han Sans CN-Regular", "Source Han Sans CN";
    font-weight: 400;
    font-size: 21px;
    color: #333333;
    display: block;
    z-index: 1001;
    cursor: pointer;
}
.navbar .nav > li > a#lang-switcher-link .lang-arrow {
    position: absolute;
    left: 1643.65px;
    top: 97px;
    width: 10.47px;
    height: 5.23px;
    border-left: 5.23px solid transparent;
    border-right: 5.23px solid transparent;
    border-top: 5.23px solid #333333;
    display: block;
    z-index: 1001;
    cursor: pointer;
    transition: transform 0.3s ease;
}
.navbar .nav > li > a#lang-switcher-link:hover .lang-arrow {
    border-top-color: #ffffff;
    transform: rotate(180deg);
}
.navbar .nav > li > a#lang-switcher-link + .lang-switch {
    display: none;
    position: absolute;
    left: 1554px;
    top: 125px;
    width: 106px;
    height: 45px;
    background: #FFFFFF;
    border: 1px solid #707070;
    z-index: 10000;
}
.navbar .nav > li > a#lang-switcher-link + .lang-switch.show {
    display: block;
}
.lang-switch .lang-option {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 21px;
    color: #323232;
    font-family: "Source Han Sans CN-Regular", "Source Han Sans CN";
    font-weight: 400;
    line-height: 29px;
    cursor: pointer;
    white-space: nowrap;
}

/* 8. 响应式适配 */
@media (max-width: 768px) {
    .navbar .nav > li.lang-switcher-container,
    .navbar .nav > li > a#lang-switcher-link,
    .navbar .nav > li > a#lang-switcher-link .lang-icon,
    .navbar .nav > li > a#lang-switcher-link .lang-text,
    .navbar .nav > li > a#lang-switcher-link .lang-arrow {
        position: relative;
        left: auto;
        top: auto;
        margin: 0 5px;
    }
    .navbar .nav > li > a#lang-switcher-link + .lang-switch {
        position: relative;
        left: 0;
        top: 100%;
        width: 100%;
        text-align: center;
    }
    .navbar .nav > li > a#lang-switcher-link + .lang-switch .lang-option {
        position: relative;
        left: 0;
        top: 0;
        text-align: center;
    }
    .navbar-brand {
        left: auto !important;
        top: auto !important;
        position: relative !important;
        margin: 15px;
    }
    .navbar-nav {
        height: auto;
        flex-direction: column;
        align-items: flex-start;
    }
    .navbar .nav > li > a {
        padding: 10px 15px;
        border-bottom: 1px solid #f0f0f0;
        width: 100%;
        position: relative;
        left: auto;
        top: auto;
        margin: 0;
    }
    .navbar .nav > li > a[href*="product"],
    .navbar .nav > li > a[href*="download"],
    .navbar .nav > li > a[href*="bearkey"],
    .navbar .nav > li > a[href*="wiki"],
    .navbar .nav > li > a[href*="shop"] {
        position: relative;
        left: auto;
        top: auto;
        width: 100%;
        transform: none;
    }
}

/* 9. 伪元素hover背景扩展（可选） */
.navbar .nav > li > a:before {
    content: '';
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -15px;
    right: -15px;
    background: transparent;
    z-index: -1;
    transition: all 0.3s ease;
    border-radius: 4px;
}
.navbar .nav > li > a:hover:before,
.navbar .nav > li > a:focus:before,
.navbar .nav > li.active > a:before {
    background: #055999;
} 