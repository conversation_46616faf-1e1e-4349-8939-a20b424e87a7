---
title: Classes
subTitle: API
nav: docs
description: Owl Carousel Documentation

sort: 2
tags:
- API
---

{{#markdown }}
## Classes

This is an example of how html is rendered. With the following options you can change almost every class the way you want


```
<div class="owl-carousel owl-theme owl-loaded">
    <div class="owl-stage-outer">
        <div class="owl-stage">
            <div class="owl-item">...</div>
            <div class="owl-item">...</div>
            <div class="owl-item">...</div>
        </div>
    </div>
    <div class="owl-nav">
        <div class="owl-prev">prev</div>
        <div class="owl-next">next</div>
    </div>
    <div class="owl-dots">
        <div class="owl-dot active"><span></span></div>
        <div class="owl-dot"><span></span></div>
        <div class="owl-dot"><span></span></div>
    </div>
</div>
```
### Options

{{#each classes}}
#### {{name}}

Type: `{{type}}`<br />
Default: `{{Default}}`

{{desc}}

------
{{/each}}



### Next Step

####[Events](api-events.html)

{{/markdown }}

