---
title: Auto Height Demo
subTitle: Auto Height
nav: demos
description: autoHeight usage demo
sort: 5

tags:
- demo
- plugin
---

<div class="owl-carousel owl-theme">
	<div class="item" style="height:300px"><h4>1</h4></div>
	<div class="item" style="height:100px"><h4>2</h4></div>
	<div class="item" style="height:500px"><h4>3</h4></div>
	<div class="item" style="height:250px"><h4>4</h4></div>
	<div class="item" style="height:400px"><h4>5</h4></div>
	<div class="item" style="height:500px"><h4>6</h4></div>
	<div class="item" style="height:600px"><h4>7</h4></div>
	<div class="item" style="height:400px"><h4>8</h4></div>
	<div class="item" style="height:300px"><h4>9</h4></div>
	<div class="item" style="height:350px"><h4>10</h4></div>
	<div class="item" style="height:200px"><h4>11</h4></div>
	<div class="item" style="height:150px"><h4>12</h4></div>
</div>

{{#markdown }}
### Overview

To enable use `autoHeight: true`. At the moment works only with 1 item on screen.
The plan is to calculate all visible items and change height according to heighest item.

```
//default settings:
AutoHeight.Defaults = {
	autoHeight: false,
	autoHeightClass: 'owl-height'
};
```


### Setup
```
$('.owl-carousel').owlCarousel({
	items:1,
	margin:10,
	autoHeight:true
});

```

{{/markdown }}

<script>
$(document).ready(function(){
	$('.owl-carousel').owlCarousel({
		items:1,
		margin:10,
		autoHeight:true
	});
})
</script>
