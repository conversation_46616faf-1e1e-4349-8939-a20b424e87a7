<!DOCTYPE html>
<html lang="en" style="height:100%">
<head>
    <meta charset="utf-8">
    <title>Xiamen Bearkey Technology Co., Ltd. - Official Website</title>
    <meta name="keywords" content="RK3568 ，DATAACOUISITION GATEWAY，RK3568 DATAACOUISITION GATEWAY">
    <link rel="icon" href="../images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="RK3568 ，DATAACOUISITION GATEWAY，RK3568 DATAACOUISITION GATEWAY"/>
    <meta name="author" content=""/>
    <!-- css -->
    <link rel="stylesheet" href="../simple-line-icons/css/simple-line-icons.css">
    <link href="../css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="../css/flexslider.css" rel="stylesheet"/>
    <link href="../css/magnific-popup.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet"/>
    <link href="../css/gallery-1.css" rel="stylesheet">
    <link href="/css/nav-style.css" rel="stylesheet"/>
    <!-- Font Awesome icon library -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body style="height:100%;overflow-x:hidden;" class="chinese-version">
<div id="wrapper" style="height:100%">

    <!-- start header -->
    <header>
        <!-- Navigation will be loaded dynamically by nav.js -->
    </header><!-- end header -->
    
    <section id="content" class="product_detail_cls" >
        <div>
            <div class="row" style="margin-bottom: 0px;margin-top: -5em;">
            <hr/>
                <div >
            
                
                    <section id="product_info" style="display: block">
                        <div id="product_detail">
                        <!-- Product details will be loaded dynamically via AJAX -->
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="../js/jquery.js"></script>
<script src="../js/jquery.easing.1.3.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/jquery.fancybox.pack.js"></script>
<script src="../js/jquery.fancybox-media.js"></script>
<script src="../js/portfolio/jquery.quicksand.js"></script>
<script src="../js/portfolio/setting.js"></script>
<script src="../js/jquery.flexslider.js"></script>
<script src="../js/jquery.isotope.min.js"></script>
<script src="../js/jquery.magnific-popup.min.js"></script>
<script src="../js/animate.js"></script>
<script src="../js/custom.js"></script>
<script src="../js/tools.js"></script>
<script src="../js/nav.js"></script>
<script src="../js/footer.js"></script>
<script src="../js/floating-toolbar.js"></script>
<script src="../js/toolbar-data.js"></script>
<script>
// 英文产品类型名称
var productTypeEn = "RK3568 DATA ACQUISITION GATEWAY";

// Execute when page is loaded
$(document).ready(function() {
    // Load product details
    loadProductDetail();
});

// Custom language switch function
function chg_lang() {
    // Switch to Chinese version
    window.location.href = "RK3568数据采集网关.html";
}

// Load product details
function loadProductDetail() {
    $.ajax({
        type: "get",
        url: "/apis/product_detail_list/",
        data: {
            filters: JSON.stringify({
                info_type: productTypeEn,  // 使用英文产品类型作为筛选条件
                lang: 1,  // 英文语言筛选
                show: 1   // 只显示开启显示的内容
            })
        },
        success: function(response) {
            if (response.status === 'ok' && response.data && response.data.length > 0) {
                console.log('📊 Retrieved ' + response.data.length + ' product detail records from database');

                // Sort by display order (ascending)
                var productImages = response.data.sort(function(a, b) {
                    return a.display_order - b.display_order;
                });

                // Generate product details HTML
                var detailHtml = '';
                productImages.forEach(function(item) {
                    // Check show status, only display items with show set to 1
                    if (item.image_path && item.show === 1) {
                        detailHtml += '<p><img src="' + item.image_path + '" alt="' + productTypeEn + '" width="100%" /></p>';
                    }
                });

                // Update product details content
                $('#product_detail').html(detailHtml);

                // Start waiting for images to load
                waitForImagesLoad();
            } else {
                console.error('No product details found');
                $('#product_detail').html('<p>No product details available</p>');
                // Load footer immediately if no images
                loadFooter();
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load product details:', error);
            $('#product_detail').html('<p>Failed to load product details</p>');
            // Load footer immediately on error
            loadFooter();
        }
    });
}

// Wait for all images to load
function waitForImagesLoad() {
    var images = $('#product_detail img');
    var totalImages = images.length;
    var loadedImages = 0;

    console.log('🖼️ Need to load ' + totalImages + ' images');

    if (totalImages === 0) {
        console.log('✅ No images to load, loading footer directly');
        loadFooter();
        return;
    }

    console.log('🔄 Starting to wait for ' + totalImages + ' images to complete loading...');

    images.each(function(index) {
        var img = $(this)[0];

        // If image is already loaded
        if (img.complete && img.naturalHeight !== 0) {
            loadedImages++;
            console.log('✅ Image ' + (index + 1) + '/' + totalImages + ' already loaded (cached)');
            checkAllImagesLoaded();
        } else {
            // Listen for image load events
            $(this).on('load', function() {
                loadedImages++;
                console.log('✅ Image ' + (index + 1) + '/' + totalImages + ' loaded');
                checkAllImagesLoaded();
            }).on('error', function() {
                loadedImages++;
                console.log('❌ Image ' + (index + 1) + '/' + totalImages + ' failed to load');
                checkAllImagesLoaded();
            });
        }
    });

    function checkAllImagesLoaded() {
        console.log('📈 Image loading progress: ' + loadedImages + '/' + totalImages);
        if (loadedImages === totalImages) {
            console.log('🎉 All images loaded, adding images-loaded class');
            // Add images-loaded class to allow footer display
            document.body.classList.add('images-loaded');
            console.log('🔄 Starting to load footer');
            loadFooter();
        }
    }
}

function loadFooter() {
    console.log('🔄 Starting to insert footer after last image...');

    // Find all product detail images
    var allImages = $('#product_detail img');
    console.log('🖼️ Found image count:', allImages.length);

    // Find the last image
    var lastImage = allImages.last();
    var insertTarget;

    if (lastImage.length > 0) {
        // Find the outermost container of the last image
        var imageContainer = lastImage.closest('.col-md-6, .col-sm-6, .col-xs-12, .product-item, div');
        if (imageContainer.length > 0) {
            insertTarget = imageContainer;
            console.log('📍 Will insert footer after last image container');
        } else {
            insertTarget = lastImage;
            console.log('📍 Will insert footer after last image element');
        }
    } else {
        // If no images, insert at end of product detail area
        insertTarget = $('#product_detail');
        console.log('📍 No images found, will insert footer at end of product detail area');
    }

    // Remove existing footer if present
    if ($('footer').length > 0) {
        console.log('🗑️ Removing existing footer');
        $('footer').remove();
    }

    // Insert footer at correct position
    insertTarget.after('<footer></footer>');
    console.log('📌 Footer inserted at correct position');

    var showFooter = function() {
        if (typeof window.initFooter === 'function') {
            console.log('📝 Starting to initialize footer content...');
            window.initFooter();
            console.log('✨ Footer content loaded');
        }
    };

    if (!window.initFooter) {
        console.log('📦 Starting to load footer script...');
        var script = document.createElement('script');
        script.src = '../js/footer.js';
        script.onload = showFooter;
        document.body.appendChild(script);
    } else {
        showFooter();
    }
}
</script>
</body>
</html>