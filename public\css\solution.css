/* 防止横向滚动条 */
body {
    overflow-x: hidden;
}

/* 页面标题背景板 */
.page-title-banner {
    width: 100%;
    height: 6.25vw; /* 120px */
    background: #F8F8F8;
    border-radius: 0;
    display: flex;
    align-items: center;
    margin-bottom: 2.5vw; /* 48px */
}

.page-title {
    font-family: "Source Han Sans CN", "Source Han Sans CN", sans-serif;
    font-weight: 500;
    font-size: 2.29vw; /* 44px */
    color: #00509E;
    line-height: 4.43vw; /* 85px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0;
}

/* 面包屑导航样式 */
.breadcrumb {
    margin: 0;
    padding: 0;
    background: none;
    display: flex;
    align-items: center;
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 0.83vw; /* 16px */
    line-height: 1.51vw; /* 29px */
    font-style: normal;
    text-transform: none;
}

.breadcrumb a {
    color: #333333;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #055999;
}

.breadcrumb .separator {
    margin: 0 0.42vw; /* 8px */
    color: #333333;
}

.breadcrumb .current {
    color: #333333;
}

/* 内容区域样式 */
.solution-content {
    padding: 0;
    max-width: 83.33vw; /* 1600px */
    margin: 0 auto;
}

.section-title {
    font-family: "Source Han Sans CN", sans-serif;
    font-size: 1.67vw; /* 32px */
    color: #323232;
    line-height: 4.43vw; /* 85px */
    font-weight: 500;
    font-style: normal;
    text-transform: none;
    margin: 0;
    text-align: center;
}

/* 方案定制卡片区域样式 */
.solution-cards {
    display: flex;
    justify-content: center;
    gap: 3.125vw; /* 60px */
    margin: 0 auto;
    max-width: 72.92vw; /* 1400px */
    margin-top: 2.55vw; /* 49px */
    align-items: flex-start; /* 改为顶部对齐 */
}

.solution-card {
    width: 18.75vw; /* 360px */
    height: auto;
    min-height: fit-content; /* 确保高度完全自适应内容 */
    border-radius: 1.46vw; /* 28px */
    background: #F8F8F8;
    border: 0.104vw solid #0050A2; /* 2px */
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 移除第二个卡片的特殊高度设置 */
.solution-card:nth-child(2) {
    height: auto;
    min-height: fit-content;
}

.card-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 第一个卡片的图片特殊尺寸和边距 */
.solution-card:first-child .card-icon {
    width: 11.15vw; /* 214px */
    height: 7.76vw; /* 149px */
    margin-top: 2.08vw; /* 40px */
    margin-bottom: 1.15vw; /* 22px */
    background: #0050A2;
    border-radius: 0.16vw; /* 3px */
}

/* 第二个卡片的图片特殊尺寸和边距 */
.solution-card:nth-child(2) .card-icon {
    width: 9.06vw; /* 173.95px */
    height: 8.23vw; /* 158.03px */
    margin-top: 1.61vw; /* 31px */
    margin-bottom: 1.15vw; /* 22px */
}

/* 第三个卡片的图标尺寸 */
.solution-card:nth-child(3) .card-icon {
    width: 13.25vw; /* 254.49px */
    height: 8.65vw; /* 166.1px */
    margin-top: 1.20vw; /* 23px */
    margin-bottom: 1.15vw; /* 22px */
}

.solution-card:nth-child(3) .card-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.card-title {
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 1.46vw; /* 28px */
    color: #323232;
    line-height: normal; /* 85px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 0.94vw; /* 18px */
    margin-top: 0;
}

/* 英文界面卡片标题样式 */
html[lang="en"] .card-title {
    font-size: 1.25vw; /* 24px */
    line-height: 1.25vw; /* 24px */
    letter-spacing: -0.02em;
}

.card-description {
    font-family: "Source Han Sans CN", "Source Han Sans CN";
    font-weight: 400;
    font-size: 0.83vw; /* 16px */
    color: #333333;
    line-height: 1.46vw; /* 28px */
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
}

/* 第一个卡片的描述文本特殊边距 */
.solution-card:first-child .card-description {
    margin-left: 1.46vw; /* 28px */
    margin-right: 1.46vw; /* 28px */
    margin-bottom: 1.77vw; /* 34px */
}

/* 第二个卡片的描述文本特殊边距 */
.solution-card:nth-child(2) .card-description {
    margin-left: 1.46vw; /* 28px */
    margin-right: 1.46vw; /* 28px */
    margin-bottom: 1.04vw; /* 20px */
}

/* 第三个卡片的描述文本特殊边距 */
.solution-card:nth-child(3) .card-description {
    margin-left: 1.46vw; /* 28px */
    margin-right: 1.46vw; /* 28px */
    margin-bottom: 1.61vw; /* 31px */
}

/* 英文界面第三个卡片描述文本的特殊样式 */
html[lang="en"] .solution-card:nth-child(3) .card-description {
    line-height: 1.04vw; /* 20px */
}

/* 商业定制模块样式 */
.business-customize {
    margin-top: 4.48vw; /* 86px */
    margin-bottom: 2.5vw; /* 48px */
    width: 62.5vw; /* 1200px */
    margin-left: auto;
    margin-right: auto;
}

.business-title {
    font-family: "Source Han Sans CN", "Source Han Sans CN", sans-serif;
    font-weight: 400;
    font-size: 1.67vw; /* 32px */
    color: #323232;
    line-height: 4.43vw; /* 85px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0;
    margin-bottom: 1.93vw; /* 37px */
}

.customize-form {
    width: 62.5vw; /* 1200px */
}

.form-row {
    display: flex;
    gap: 1.04vw; /* 20px */
    margin-bottom: 1.04vw; /* 20px */
    flex-wrap: wrap;
}

/* 第一行输入框容器样式 */
.form-row:first-of-type {
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    gap: 0.83vw; /* 16px */
    margin-bottom: 1.41vw; /* 27px */
    width: 62.5vw; /* 1200px */
}

.form-row input {
    flex: 1;
    height: 2.5vw; /* 48px */
    padding: 0 1.04vw; /* 20px */
    border: 0.052vw solid #DCDCDC; /* 1px */
    border-radius: 0.21vw; /* 4px */
    font-size: 0.83vw; /* 16px */
    color: #333333;
}

/* 第二行输入框容器样式 */
.form-row:nth-of-type(2) {
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    gap: 0.99vw; /* 19px */
    margin-bottom: 1.77vw; /* 34px */
    width: 62.5vw; /* 1200px */
}

.form-row input {
    flex: 1;
    height: 2.5vw; /* 48px */
    padding: 0 1.04vw; /* 20px */
    border: 0.052vw solid #DCDCDC; /* 1px */
    border-radius: 0.21vw; /* 4px */
    font-size: 0.83vw; /* 16px */
    color: #333333;
}

.form-row .company-input {
    flex: 2;
}

.form-row .type-input {
    flex: 1;
}

/* 第三行（定制需求）容器样式 */
.form-row:nth-of-type(3) {
    width: 62.5vw; /* 1200px */
}

/* 定制需求文本框特殊样式 */
.form-row textarea {
    width: 62.5vw; /* 1200px */
    height: 23.96vw; /* 460px */
    background: #FFFFFF;
    border-radius: 0.42vw; /* 8px */
    border: 0.052vw solid #707070; /* 1px */
    padding: 0vw 1.04vw 1.04vw 1.04vw; /* 0px 20px 20px 20px */
    padding-top: 0.83vw; /* 16px */
    resize: none;
    box-sizing: border-box;
    display: block;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 定制需求文本框的占位符文字样式 */
.form-row textarea::placeholder {
    font-family: "Source Han Sans CN", "Source Han Sans CN", sans-serif;
    font-weight: 400;
    font-size: 1.04vw; /* 20px */
    color: #323232;
    line-height: 1.04vw; /* 20px */
    text-align: left;
    font-style: normal;
    text-transform: none;
    position: relative;
    top: -1.56vw; /* -30px */
}

/* 定制需求文本框滚动条样式 */
.form-row textarea::-webkit-scrollbar {
    width: 0.42vw; /* 8px */
}

.form-row textarea::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0.21vw; /* 4px */
}

.form-row textarea::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 0.21vw; /* 4px */
}

.form-row textarea::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 提交按钮容器样式 */
.form-submit {
    text-align: right;
    margin-top: 2.08vw; /* 40px */
    margin-bottom: 4.375vw; /* 84px */
}

.form-submit button {
    width: 10vw; /* 192px */
    height: 3.125vw; /* 60px */
    background: #007DDB;
    border: none;
    border-radius: 0.625vw; /* 12px */
    font-family: "Source Han Sans CN", "Source Han Sans CN", sans-serif;
    font-weight: 400;
    font-size: 1.46vw; /* 28px */
    color: #FFFFFF;
    line-height: 3.125vw; /* 60px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
    padding: 0;
}

.form-submit button:hover {
    background: #0069b9;  /* 保持hover效果但使用相近的深色 */
}

/* 输入框焦点状态 */
.form-row input:focus,
.form-row textarea:focus {
    outline: none;
    border-color: #0050A2 !important; /* 添加!important确保覆盖其他样式 */
}

/* 所有输入框的通用样式 */
.form-row input,
.form-row textarea {
    font-family: "Source Han Sans CN", "Source Han Sans CN", sans-serif;
    font-weight: 400;
    font-size: 1.04vw; /* 20px */
    color: #323232;
    line-height: 1.2;
    font-style: normal;
    text-transform: none;
}

/* 所有输入框的占位符文本样式 */
.form-row input::placeholder,
.form-row textarea::placeholder {
    font-family: "Source Han Sans CN", "Source Han Sans CN", sans-serif;
    font-weight: 400;
    font-size: 1.04vw; /* 20px */
    color: #323232;
    line-height: 4.43vw; /* 85px */
    text-align: left;
    font-style: normal;
    text-transform: none;
}

/* 姓名输入框特殊样式 */
.form-row input[placeholder="姓名（必填）"],
.form-row input[placeholder="Name (Required)"] {
    width: 17.55vw; /* 337px */
    height: 2.5vw; /* 48px */
    background: #FFFFFF;
    border-radius: 0.42vw; /* 8px */
    border: 0.052vw solid #707070; /* 1px */
    flex: none;
    transition: all 0.3s ease;
}

/* 联系手机输入框特殊样式 */
.form-row input[placeholder="联系手机（必填）"],
.form-row input[placeholder="Phone (Required)"] {
    width: 20.31vw; /* 390px */
    height: 2.5vw; /* 48px */
    background: #FFFFFF;
    border-radius: 0.42vw; /* 8px */
    border: 0.052vw solid #707070; /* 1px */
    flex: none;
    transition: all 0.3s ease;
}

/* 邮箱地址输入框特殊样式 */
.form-row input[placeholder="邮箱地址（必填）"],
.form-row input[placeholder="Email (Required)"] {
    width: 23.02vw; /* 442px */
    height: 2.5vw; /* 48px */
    background: #FFFFFF;
    border-radius: 0.42vw; /* 8px */
    border: 0.052vw solid #707070; /* 1px */
    flex: none;
    transition: all 0.3s ease;
}

/* 公司输入框特殊样式 */
.form-row .company-input {
    width: 30.73vw; /* 590px */
    height: 2.5vw; /* 48px */
    background: #FFFFFF;
    border-radius: 0.42vw; /* 8px */
    border: 0.052vw solid #707070; /* 1px */
    flex: none;
}

/* 定制类型输入框特殊样式 */
.form-row .type-input {
    width: 30.73vw; /* 590px */
    height: 2.5vw; /* 48px */
    background: #FFFFFF;
    border-radius: 0.42vw; /* 8px */
    border: 0.052vw solid #707070; /* 1px */
    flex: none;
}

/* 小屏幕适配 (491px以下) */
@media screen and (max-width: 490px) {
    /* 方案定制卡片区域样式 */
    .solution-cards {
        display: flex;
        justify-content: flex-start; /* 改为左对齐 */
        gap: 5px; /* 设置卡片间距 */
        margin: 0;
        margin-top: 19.6px;
        padding: 0 0px;
        width: 100%;
        box-sizing: border-box;
        max-width: none;
        margin-left: -18px;

    }

    .solution-card {
        width: calc(36%); /* 总宽度减去两个间隔(5px * 2)，再三等分 */
        height: 172.8px;
        border-radius: 11.2px;
        border: 0.8px solid #0050A2;
        box-sizing: border-box;
        margin: 0;
        flex-shrink: 0;
    }

    /* 软件定制卡片特殊样式 */
    .solution-card:nth-child(2) {
        height: 231.6px;
    }

    .card-icon {
        margin-bottom: 4px;
    }

    /* 第一个卡片的图片特殊尺寸和边距 */
    .solution-card:first-child .card-icon {
        width: 79.34px; /* 198.35px * 0.4 */
        height: 55.01px; /* 137.53px * 0.4 */
        margin-top: 22.8px; /* 57px * 0.4 */
        margin-bottom: 4px;
    }

    /* 第二个卡片的图片特殊尺寸和边距 */
    .solution-card:nth-child(2) .card-icon {
        width: 65.88px; /* 164.69px * 0.4 */
        height: 59.84px; /* 149.61px * 0.4 */
        margin-top: 22px; /* 55px * 0.4 */
        margin-bottom: 4px;
    }

    /* 第三个卡片的图标尺寸 */
    .solution-card:nth-child(3) .card-icon {
        width: 90px; /* 263.99px * 0.4 */
        height: 68.92px; /* 172.31px * 0.4 */
        margin-top: 12px; /* 30px * 0.4 */
        margin-bottom: 4px;
    }

    .card-title {
        font-size: 10px;
        line-height: 12.8px; /* 32px * 0.4 */
        margin-bottom: 2px;
    }

    /* 英文界面卡片标题样式 */
    html[lang="en"] .card-title {
        font-size: 9.6px; /* 24px * 0.4 */
        line-height: 9.6px; /* 24px * 0.4 */
    }

    .card-description {
        font-size: 1px; /* 调整后的字体大小 */
        line-height: 1;
    }

    /* 第一个卡片的描述文本特殊边距 */
    .solution-card:first-child .card-description {
        margin-left: 0; /* 移除左边距 */
        margin-right: 0; /* 移除右边距 */
    }

    /* 第二个卡片的描述文本特殊边距 */
    .solution-card:nth-child(2) .card-description {
        margin-left: 0; /* 移除左边距 */
        margin-right: 0; /* 移除右边距 */
    }

    /* 第三个卡片的描述文本特殊边距 */
    .solution-card:nth-child(3) .card-description {
        margin-left: 0; /* 移除左边距 */
        margin-right: 0; /* 移除右边距 */
    }

    /* 英文界面第三个卡片描述文本的特殊样式 */
    html[lang="en"] .solution-card:nth-child(3) .card-description {
        line-height: 8px; /* 20px * 0.4 */
    }

    /* 商业定制模块样式 */
    .business-customize {
        margin-top: 21.5px; /* 86px * 0.25 */
        margin-bottom: 12px; /* 48px * 0.25 */
        width: 300px; /* 1200px * 0.25 */
        margin-left: auto;
        margin-right: auto;
    }

    .business-title {
        font-size: 8px; /* 32px * 0.25 */
        line-height: 21.25px; /* 85px * 0.25 */
        margin-bottom: 9.25px; /* 37px * 0.25 */
    }

    .customize-form {
        width: 300px; /* 1200px * 0.25 */
    }

    .form-row {
        gap: 5px; /* 20px * 0.25 */
        margin-bottom: 5px; /* 20px * 0.25 */
    }

    /* 第一行输入框容器样式 */
    .form-row:first-of-type {
        gap: 4px; /* 16px * 0.25 */
        margin-bottom: 6.75px; /* 27px * 0.25 */
        width: 300px; /* 1200px * 0.25 */
    }

    .form-row input {
        height: 12px; /* 48px * 0.25 */
        padding: 0 5px; /* 20px * 0.25 */
        border: 0.25px solid #DCDCDC; /* 1px * 0.25 */
        border-radius: 1px; /* 4px * 0.25 */
        font-size: 4px; /* 16px * 0.25 */
    }

    /* 第二行输入框容器样式 */
    .form-row:nth-of-type(2) {
        gap: 4.75px; /* 19px * 0.25 */
        margin-bottom: 8.5px; /* 34px * 0.25 */
        width: 300px; /* 1200px * 0.25 */
    }

    /* 第三行（定制需求）容器样式 */
    .form-row:nth-of-type(3) {
        width: 300px; /* 1200px * 0.25 */
    }

    /* 姓名输入框特殊样式 */
    .form-row input[placeholder="姓名（必填）"],
    .form-row input[placeholder="Name (Required)"] {
        width: 84.25px; /* 337px * 0.25 */
        height: 12px; /* 48px * 0.25 */
        border-radius: 2px; /* 8px * 0.25 */
        border: 0.25px solid #707070; /* 1px * 0.25 */
    }

    /* 联系手机输入框特殊样式 */
    .form-row input[placeholder="联系手机（必填）"],
    .form-row input[placeholder="Phone (Required)"] {
        width: 97.5px; /* 390px * 0.25 */
        height: 12px; /* 48px * 0.25 */
        border-radius: 2px; /* 8px * 0.25 */
        border: 0.25px solid #707070; /* 1px * 0.25 */
    }

    /* 邮箱地址输入框特殊样式 */
    .form-row input[placeholder="邮箱地址（必填）"],
    .form-row input[placeholder="Email (Required)"] {
        width: 110.5px; /* 442px * 0.25 */
        height: 12px; /* 48px * 0.25 */
        border-radius: 2px; /* 8px * 0.25 */
        border: 0.25px solid #707070; /* 1px * 0.25 */
    }

    /* 公司输入框特殊样式 */
    .form-row .company-input {
        width: 147.5px; /* 590px * 0.25 */
        height: 12px; /* 48px * 0.25 */
        border-radius: 2px; /* 8px * 0.25 */
        border: 0.25px solid #707070; /* 1px * 0.25 */
    }

    /* 定制类型输入框特殊样式 */
    .form-row .type-input {
        width: 147.5px; /* 590px * 0.25 */
        height: 12px; /* 48px * 0.25 */
        border-radius: 2px; /* 8px * 0.25 */
        border: 0.25px solid #707070; /* 1px * 0.25 */
    }

    /* 定制需求文本框特殊样式 */
    .form-row textarea {
        width: 300px; /* 1200px * 0.25 */
        height: 115px; /* 460px * 0.25 */
        border-radius: 2px; /* 8px * 0.25 */
        border: 0.25px solid #707070; /* 1px * 0.25 */
        padding: 4px 5px 5px 5px; /* 16px 20px 20px 20px * 0.25 */
        font-size: 5px; /* 20px * 0.25 */
    }

    /* 定制需求文本框的占位符文字样式 */
    .form-row textarea::placeholder {
        font-size: 5px; /* 20px * 0.25 */
        line-height: 5px; /* 20px * 0.25 */
        top: 2.5px; /* -30px * 0.25 */
    }

    /* 定制需求文本框滚动条样式 */
    .form-row textarea::-webkit-scrollbar {
        width: 2px; /* 8px * 0.25 */
    }

    .form-row textarea::-webkit-scrollbar-track {
        border-radius: 1px; /* 4px * 0.25 */
    }

    .form-row textarea::-webkit-scrollbar-thumb {
        border-radius: 1px; /* 4px * 0.25 */
    }

    /* 提交按钮容器样式 */
    .form-submit {
        margin-top: 10px; /* 40px * 0.25 */
        margin-bottom: 21px; /* 84px * 0.25 */
    }

    .form-submit button {
        width: 48px; /* 192px * 0.25 */
        height: 15px; /* 60px * 0.25 */
        border-radius: 3px; /* 12px * 0.25 */
        font-size: 7px; /* 28px * 0.25 */
        line-height: 15px; /* 60px * 0.25 */
    }
}

