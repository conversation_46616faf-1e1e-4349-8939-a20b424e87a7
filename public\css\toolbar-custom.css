.floating-toolbar {
    position: fixed;
    top: 50%; /* 默认垂直居中 */
    transform: translateY(-50%); /* 精确垂直居中 */
    right: 16px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.floating-toolbar .toolbar-btn {
    width: 64px;
    height: 64px;
    background: #0050A2;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px; /* 增加间距 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.2); /* 增强阴影 */
    cursor: pointer;
    transition: all 0.3s ease; /* 平滑过渡 */
}

.floating-toolbar .toolbar-btn:last-child {
    margin-bottom: 0;
}

.floating-toolbar .toolbar-btn:hover {
    background: #003f7d;
    transform: translateX(-5px); /* 悬停效果 */
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.floating-toolbar .toolbar-btn svg,
.floating-toolbar .toolbar-btn img {
    width: 32px;
    height: 32px;
    display: block;
    filter: brightness(0) invert(1); /* 确保图标为白色 */
}

/* 添加一个淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.floating-toolbar {
    animation: fadeIn 0.5s ease-in-out;
}

@media (max-width: 767px) {
    .floating-toolbar {
        right: 8px;
        top: 50%;
    }
    .floating-toolbar .toolbar-btn {
        width: 56px;
        height: 56px;
        margin-bottom: 6px;
    }
    .floating-toolbar .toolbar-btn svg,
    .floating-toolbar .toolbar-btn img {
        width: 28px;
        height: 28px;
    }
} 