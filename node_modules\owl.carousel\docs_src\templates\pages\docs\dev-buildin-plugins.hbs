---
title: Built-in Plugins
subTitle: Development
nav: docs
description: Owl Carousel Documentation

sort: 1
tags:
- Development
---

{{#markdown }}
## Built-in Plugins
> Owl Carousel supports plugin modular structure. This means that you can remove plugins that you won’t use or create new ones that fit your needs



### Plugins included

Following javascript files are concatenated in distributed owl.carousel.js and owl.carousel.min.js version. If you are looking for individual scripts please visit [Github]({{ pkg.homepage }}) and fork/download the source project


```
src/
└── js/
	├── owl.animate.js
    ├── owl.autoplay.js
    ├── owl.autoheight.js
    ├── owl.carousel.js
    ├── owl.hash.js
    ├── owl.lazyload.js
    ├── owl.navigation.js
    └── owl.video.js
```

> [See demos of built-in plugins](../demos/demos.html#using-built-in-plugins)

------

Big thanks to [<PERSON><PERSON>](https://github.com/witrin) for helping me with modular structure and for providing best practice with developing the code.





{{!--

## owl.lazyload.js

LazyLoad require `class="owl-lazy"` and link to image inside data: data-src="url_to_img" or/and data-src-retina="url_to_highres_img".
There is also option to load images into background but this needs to be tested. [See demo](/demos/demo.html)

Usage:

```
$('.owl-carousel').owlCarousel({
	lazyLoad:true
});
```
HTML Markup:
```
<div class="owl-carousel">
	<img class="owl-lazy" data-src="https://placehold.it/350x250&text=3" alt="">
	<img class="owl-lazy" data-src-retina="https://placehold.it/350x250&text=1-retina" data-src="https://placehold.it/350x250&text=1" alt="">
	<div class="owl-lazy" data-src-retina="https://placehold.it/350x250&text=1-retina" data-src="https://placehold.it/350x250&text=1"></div>
</div>
```

## owl.video.js

Currently support YouTube, Vimeo, and vzaar videos only.

To add video into carousel just put `<a class="owl-video" href="_straight URL from YouTube, Vimeo, or vzaar"></a>`.
A tag is not mandatory, can be any other tag but `"owl-video"` and href with url link is required.

Thumbnails are automaticaly fetched from servers. [See demo](/demos/demo.html)

#### Usage:
```
$('.owl-carousel').owlCarousel({
	video:true,
	videoHeight:200, //optional
	videoWidth: 200 //optional
});
```
#### HTML Markup:
```
<div class="owl-carousel">
	<div class="article-video"><a class="owl-video" href="https://vimeo.com/23924346"></a></div>
	<div class="article-video"><a class="owl-video" href="https://www.youtube.com/watch?v=JpxsRwnRwCQ"></a></div>
	<div class="article-video"><a class="owl-video" href="https://www.youtube.com/watch?v=FBu_jxT1PkA"></a></div>
	<div class="article-video"><a class="owl-video" href="https://www.youtube.com/watch?v=oy18DJwy5lI"></a></div>
</div>
```

## owl.autoheight.js

Adjusts stage height to highest element on viewport. No special HTML markup required. [See demo](/demos/demo.html)

#### Usage:
```
$('.owl-carousel').owlCarousel({
	autoHeight:true,
	autoHeightClass: 'owl-height' //default
});
```

## owl.animate.js

Owl Carousel CSS has built-in only fade out transition. To get more fancy looking animations you have to create them yourself in CSS or use fantastic Animate.css library created by Daniel Eden https://daneden.github.io/animate.css/
Animate functions works only with one item and only in browsers that support perspective property.

To get fade out effect just set `animateOut: 'fadeOut'`

#### Default options:
```
animateOut:false;
animateIn:false;
```
Read more on demo page. --}}

{{/markdown }}

