<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Xiamen Bearkey Technology Co., Ltd. - Official Website</title>
    <meta name="keywords" Content="Bearkey Technology, Smart Meeting System, Wireless Screen Casting, AI, Industrial Control Board, Face Recognition, Smart Vision Development, Smart Audio Module, IoT, Open Source Board, Smart Storytelling Machine, AIoT, Audio and Video Processing, RK3399Pro, RK1808, Edge Computing">
    <link rel="icon" href="images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="Bearkey Technology, Smart Meeting System, Wireless Screen Casting, AI, Industrial Control Board, Face Recognition, Smart Vision Development, Smart Audio Module, IoT, Open Source Board, Smart Storytelling Machine, AIoT, Audio and Video Processing, RK3399Pro, RK1808, Edge Computing"/>
    <meta name="author" content=""/>
    <!-- 语言标识 -->
    <meta name="language" content="en">
    <!-- css -->
    <link href="css/bootstrap.min.css" rel="stylesheet"/>
    <link href="css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="simple-line-icons/css/simple-line-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <link href="css/flexslider.css" rel="stylesheet"/>
    <link href="css/magnific-popup.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet"/>
    <link href="css/gallery-1.css" rel="stylesheet">
    <link href="css/product.css" rel="stylesheet">
    <style type="text/css">
        .active {
            color: #002e5b;
            text-decoration: none;
        }
        .no-products {
            width: 100%;
            text-align: center;
            padding: 30px;
            font-size: 18px;
            color: #646464;
        }
    </style>

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body class="en product_en">
<div id="wrapper" style="display: none">

    <!-- start header -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header>
    <section id="inner-headline">
    </section>
    
    <!-- 产品菜单背景板 -->
    <div class="editContent">
        <ul class="filter" id="product_menu">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </ul>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
        <!-- 产品展示区域 -->
        <div id="row">
            <!-- 产品列表将通过JavaScript动态生成 -->
        </div>
    </div>

    <footer style="display:none;">
        <!-- 页脚内容将通过footer.js动态加载 -->
    </footer>
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="js/jquery.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.fancybox.pack.js"></script>
<script src="js/jquery.fancybox-media.js"></script>
<script src="js/portfolio/jquery.quicksand.js"></script>
<script src="js/portfolio/setting.js"></script>
<script src="js/jquery.flexslider.js"></script>
<script src="js/jquery.isotope.min.js"></script>
<script src="js/jquery.magnific-popup.min.js"></script>
<script src="js/animate.js"></script>
<script src="js/custom.js"></script>
<script src="js/tools.js"></script>
<script src="js/nav.js"></script>
<script src="js/footer.js"></script>
<script src="js/product.js"></script>
<script src="js/floating-toolbar.js"></script>
<script src="js/toolbar-data.js"></script>
</body>
</html> 