/**
 * 产品推荐前端显示脚本
 * 根据当前网站类型（中文/英文）显示对应的产品推荐
 */

// 判断当前是否为英文网站
function isEnglishSite() {
    // 检查URL中是否包含_en.html或其他英文标识
    var isEnglish = window.location.pathname.includes('_en.html') || 
                   window.location.pathname.includes('/en/') ||
                   window.location.hostname.includes('en.') || 
                   window.location.search.includes('lang=en');
    
    console.log("页面类型检测:", {
        "URL路径": window.location.pathname,
        "是否包含_en.html": window.location.pathname.includes('_en.html'),
        "是否包含/en/": window.location.pathname.includes('/en/'),
        "是否为英文网站": isEnglish
    });
    
    return isEnglish;
}

// 获取产品推荐列表
function getProductRecommend() {
    // 根据当前网站类型确定lang参数
    var lang = isEnglishSite() ? 1 : 0;
    
    console.log("获取产品推荐，当前网站类型:", lang === 1 ? "英文" : "中文");
    
    // 清空旧的产品推荐内容（如果有）
    var oldContentContainers = document.querySelectorAll('#recmd, #product-recommend-container');
    oldContentContainers.forEach(function(container) {
        if (container) {
            console.log("清空容器:", container.id);
            container.innerHTML = '';
        }
    });
    
    // 发送请求获取产品推荐列表
    $.ajax({
        type: "post",
        url: "/apis/get_product_recommend/",
        async: true,
        data: {
            page: 1,
            page_size: 100, // 获取足够多的记录
            filters: JSON.stringify({lang: lang}) // 根据当前网站类型筛选
        },
        success: function(data) {
            console.log("获取产品推荐响应:", data);
            
            if (data.status === 'ok' && data.data_list && data.data_list.length > 0) {
                console.log("成功获取产品推荐，数量:", data.data_list.length);
                
                // 渲染产品推荐
                renderProductRecommend(data.data_list);
            } else {
                console.error("获取产品推荐失败:", data);
                // 显示错误信息
                var container = document.getElementById('product-recommend-container') || document.getElementById('recmd');
                if (container) {
                    container.innerHTML = '<div class="alert alert-warning">暂无产品推荐数据</div>';
                }
            }
        },
        error: function(xhr, status, error) {
            console.error("获取产品推荐请求失败:", status, error);
            // 显示错误信息
            var container = document.getElementById('product-recommend-container') || document.getElementById('recmd');
            if (container) {
                container.innerHTML = '<div class="alert alert-danger">加载产品推荐数据失败</div>';
            }
        }
    });
}

// 渲染产品推荐
function renderProductRecommend(products) {
    // 尝试获取产品推荐容器（兼容两种可能的容器ID）
    var container = document.getElementById('product-recommend-container') || document.getElementById('recmd');
    if (!container) {
        console.error("未找到产品推荐容器");
        return;
    }
    
    console.log("使用容器渲染产品:", container.id);
    
    // 清空容器
    container.innerHTML = '';
    
    // 根据当前语言过滤产品
    var lang = isEnglishSite() ? 1 : 0;
    var filteredProducts = products.filter(function(product) {
        return product.lang === lang;
    });
    
    console.log("过滤后的产品数量:", filteredProducts.length, "原始产品数量:", products.length);
    
    if (filteredProducts.length === 0) {
        container.innerHTML = '<div class="alert alert-info">' + 
            (lang === 1 ? 'No product recommendations available.' : '暂无产品推荐') + 
            '</div>';
        return;
    }
    
    // 按照sort_order排序
    filteredProducts.sort(function(a, b) {
        return (a.sort_order || 0) - (b.sort_order || 0);
    });
    
    // 创建产品推荐HTML
    var html = '';
    
    // 创建产品推荐行
    html += '<div class="row">';
    
    // 遍历产品
    for (var i = 0; i < filteredProducts.length; i++) {
        var product = filteredProducts[i];
        
        console.log("渲染产品:", {
            id: product.id,
            title: product.title,
            lang: product.lang,
            sort_order: product.sort_order
        });
        
        // 创建产品推荐卡片
        html += '<div class="col-md-3 col-sm-6">';
        html += '  <div class="box">';
        html += '    <span class="icons"><img src="' + product.icon + '" alt="' + product.title + '" style="max-width:80px;max-height:80px;"></span>';
        html += '    <div class="box-area">';
        html += '      <h3>' + product.title + '</h3>';
        html += '      <p class="index_desc">' + product.description + '</p>';
        if (product.url) {
            html += '    <p><a href="' + product.url + '">' + (isEnglishSite() ? 'Learn more' : '了解更多') + '</a></p>';
        }
        html += '    </div>';
        html += '  </div>';
        html += '</div>';
        
        // 每行4个产品，创建新行
        if ((i + 1) % 4 === 0 && i < filteredProducts.length - 1) {
            html += '</div><div class="row">';
        }
    }
    
    html += '</div>';
    
    // 设置容器内容
    container.innerHTML = html;
    console.log("产品推荐渲染完成");
}

// 页面加载完成后获取产品推荐
$(document).ready(function() {
    console.log("页面加载完成，开始获取产品推荐");
    
    // 确保只执行一次
    if (window.productRecommendInitialized) {
        console.log("产品推荐已初始化，跳过");
        return;
    }
    
    window.productRecommendInitialized = true;
    
    // 获取新的产品推荐
    getProductRecommend();
    
    // 监听语言切换事件（如果有）
    $(document).on('language-changed', function(event, lang) {
        console.log("检测到语言变更事件，重新加载产品推荐");
        getProductRecommend();
    });
}); 