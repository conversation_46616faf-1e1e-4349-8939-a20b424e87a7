/**
 * 方案定制页面JavaScript
 * 从数据库读取方案定制信息并填充到页面卡片中
 */

// 工具函数：判断是否为英文站点
function isEnglishSite() {
    return window.location.pathname.includes('_en.html');
}

// 工具函数：按显示顺序排序
function sortByDisplayOrder(items) {
    return [...items].sort((a, b) => 
        Number(a.display_order || 100) - Number(b.display_order || 100)
    );
}

// 工具函数：标准化图片路径
function normalizeImagePath(imgPath) {
    if (!imgPath) return '';
    
    if (imgPath.includes('C:')) {
        const fileName = imgPath.split('\\').pop().split('/').pop();
        return '/uploads/' + fileName;
    } else if (!imgPath.startsWith('http') && !imgPath.startsWith('/')) {
        return '/' + imgPath;
    }
    
    return imgPath;
}

/**
 * 获取并显示方案定制卡片数据
 */
async function fetchSolutionCards() {
    try {
        const lang = isEnglishSite() ? 1 : 0;
        console.log(`获取方案定制卡片数据，语言: ${lang === 1 ? '英文' : '中文'}`);
        
        const params = new URLSearchParams({
            page: '1',
            page_size: '10',
            filters: JSON.stringify({
                info_type: 'solution_customize_detail',
                show: true,
                lang: lang
            })
        });

        const response = await fetch(`/apis/company_info/list?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`获取方案定制数据失败: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.status === 'ok' && result.data) {
            // 过滤并排序数据
            const solutionItems = result.data.filter(item => item.info_type === 'solution_customize_detail');
            const sortedItems = sortByDisplayOrder(solutionItems);
            
            // 更新卡片内容
            updateSolutionCards(sortedItems);
        } else {
            console.log('没有获取到方案定制数据，保持默认内容');
        }
    } catch (error) {
        console.error('获取方案定制数据失败:', error);
    }
}

/**
 * 更新方案定制卡片内容
 */
function updateSolutionCards(items) {
    // 获取所有卡片元素
    const cards = document.querySelectorAll('.solution-card');
    if (!cards || cards.length === 0) {
        console.error('未找到方案定制卡片元素');
        return;
    }
    
    // 确保有足够的数据
    if (!items || items.length === 0) {
        console.log('没有方案定制数据，保持默认内容');
        return;
    }
    
    // 最多处理3个卡片
    const maxCards = Math.min(cards.length, items.length, 3);
    
    // 遍历卡片进行更新
    for (let i = 0; i < maxCards; i++) {
        const card = cards[i];
        const item = items[i];
        
        // 更新图片
        const iconElement = card.querySelector('.card-icon img');
        if (iconElement && item.image_path) {
            iconElement.src = normalizeImagePath(item.image_path);
            iconElement.alt = item.title || '';
            
            // 添加错误处理
            iconElement.onerror = function() {
                console.error(`卡片${i+1}图片加载失败: ${this.src}`);
                if (i === 0) {
                    this.src = './images/home/<USER>';
                } else if (i === 1) {
                    this.src = './images/home/<USER>';
                } else {
                    this.src = './images/home/<USER>';
                }
            };
        }
        
        // 更新标题
        const titleElement = card.querySelector('.card-title');
        if (titleElement && item.title) {
            titleElement.textContent = item.title;
        }
        
        // 更新描述
        const descriptionElement = card.querySelector('.card-description');
        if (descriptionElement && item.content) {
            // 如果是第二个卡片（软件定制），处理带斜杠的技术名词
            if (i === 1) {
                // 在斜杠后添加零宽空格，允许在此处换行
                const processedContent = item.content.replace(/\//g, '/\u200B');
                descriptionElement.textContent = processedContent;
            } else {
                descriptionElement.textContent = item.content;
            }
        }
    }
    
    console.log(`已更新${maxCards}个方案定制卡片`);
}

// 获取表单错误消息（中英文）
function getErrorMessage(errorKey) {
    const isEnglish = isEnglishSite();
    
    const messages = {
        invalidEmail: isEnglish ? 'Invalid email format' : '邮箱格式不正确',
        requiredFields: isEnglish ? 'Please fill in all required fields' : '请填写所有必填字段',
        submitFailed: isEnglish ? 'Submission failed, please try again later' : '提交失败，请稍后重试',
        submitSuccess: isEnglish ? 'Successfully submitted' : '提交成功'
    };
    
    return messages[errorKey] || (isEnglish ? 'An error occurred' : '发生错误');
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('方案定制页面加载完成');
    
    // 获取卡片数据
    fetchSolutionCards();

    // 表单提交处理
    const form = document.querySelector('.customize-form');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();  // 阻止表单默认提交行为
            
            console.log('开始处理表单提交');
            
            // 获取表单数据
            const isEnglish = isEnglishSite();
            const nameField = isEnglish ? 'Name (Required)' : '姓名（必填）';
            const phoneField = isEnglish ? 'Phone (Required)' : '联系手机（必填）';
            const emailField = isEnglish ? 'Email (Required)' : '邮箱地址（必填）';
            const companyField = isEnglish ? 'Company (Required)' : '公司（必填）';
            const typeField = isEnglish ? 'Customization Type (Required)' : '定制类型（必填）';
            const requirementsField = isEnglish ? 'Customization Requirements (Required)' : '定制需求（必填）';
            
            const formData = {
                name: form.querySelector(`input[placeholder="${nameField}"]`).value.trim(),
                phone: form.querySelector(`input[placeholder="${phoneField}"]`).value.trim(),
                email: form.querySelector(`input[placeholder="${emailField}"]`).value.trim(),
                company: form.querySelector('.company-input').value.trim(),
                custom_type: form.querySelector('.type-input').value.trim(),
                requirements: form.querySelector('textarea').value.trim()
            };
            
            console.log('表单数据:', formData);
            
            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(formData.email)) {
                console.error('邮箱格式验证失败:', formData.email);
                showNotification(getErrorMessage('invalidEmail'), 'error');
                return;
            }
            
            // 验证其他必填字段
            for (const [key, value] of Object.entries(formData)) {
                if (!value) {
                    console.error(`字段 ${key} 为空`);
                    showNotification(getErrorMessage('requiredFields'), 'error');
                    return;
                }
            }
            
            try {
                console.log('开始发送表单数据到服务器');
                const response = await fetch('/apis/business_customization/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                console.log('服务器响应状态:', response.status);
                const result = await response.json();
                console.log('服务器响应数据:', result);
                
                if (result.status === 'ok') {
                    console.log('表单提交成功');
                    showNotification(getErrorMessage('submitSuccess'), 'success');
                    form.reset();  // 清空表单
                } else {
                    console.error('表单提交失败:', result.message);
                    showNotification(result.message || getErrorMessage('submitFailed'), 'error');
                }
            } catch (error) {
                console.error('表单提交过程出错:', error);
                console.error('错误堆栈:', error.stack);
                showNotification(getErrorMessage('submitFailed'), 'error');
            }
        });
    }
});

// 显示通知框
function showNotification(message, type = 'success') {
    // 创建通知框元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // 根据类型设置不同的样式
    const styles = {
        success: {
            background: '#007DDB',
            color: '#FFFFFF'
        },
        error: {
            background: '#FF4D4F',
            color: '#FFFFFF'
        }
    };
    
    const currentStyle = styles[type] || styles.success;
    
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 20px 40px;
        background: ${currentStyle.background};
        color: ${currentStyle.color};
        border-radius: 12px;
        font-size: 28px;
        font-family: "Source Han Sans CN", sans-serif;
        z-index: 1000;
        text-align: center;
        min-width: 200px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;
    
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.3s ease-out';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
} 