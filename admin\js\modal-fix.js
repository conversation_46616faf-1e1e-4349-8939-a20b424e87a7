/**
 * Bootstrap 2.x 模态框修复
 * 解决模态框背景无法点击关闭、多个模态框叠加、灰色遮罩无法清除等问题
 */

$(document).ready(function() {
    // 初始化时清理可能存在的模态框背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    // 修复Bootstrap 2.x模态框问题
    if (typeof $.fn.modal !== 'undefined') {
        // 保存原始modal方法
        var originalModal = $.fn.modal;
        
        // 重写modal方法
        $.fn.modal = function(option) {
            console.log("调用模态框方法:", option);
            
            if (option === 'hide') {
                // 隐藏模态框时清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                // 调用原始方法
                return originalModal.apply(this, arguments);
            }
            else if (option === 'show' || typeof option === 'object') {
                // 显示模态框前清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                // 调用原始方法
                return originalModal.apply(this, arguments);
            }
            
            // 其他情况调用原始方法
            return originalModal.apply(this, arguments);
        };
    }
    
    // 监听模态框关闭事件，确保背景被移除
    $(document).on('hidden', '.modal', function() {
        console.log("模态框关闭，清理背景");
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });
    
    // 监听点击事件，修复灰色遮罩无法点击的问题
    $(document).on('click', '.modal-backdrop', function() {
        console.log("点击了模态框背景，关闭所有模态框");
        $('.modal').modal('hide');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });
    
    // 监听ESC键，确保模态框可以关闭
    $(document).keyup(function(e) {
        if (e.keyCode === 27) { // ESC键
            console.log("按下ESC键，关闭所有模态框");
            $('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }
    });
    
    // 初始化数据列表
    setTimeout(function() {
        console.log("页面加载完成，正在加载数据列表...");
        if (typeof nav_list === 'function') {
            nav_list(1, 10);
            console.log("数据列表加载成功");
        }
    }, 1000);
}); 