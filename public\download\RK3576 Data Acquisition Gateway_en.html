<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>RK3576 Data Acquisition Gateway-Downloads-Bearkey-Official Website</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    
    <!-- css -->
    <link href="../css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../css/style.css" rel="stylesheet"/>
    <link href="../css/footer.css" rel="stylesheet"/>
    <link href="../css/download-page.css" rel="stylesheet"/>
    <link href="../css/nav-style.css" rel="stylesheet"/>
    <!-- Font Awesome Icon Library -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* Default state of SVG images */
        .download-action img {
            content: url("../images/home/<USER>");
        }
        .download-action:hover img {
            content: url("../images/home/<USER>");
        }
        .category-select {
            background-image: url("../images/home/<USER>");
        }
        .category-select:hover {
            background-image: url("../images/home/<USER>");
        }
        .arrow-icon {
            content: url("../images/home/<USER>");
        }
        .expandable-menu:hover .arrow-icon {
            content: url("../images/home/<USER>");
        }
        .expandable-menu.expanded .arrow-icon {
            content: url("../images/home/<USER>");
        }
    </style>
</head>
<body>
<div id="wrapper" style="display: none;">
    <!-- Navigation Bar -->
    <header>
        <!-- Navigation bar will be dynamically loaded via nav.js -->
    </header>

    <!-- Page Title Background -->
    <div class="page-title-banner">
        <div class="container">
            <h1 class="page-title">Downloads</h1>
        </div>
    </div>

    <div class="containert">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb">
            <a href="/index_en.html">Bearkey Technology</a>
            <span class="separator">/</span>
            <span class="current">Downloads</span>
        </div>

        <!-- Main Content Area -->
        <div class="download-main">
            <!-- Left Selection Area -->
            <div class="download-sidebar">
                <!-- Menu will be dynamically loaded via JavaScript -->
            </div>

            <!-- Right Content Area -->
            <div class="download-content">
                <!-- Product Display Area -->
                <div class="download-product">
                    <div class="product-image">
                        <img src="" alt="Product Image" id="product-image">
                    </div>
                    <div class="product-actions">
                        <div class="product-name" id="product-title">RK3576 Industrial Development board</div>
                        <div class="product-buttons" id="product-buttons">
                            <a href="#" class="btn-product">Product Manual</a>
                            <a href="#" class="btn-product">Buy Now</a>
                        </div>
                    </div>
                </div>

                <!-- Download Content Container -->
                <div class="download-container">
                    <!-- Left Column -->
                    <div class="download-column" id="left-column"></div>
                    <!-- Right Column -->
                    <div class="download-column" id="right-column"></div>
                </div>

                <!-- Container for All Download Sections -->
                <div id="all-sections" style="display: none;">
                    <!-- Tools Section -->
                    <div class="download-section" data-type="tools">
                        <h2 class="section-title">Tools</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">RK Driver Assistant</div>
                                    <div class="item-note"></div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Erase IDB Tool</div>
                                    <div class="item-note">Extract Code: 1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Linux_upgrade_tool</div>
                                    <div class="item-note">Extract Code: 123456</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resources Section -->
                    <div class="download-section" data-type="resources">
                        <h2 class="section-title">Resources</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Linux-headers</div>
                                    <div class="item-note">Extract Code: 1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Source Code Section -->
                    <div class="download-section" data-type="source">
                        <h2 class="section-title">Source Code</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Android 7.1 Industry SDK Source Code</div>
                                    <div class="item-note">Extract Code: 1234</div>
                                    <div class="item-note">This SDK resource is the main maintained version</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Linux-SDK Source Package</div>
                                    <div class="item-note">Extract Code: 1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Firmware Section -->
                    <div class="download-section" data-type="firmware">
                        <h2 class="section-title">Firmware</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Ubuntu 18.04 Firmware</div>
                                    <div class="item-note">Extract Code: 1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Industry 10.1-inch MPI Firmware</div>
                                    <div class="item-note">Extract Code:</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File System Section -->
                    <div class="download-section" data-type="filesystem">
                        <h2 class="section-title">File System</h2>
                        <div class="download-list">
                            <div class="download-item">
                                <div class="item-info">
                                    <div class="item-name">Linux Root File System Image (arm64&arm32)</div>
                                    <div class="item-note">Extract Code: 1234</div>
                                </div>
                                <div class="download-action">
                                    <img src="../images/home/<USER>" alt="Download">
                                    <span>Download</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <!-- Footer will be dynamically loaded via footer.js -->
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- Download Modal -->
<div class="modal fade" id="toolDownloadModal" tabindex="-1" role="dialog" aria-labelledby="toolDownloadModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content tool-modal-content">
            <div class="modal-header tool-modal-header">
                <span class="tool-modal-title">Resource Download Directory</span>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body tool-modal-body">
                <!-- Tool name, extract code, and buttons will be dynamically filled -->
            </div>
        </div>
    </div>
</div>

<!-- javascript -->
<script src="../js/jquery.js"></script>
<script src="../js/jquery.easing.1.3.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/nav.js"></script>
<script src="../js/floating-toolbar.js"></script>
<script src="../js/toolbar-data.js"></script>
<script src="../js/download.js"></script>
<script src="../js/footer.js"></script>
<script src="../js/note-text-formatter.js"></script>
<script>
// 返回置顶功能
$(document).ready(function() {
    // 监听滚动事件
    $(window).scroll(function(){
        if ($(this).scrollTop() > 100) {
            $('.scrollup').fadeIn();
        } else {
            $('.scrollup').fadeOut();
        }
    });

    // 点击返回顶部
    $('.scrollup').click(function(){
        $("html, body").animate({ scrollTop: 0 }, 1000);
        return false;
    });
});
</script>

<script>
// Global menu state save function
function saveMenuState(menuId, submenuId) {
    localStorage.setItem('downloadMenuState', JSON.stringify({
        menuId: menuId,
        submenuId: submenuId,
        timestamp: Date.now()
    }));
}
</script>
</body>
</html> 