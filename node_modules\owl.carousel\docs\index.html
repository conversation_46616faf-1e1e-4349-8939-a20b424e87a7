<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Touch enabled jQuery plugin that lets you create beautiful responsive carousel slider.">
    <meta name="author" content="<PERSON>">
    <title>
      Home | Owl Carousel | 2.3.4
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="assets/vendors/jquery.min.js"></script>
    <script src="assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li class="active">
                <a href="/OwlCarousel2/index.html">Home</a> 
              </li>
              <li> <a href="/OwlCarousel2/demos/demos.html">Demos</a>  </li>
              <li> <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a>  </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- home panel -->
    <section id="hero">
      <div class="row">
        <div class="large-8 medium-8 columns">
          <h1>Owl Carousel 2</h1>
          <h4>Touch enabled jQuery plugin that lets you create a beautiful responsive carousel slider.</h4>
          <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip" class="hero-button">Download</a> 
          <a href="https://github.com/OwlCarousel2/OwlCarousel2" class="hero-button outline">Github</a> 
          <p>2.3.4</p>
        </div>
        <div class="large-4 medium-4 columns">
          <img class="owl-logo" src="assets/img/owl-logo.png" alt="mr. Owl">
        </div>
      </div>
    </section>

    <!-- body -->
    <div class="home-demo">
      <div class="row">
        <div class="large-12 columns">
          <h3>Demo</h3>
          <div class="owl-carousel">
            <div class="item">
              <h2>Swipe</h2>
            </div>
            <div class="item">
              <h2>Drag</h2>
            </div>
            <div class="item">
              <h2>Responsive</h2>
            </div>
            <div class="item">
              <h2>CSS3</h2>
            </div>
            <div class="item">
              <h2>Fast</h2>
            </div>
            <div class="item">
              <h2>Easy</h2>
            </div>
            <div class="item">
              <h2>Free</h2>
            </div>
            <div class="item">
              <h2>Upgradable</h2>
            </div>
            <div class="item">
              <h2>Tons of options</h2>
            </div>
            <div class="item">
              <h2>Infinity</h2>
            </div>
            <div class="item">
              <h2>Auto Width</h2>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      var owl = $('.owl-carousel');
      owl.owlCarousel({
        margin: 10,
        loop: true,
        responsive: {
          0: {
            items: 1
          },
          600: {
            items: 2
          },
          1000: {
            items: 3
          }
        }
      })
    </script>

    <!-- features -->
    <div id="features">
      <div class="row">
        <div class="small-12 medium-9 small-centered columns">
          <section class="row feature">
            <div class="medium-4 small-12 columns">
              <img src="assets/img/feature-options.png" alt="">
            </div>
            <div class="medium-8 small-12 columns">
              <h2>Fully Customisable</h2>
              <p>Over 60 options. Easy for novice users and even more powerful for advanced developers.</p>
            </div>
          </section>
          <section class="row feature">
            <div class="medium-4 small-12 columns">
              <img src="assets/img/feature-drag.png" alt="">
            </div>
            <div class="medium-8 small-12 columns">
              <h2>Touch and Drag Support</h2>
              <p>Designed specially to boost mobile browsing experience. Mouse drag works great on desktop too!</p>
            </div>
          </section>
          <section class="row feature">
            <div class="medium-4 small-12 columns">
              <img src="assets/img/feature-responsive.png" alt="">
            </div>
            <div class="medium-8 small-12 columns">
              <h2>Fully Responsive</h2>
              <p>Almost all options are responsive and include very intuitive breakpoints settings.</p>
            </div>
          </section>
          <section class="row feature">
            <div class="medium-4 small-12 columns">
              <img src="assets/img/feature-modern.png" alt="">
            </div>
            <div class="medium-8 small-12 columns">
              <h2>Modern Browsers</h2>
              <p>Owl uses hardware acceleration with CSS3 Translate3d transitions. Its fast and works like a charm! </p>
            </div>
          </section>
          <section class="row feature">
            <div class="medium-4 small-12 columns">
              <img src="assets/img/feature-zombie.png" alt="">
            </div>
            <div class="medium-8 small-12 columns">
              <h2>Zombie Browsers</h2>
              <p>CSS2 fallback supported for older browser.</p>
            </div>
          </section>
          <section class="row feature">
            <div class="medium-4 small-12 columns">
              <img src="assets/img/feature-module.png" alt="">
            </div>
            <div class="medium-8 small-12 columns">
              <h2>Modules and Plugins</h2>
              <p>Owl Carousel supports plugin modular structure. Therefore, you can detach plugins that you won&#x27;t use on your project or create new ones that fit your needs</p>
            </div>
          </section>
        </div>
      </div>
    </div>

    <!-- footer -->
    <section id="teaser-text">
      <div class="row">
        <div class="small-12 medium-11 small-centered columns">
          <hr>
          <h3 id="owl-carousel-has-been-choosen-as-number-one-jquery-plugin-by-hundreds-of-developers-now-its-time-for-a-new-version-that-comes-with-lots-of-new-features-and-even-more-user-friendly-api-">Owl Carousel has been choosen as number one jQuery plugin by hundreds of developers. Now its time for a new version that comes with lots of new features and even more user friendly API.</h3>
          <p>Watch this space- we&#39;ll be launching soon :)</p>
        </div>
      </div>
    </section>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="assets/vendors/highlight.js"></script>
    <script src="assets/js/app.js"></script>
  </body>
</html>