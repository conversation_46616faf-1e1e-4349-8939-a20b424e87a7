// 使用立即执行函数表达式(IIFE)避免全局变量冲突
(function() {
// 检测是否在子目录中
const isInSubdir = window.location.pathname.split('/').length > 2;
const pathPrefix = isInSubdir ? '../' : '';

// 动态添加footer.css引用，确保样式被正确加载
(function loadFooterCSS() {
    // 检查是否已经加载了footer.css
    const links = document.getElementsByTagName('link');
    for (let i = 0; i < links.length; i++) {
        if (links[i].href.indexOf('footer.css') !== -1) {
            return; // 已经加载，不需要重复添加
        }
    }

    // 创建link元素并添加到head
    const footerCSS = document.createElement('link');
    footerCSS.rel = 'stylesheet';
    footerCSS.type = 'text/css';
    
    // 确定footer.css的路径（基于footer.js的路径）
    const scripts = document.getElementsByTagName('script');
    let footerJSPath = '';
    
    // 找到footer.js的路径
    for (let i = 0; i < scripts.length; i++) {
        if (scripts[i].src.indexOf('footer.js') !== -1) {
            footerJSPath = scripts[i].src;
            break;
        }
    }
    
    // 基于footer.js的路径构建footer.css的路径
    if (footerJSPath) {
        // 替换js/footer.js为css/footer.css
        footerCSS.href = footerJSPath.replace('js/footer.js', 'css/footer.css');
    } else {
        // 如果找不到footer.js的路径，使用相对路径
        footerCSS.href = `${pathPrefix}css/footer.css`;
    }
    
    // 添加到head
    document.head.appendChild(footerCSS);
})();

// 页脚HTML模板
const FOOTER_TEMPLATE = `
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <div class="widget">
                    <h5 class="widgetheading" id="product_solution_title">产品中心</h5>
                    <ul class="link-list" id="product_solution_container">
                        <!-- 产品方案内容将从后端动态加载 -->
                    </ul>
                </div>
            </div>
            <div class="col-md-4">
                <div class="widget">
                    <h5 class="widgetheading" id="service_support_title">服务与支持</h5>
                    <ul class="link-list" id="service_support_container">
                        <!-- 服务与支持内容将从后端动态加载 -->
                    </ul>
                </div>
            </div>
            <div class="col-md-4">
                <div class="widget">
                    <h5 class="widgetheading" id="contact_info_title">联系我们</h5>
                    <div class="contact-section">
                        <ul class="link-list" id="contact_info_container">
                            <!-- 联系方式内容将从后端动态加载 -->
                        </ul>
                        <div id="office_address_container" class="office-address">
                            <!-- 办公地址内容将从后端动态加载 -->
                        </div>
                    </div>
                    <!-- 关注我们的二维码内容 -->
                    <div id="follow_us_container">
                        <!-- 二维码内容将从后端动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer-divider"></div>
    <div class="footer-copyright">
        <p>
            Copyright &copy; 2015-<script>document.write(new Date().getFullYear())</script> 
            <span id="copyright_company"></span> All Rights Reserved.|
            <a target="_blank" href="http://beian.miit.gov.cn"> 闽ICP备********号-4</a>
        </p>
    </div>
`;

// 默认文本配置
const DEFAULT_TEXTS = {
    en: {
        officeAddress: 'Office Address',
        contactUs: 'Contact Us',
        followUs: 'Follow Us (WeChat Official Account)',
        products: 'Products',
        serviceSupport: 'Service & Support',
        company: 'Xiamen Bearkey Technology Co., Ltd.',
        defaultAddress: 'No. 51, Chengyi North Avenue, Software Park III, Xiamen',
        defaultContacts: [
            'Business: <EMAIL>',
            'Recruitment: <EMAIL>',
            'Support: <EMAIL>'
        ],
        defaultProducts: [
            { name: 'Wireless Screen Casting', url: '#' },
            { name: 'Industrial AI Solutions', url: '#' }
        ]
    },
    zh: {
        officeAddress: '办公地址',
        contactUs: '联系我们',
        followUs: '关注我们（微信公众号）',
        products: '产品中心',
        serviceSupport: '服务与支持',
        company: '厦门贝启科技有限公司',
        defaultAddress: '厦门市海沧区软件园三期星网锐捷海西科技园1号楼101',
        defaultContacts: [
            '商务：<EMAIL>',
            '求职：<EMAIL>',
            '支持：<EMAIL>'
        ],
        defaultProducts: [
            { name: '无线投屏解决方案', url: '#' },
            { name: '工控主板解决方案', url: '#' }
        ]
    }
};

// 工具函数：检查是否为英文站点
function isEnglishSite() {
    return window.location.pathname.includes('_en.html') || 
           window.location.pathname.includes('/en/') ||
           window.location.hostname.includes('en.') || 
           window.location.search.includes('lang=en');
}

// 工具函数：获取当前语言配置
function getCurrentLangConfig() {
    return isEnglishSite() ? DEFAULT_TEXTS.en : DEFAULT_TEXTS.zh;
}

// 工具函数：处理图片路径
function normalizeImagePath(imgSrc) {
    if (!imgSrc) return '';
    if (imgSrc.includes('C:')) {
        const fileName = imgSrc.split('\\').pop().split('/').pop();
        return '/uploads/' + fileName;
    } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
        return '/' + imgSrc;
    }
    return imgSrc;
}

// 初始化页脚
function initFooter() {
    // 插入页脚模板
    const footerElement = document.querySelector('footer');
    if (!footerElement) {
        return;
    }
    
    footerElement.innerHTML = FOOTER_TEMPLATE;
    
    // 标记为已初始化
    footerElement.setAttribute('data-initialized', 'true');

    // 设置默认文本
    const langConfig = getCurrentLangConfig();
    
    try {
        document.getElementById('product_solution_title').textContent = langConfig.products;
        document.getElementById('service_support_title').textContent = langConfig.serviceSupport;
        document.getElementById('contact_info_title').textContent = langConfig.contactUs;
        document.getElementById('copyright_company').textContent = langConfig.company;
    } catch (error) {
    }

    // 加载动态内容
    loadFooterContent();
    
    // 调整页脚布局
    adjustFooterLayout();
    
    // 窗口大小改变时重新调整布局
    window.addEventListener('resize', adjustFooterLayout);
    // 只在product.html和product_en.html页面显示footer（display:block），其他页面不处理display属性
    if (window.location.pathname.endsWith('product.html') || window.location.pathname.endsWith('product_en.html')) {
        footerElement.style.display = 'block';
    }
}

// 调整页脚布局适应不同屏幕尺寸
function adjustFooterLayout() {
    const footerElement = document.querySelector('footer');
    const footerDivider = document.querySelector('.footer-divider');
    const footerCopyright = document.querySelector('.footer-copyright');
    
    if (!footerElement || !footerDivider || !footerCopyright) return;
    
    // 判断是否为小屏幕
    if (window.innerWidth <= 991) {
        footerDivider.style.position = 'relative';
        footerDivider.style.bottom = 'auto';
        footerDivider.style.marginTop = '30px';
        
        footerCopyright.style.position = 'relative';
        footerCopyright.style.bottom = 'auto';
        footerCopyright.style.marginTop = '15px'; 
    } else {
        footerDivider.style.position = 'absolute';
        footerDivider.style.bottom = '50px';
        footerDivider.style.marginTop = '0';
        
        footerCopyright.style.position = 'absolute';
        footerCopyright.style.bottom = '15px';
        footerCopyright.style.marginTop = '0';
    }
}

// 加载页脚动态内容
function loadFooterContent() {
    const lang = isEnglishSite() ? 1 : 0;

    // 加载产品中心
    loadCompanyInfo('product_solution', updateProductSolution);

    // 加载服务与支持
    loadCompanyInfo('service_support', updateServiceSupport);

    // 加载联系方式
    loadCompanyInfo('contact', updateCompanyContact);

    // 加载办公地址
    loadCompanyInfo('address', updateCompanyAddress);

    // 加载关注我们
    loadCompanyInfo('follow_us', updateFollowUs);
}

// 从API加载公司信息
function loadCompanyInfo(infoType, callback) {
    const lang = isEnglishSite() ? 1 : 0;
    
    // 构建API请求URL和参数
    const apiUrl = '/apis/company_info/list';
    const params = {
        page: 1,
        page_size: 10,
        filters: JSON.stringify({
            info_type: infoType,
            show: true,
            lang: lang
        })
    };
    
    $.ajax({
        url: apiUrl,
        type: 'GET',
        data: params,
        success: function(result) {
            if (result.status === 'ok' && result.data) {
                // 添加调试信息
                if (infoType === 'follow_us') {
                    console.log('获取到follow_us数据:', result.data);
                    result.data.forEach((item, index) => {
                        console.log(`二维码项目 ${index + 1}:`, {
                            name: item.name,
                            title: item.title,
                            section_title: item.section_title,
                            content: item.content,
                            image_path: item.image_path
                        });
                    });
                }
                callback(result.data);
            } else {
                console.error('加载失败:', infoType, result);
                callback(null);
            }
        },
        error: function(error) {
            console.error('API错误:', infoType, error);
            callback(null);
        }
    });
}

// 更新办公地址
function updateCompanyAddress(data) {
    const container = document.getElementById('office_address_container');
    const langConfig = getCurrentLangConfig();
    const isEnglish = isEnglishSite();
    
    if (!container) return;
    
    // 根据语言选择适当的标签
    let addressLabelHtml = '';
    if (isEnglish) {
        // 判断是否为小屏幕
        const isSmallScreen = window.innerWidth <= 491;
        
        if (isSmallScreen) {
            // 小屏幕英文界面使用简单的"Address:"标签，无需字符间距控制
            addressLabelHtml = '<span class="address-prefix">Address:&nbsp;</span>';
        } else {
            // 大屏幕英文界面使用"Address:"标签，添加一个字母大小的空间
            addressLabelHtml = '<span class="address-prefix"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span>';
        }
    } else {
        // 中文界面使用"地址："标签
        addressLabelHtml = '<span class="address-prefix"><span class="address-prefix-char first-char">地</span><span class="address-prefix-char spacer"></span><span class="address-prefix-char spacer"></span><span class="address-prefix-char">址</span>&nbsp;：&nbsp;</span>';
    }
    
    // 默认地址内容
    let defaultAddressHtml = '';
    if (isEnglish) {
        // 判断是否为小屏幕
        const isSmallScreen = window.innerWidth <= 491;
        
        if (isSmallScreen) {
            // 小屏幕英文界面地址分行显示
            defaultAddressHtml = `
                <div class="address-line">${addressLabelHtml}</div>
                <div class="address-line"><span class="address-content">Unit 101, East Wing, Building 1, Xingwang Ruijie Haixi Science and Technology Park, No. 9, Gaoxin Avenue, Shangjie</span></div>
                <div class="address-line"><span class="address-content">Town, Minhou County, Fuzhou City</span></div>
                <div class="address-line"><span class="address-content">Unit 209, Xiamen University National Science and Technology Park, No. 39, Wanghai Road, Software Park Phase II, Siming</span></div>
                <div class="address-line"><span class="address-content">District, Xiamen City</span></div>
            `;
        } else {
            // 大屏幕英文界面地址显示
            defaultAddressHtml = `
                <div class="address-line">${addressLabelHtml}<span class="address-content">&nbsp;&nbsp;Unit 101, East Wing, Building 1, Xingwang Ruijie Haixi Science and Technology Park, No. 9, Gaoxin Avenue,</span></div>
                <div class="address-line"><span class="address-prefix" style="visibility: hidden;"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span><span class="address-content">&nbsp;&nbsp;Minhou County, Fuzhou</span></div>
                <div class="address-line">${addressLabelHtml}<span class="address-content">&nbsp;&nbsp;Unit 209, Xiamen University National Science and Technology Park, No. 39, Wanghai Road, Software Park II,</span></div>
                <div class="address-line"><span class="address-prefix" style="visibility: hidden;"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span><span class="address-content">&nbsp;&nbsp;Siming District, Xiamen</span></div>
            `;
        }
    } else {
        defaultAddressHtml = `
            <div class="address-line">${addressLabelHtml}<span class="address-content">&nbsp;&nbsp;福州市闽侯县上街镇高新大道9号星网锐捷海西科技园1号楼东101单元</span></div>
            <div class="address-line"><span class="address-prefix" style="visibility: hidden;"><span class="address-prefix-char first-char">地</span><span class="address-prefix-char spacer"></span><span class="address-prefix-char spacer"></span><span class="address-prefix-char">址</span>&nbsp;：&nbsp;</span><span class="address-content">&nbsp;&nbsp;厦门市思明区软件园二期望海路39号厦门大学国家科技园209单元</span></div>
        `;
    }
    
    // 添加窗口大小变化监听，重新生成地址
    window.addEventListener('resize', function() {
        if (isEnglish) {
            // 如果只使用默认地址，则在窗口大小变化时重新生成
            if (!data || data.length === 0) {
                container.innerHTML = getDefaultAddressHtml();
            }
        }
    });
    
    // 获取默认地址的辅助函数
    function getDefaultAddressHtml() {
        if (!isEnglish) return defaultAddressHtml;
        
        const isSmallScreen = window.innerWidth <= 491;
        const addressLabel = isSmallScreen ? 
            '<span class="address-prefix">Address:&nbsp;</span>' : 
            '<span class="address-prefix"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span>';
        
        if (isSmallScreen) {
            return `
                <div class="address-line">${addressLabel}</div>
                <div class="address-line"><span class="address-content">Unit 101, East Wing, Building 1, Xingwang Ruijie Haixi Science and Technology Park, No. 9, Gaoxin Avenue, Shangjie</span></div>
                <div class="address-line"><span class="address-content">Town, Minhou County, Fuzhou City</span></div>
                <div class="address-line"><span class="address-content">Unit 209, Xiamen University National Science and Technology Park, No. 39, Wanghai Road, Software Park Phase II, Siming</span></div>
                <div class="address-line"><span class="address-content">District, Xiamen City</span></div>
            `;
        } else {
            return `
                <div class="address-line">${addressLabel}<span class="address-content">&nbsp;&nbsp;Unit 101, East Wing, Building 1, Xingwang Ruijie Haixi Science and Technology Park, No. 9, Gaoxin Avenue,</span></div>
                <div class="address-line"><span class="address-prefix" style="visibility: hidden;"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span><span class="address-content">&nbsp;&nbsp;Minhou County, Fuzhou</span></div>
                <div class="address-line">${addressLabel}<span class="address-content">&nbsp;&nbsp;Unit 209, Xiamen University National Science and Technology Park, No. 39, Wanghai Road, Software Park II,</span></div>
                <div class="address-line"><span class="address-prefix" style="visibility: hidden;"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span><span class="address-content">&nbsp;&nbsp;Siming District, Xiamen</span></div>
            `;
        }
    }
    
    // 显示所有地址记录
    if (data && data.length > 0) {
        // 按显示顺序排序
        const sortedAddresses = [...data].sort((a, b) => 
            (a.display_order || 100) - (b.display_order || 100)
        );
        
        let html = '';
        let isFirstAddress = true;
        const isSmallScreen = window.innerWidth <= 491;
        
        if (isEnglish && isSmallScreen) {
            // 小屏幕英文界面特殊处理
            html += `<div class="address-line">${addressLabelHtml}</div>`;
            
            sortedAddresses.forEach(item => {
                if (item.content && item.content.trim()) {
                    // 获取地址内容
                    let addressContent = item.content.trim();
                    
                    // 移除地址前缀，避免重复
                    addressContent = addressContent.replace(/地\s*址[：:]\s*/i, '').replace(/address[：:]\s*/i, '');
                    
                    // 更智能的英文地址断行 - 考虑屏幕宽度和单词长度
                    const maxLineLength = 40; // 小屏幕上每行的最大字符数
                    let words = addressContent.split(' ');
                    let currentLine = '';
                    let lines = [];
                    
                    for (let i = 0; i < words.length; i++) {
                        let word = words[i];
                        
                        // 如果单词过长，可以考虑在单词内部断行
                        if (word.length > maxLineLength * 0.7) {
                            if (currentLine) {
                                lines.push(currentLine);
                                currentLine = '';
                            }
                            
                            // 将长单词分成两部分
                            const firstPart = word.substring(0, Math.ceil(word.length * 0.6));
                            const secondPart = word.substring(Math.ceil(word.length * 0.6));
                            lines.push(firstPart);
                            currentLine = secondPart;
                            continue;
                        }
                        
                        // 正常单词处理
                        if ((currentLine + ' ' + word).length > maxLineLength && currentLine) {
                            lines.push(currentLine);
                            currentLine = word;
                        } else {
                            currentLine = currentLine ? currentLine + ' ' + word : word;
                        }
                    }
                    
                    if (currentLine) {
                        lines.push(currentLine);
                    }
                    
                    // 生成每行地址内容
                    lines.forEach(line => {
                        html += `<div class="address-line"><span class="address-content">${line}</span></div>`;
                    });
                }
            });
        } else {
            // 大屏幕或中文界面正常处理
            sortedAddresses.forEach(item => {
                if (item.content && item.content.trim()) {
                    // 获取地址内容
                    let addressContent = item.content.trim();
                    
                    // 移除地址前缀，避免重复
                    addressContent = addressContent.replace(/地\s*址[：:]\s*/i, '').replace(/address[：:]\s*/i, '');
                    
                    // 英文地址处理 - 每60个字符左右断行
                    if (isEnglish) {
                        const chunks = [];
                        const words = addressContent.split(' ');
                        let currentLine = '';
                        
                        words.forEach(word => {
                            if ((currentLine + ' ' + word).length > 60 && currentLine.length > 0) {
                                chunks.push(currentLine);
                                currentLine = word;
                            } else {
                                currentLine = currentLine.length > 0 ? currentLine + ' ' + word : word;
                            }
                        });
                        
                        if (currentLine.length > 0) {
                            chunks.push(currentLine);
                        }
                        
                        // 第一行前添加地址标签
                        chunks.forEach((chunk, index) => {
                            if (index === 0 && isFirstAddress) {
                                html += `<div class="address-line">${addressLabelHtml}<span class="address-content">&nbsp;&nbsp;${chunk}</span></div>`;
                                isFirstAddress = false;
                            } else {
                                // 后续行添加左侧空白保持对齐
                                const hiddenPrefix = isSmallScreen ? 
                                    '' : // 小屏幕不需要隐藏前缀
                                    `<span class="address-prefix" style="visibility: hidden;"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span>`;
                                html += `<div class="address-line">${hiddenPrefix}<span class="address-content">&nbsp;&nbsp;${chunk}</span></div>`;
                            }
                        });
                    } else {
                        // 中文地址处理
                        if (isFirstAddress) {
                            html += `<div class="address-line">${addressLabelHtml}<span class="address-content">&nbsp;&nbsp;${addressContent}</span></div>`;
                            isFirstAddress = false;
                        } else {
                            // 后续地址添加左侧空白保持对齐
                            html += `<div class="address-line"><span class="address-prefix" style="visibility: hidden;"><span class="address-prefix-char first-char">地</span><span class="address-prefix-char spacer"></span><span class="address-prefix-char spacer"></span><span class="address-prefix-char">址</span>&nbsp;：&nbsp;</span><span class="address-content">&nbsp;&nbsp;${addressContent}</span></div>`;
                        }
                    }
                }
            });
        }
        
        if (html) {
            container.innerHTML = html;
        } else {
            container.innerHTML = defaultAddressHtml;
        }
    } else {
        container.innerHTML = defaultAddressHtml;
    }
}

// 更新联系方式
function updateCompanyContact(data) {
    const container = document.getElementById('contact_info_container');
    const titleElement = document.getElementById('contact_info_title');
    const langConfig = getCurrentLangConfig();
    const isEnglish = isEnglishSite();
    
    if (!container) return;
    
    // 生成带有精确控制间距的"地址"标签
    const addressLabelHtml = isEnglish ? 
        '<span class="address-prefix"><span class="address-prefix-char" style="visibility:hidden;">A</span><span class="address-prefix-char">A</span><span class="address-prefix-char">d</span><span class="address-prefix-char">d</span><span class="address-prefix-char">r</span><span class="address-prefix-char">e</span><span class="address-prefix-char">s</span><span class="address-prefix-char">s</span>&nbsp;:&nbsp;</span>' : 
        '<span class="address-prefix"><span class="address-prefix-char first-char">地</span><span class="address-prefix-char">址</span>： </span>';
    
    // 更新标题
    if (data && data.length > 0) {
        const sectionTitle = data[0].section_title || data[0].title || data[0].name || langConfig.contactUs;
        if (titleElement) {
            titleElement.textContent = sectionTitle;
        }
    } else if (titleElement) {
        titleElement.textContent = langConfig.contactUs;
    }
    
    // 更新内容，遍历所有data项
    if (data && data.length > 0) {
        const allLines = [];
        data.forEach(item => {
            if (item.content) {
                const lines = item.content.split(/\n|<br\/?/);
                allLines.push(...lines);
            }
        });
        const processedLines = allLines
            .filter(line => line.trim())
            .map(line => {
                // 检查是否为地址行，如果是则进行特殊处理
                if (line.match(/地\s*址[：:]/i) || line.match(/address[：:]/i)) {
                    // 提取地址内容，移除前缀
                    const addressContent = line.replace(/地\s*址[：:]\s*/i, '').replace(/address[：:]\s*/i, '');
                    // 使用CSS控制的地址标签
                    return `<li class="address-line">${addressLabelHtml}<span class="address-content">${addressContent}</span></li>`;
                }
                // 英文版的联系方式特殊处理
                if (isEnglish) {
                    // 匹配Business/Recruitment/Support等标签，使用非贪婪模式捕获标签与冒号之间的空格
                    const match = line.match(/^(Business|Recruitment|Support|Email|Tel|Phone|Contact|Service)([\s]*[:：][\s]*)(.*)/i);
                    if (match) {
                        const label = match[1];         // 标签名称
                        const separator = match[2];     // 原始的分隔符(包括空格和冒号)
                        const content = match[3];       // 内容
                        // 使用原始的分隔符，保留标签与冒号之间的空格
                        return `<li>${label}${separator}${content}</li>`;
                    }
                }
                return `<li>${line.trim()}</li>`;
            })
            .join('');
        container.innerHTML = processedLines;
    } else {
        container.innerHTML = langConfig.defaultContacts
            .map(contact => {
                // 英文版的联系方式特殊处理
                if (isEnglish) {
                    // 匹配Business/Recruitment/Support等标签，捕获标签与冒号之间的空格
                    const match = contact.match(/^(Business|Recruitment|Support|Email|Tel|Phone|Contact|Service)([\s]*[:：][\s]*)(.*)/i);
                    if (match) {
                        const label = match[1];         // 标签名称
                        const separator = match[2];     // 原始的分隔符(包括空格和冒号)
                        const content = match[3];       // 内容
                        // 使用原始的分隔符，保留标签与冒号之间的空格
                        return `<li>${label}${separator}${content}</li>`;
                    }
                }
                return `<li>${contact}</li>`;
            })
            .join('');
    }
}

// 新增：更新服务与支持
function updateServiceSupport(data) {
    const container = document.getElementById('service_support_container');
    const titleElement = document.getElementById('service_support_title');
    const langConfig = getCurrentLangConfig();
    
    if (!container) return;
    
    // 更新标题
    if (data && data.length > 0) {
        const sectionTitle = data[0].section_title || data[0].title || data[0].name || langConfig.serviceSupport;
        if (titleElement) {
            titleElement.textContent = sectionTitle;
        }
    } else if (titleElement) {
        titleElement.textContent = langConfig.serviceSupport;
    }
    
    // 更新内容
    if (data && data.length > 0) {
        // 按显示顺序排序
        const sortedItems = [...data].sort((a, b) => 
            (a.display_order || 100) - (b.display_order || 100)
        );
        
        const serviceItems = sortedItems.map(item => {
            // 优先使用item.url或item.link字段作为链接
            let url = item.url || item.link || '#';
            
            // 如果没有直接的url字段，尝试从content中解析
            if (url === '#' && item.content) {
                const content = item.content;
                // 尝试多种可能的链接格式
                const urlMatch = content.match(/链接:\s*(.*?)(?:\n|$)/) || 
                                content.match(/url:\s*(.*?)(?:\n|$)/i) || 
                                content.match(/link:\s*(.*?)(?:\n|$)/i);
                if (urlMatch) {
                    url = urlMatch[1].trim();
                }
            }
            
            // 获取名称
            let name = item.name || '';
            if (!name && item.content) {
                name = item.content.split(/\n|<br\/?>/)[0].replace(/链接:.*/, '').replace(/url:.*/, '').replace(/link:.*/, '').trim();
            }
            
            if (name) {
                return `<li><a href="${url}" ${url.includes('http') ? 'target="_blank"' : ''}>${name}</a></li>`;
            }
            return '';
        }).filter(Boolean);

        container.innerHTML = serviceItems.length > 0 ? 
            serviceItems.join('') : 
            `<li><a href="#">技术支持</a></li>
             <li><a href="#">售后服务</a></li>
             <li><a href="#">资料下载</a></li>
             <li><a href="#">开源社区</a></li>`;
    } else {
        container.innerHTML = `<li><a href="#">技术支持</a></li>
                             <li><a href="#">售后服务</a></li>
                             <li><a href="#">资料下载</a></li>
                             <li><a href="#">开源社区</a></li>`;
    }
}

// 更新关注我们
function updateFollowUs(data) {
    const container = document.getElementById('follow_us_container');
    
    if (!container) return;

    if (data && data.length > 0) {
        // 按显示顺序排序
        const sortedItems = [...data].sort((a, b) => 
            (a.display_order || 100) - (b.display_order || 100)
        );
        
        // 记录最终生成的HTML，用于调试
        const generatedHtml = sortedItems.map(item => {
            // 获取图片路径（可能存储在多个字段中）
            let imgSrc = item.image_path || item.img_path || item.image || '';
            
            // 如果content中包含图片路径，则尝试提取
            if (!imgSrc && item.content && (item.content.includes('.jpg') || item.content.includes('.png') || item.content.includes('.gif'))) {
                const imgMatch = item.content.match(/(https?:\/\/[^"\s]+\.(jpg|jpeg|png|gif))/i);
                if (imgMatch) {
                    imgSrc = imgMatch[0];
                }
            }
            
            // 获取标题（尝试所有可能的字段）
            let title = '';
            
            // 优先使用name和title字段
            if (item.name) title = item.name;
            else if (item.title) title = item.title;
            else if (item.section_title) title = item.section_title;
            else if (item.label) title = item.label;
            
            // 如果仍然没有标题，尝试从内容中提取
            if (!title && item.content) {
                // 去除HTML标签，只保留文本
                const contentText = item.content.replace(/<[^>]+>/g, '');
                // 提取第一行作为标题
                title = contentText.split(/\n|<br\/?>/)[0].trim();
                // 如果内容太长，截断
                if (title.length > 30) {
                    title = title.substring(0, 30) + '...';
                }
            }
            
            // 如果还是没有标题，使用默认标题
            if (!title) {
                const defaultTitles = ['商务联系', '企业微信', '微信公众号', '淘宝商店'];
                const index = sortedItems.indexOf(item);
                title = index < defaultTitles.length ? defaultTitles[index] : '二维码';
            }
            
            // 处理图片路径
            imgSrc = normalizeImagePath(imgSrc);
            
            // 生成HTML
            return `
                <div class="qrcode-item">
                    <img src="${imgSrc}" 
                         alt="${title}" 
                         onerror="this.onerror=null; this.src='${pathPrefix}images/qrcode.jpg';">
                    <span class="qrcode-title">${title}</span>
                </div>`;
        }).join('');
        
        // 输出调试信息
        console.log('生成的二维码HTML:', generatedHtml);
        
        // 设置HTML内容
        container.innerHTML = generatedHtml;
    } else {
        // 默认显示四个二维码
        container.innerHTML = `
            <div class="qrcode-item">
                <img src="${pathPrefix}images/qrcode.jpg" alt="商务联系">
                <span class="qrcode-title">商务联系</span>
            </div>
            <div class="qrcode-item">
                <img src="${pathPrefix}images/qrcode.jpg" alt="企业微信">
                <span class="qrcode-title">企业微信</span>
            </div>
            <div class="qrcode-item">
                <img src="${pathPrefix}images/qrcode.jpg" alt="微信公众号">
                <span class="qrcode-title">微信公众号</span>
            </div>
            <div class="qrcode-item">
                <img src="${pathPrefix}images/qrcode.jpg" alt="淘宝商店">
                <span class="qrcode-title">淘宝商店</span>
            </div>`;
    }
}

// 更新产品方案
function updateProductSolution(data) {
    const container = document.getElementById('product_solution_container');
    const titleElement = document.getElementById('product_solution_title');
    const langConfig = getCurrentLangConfig();
    
    if (!container) return;
    
    // 更新标题
    if (titleElement) {
        let foundTitle = false;
        
        // 尝试从数据中找到第一个有标题的元素
        if (data && data.length > 0) {
            for (let i = 0; i < data.length; i++) {
                if (data[i].section_title || data[i].title) {
                    titleElement.textContent = data[i].section_title || data[i].title;
                    foundTitle = true;
                    break;
                }
            }
        }
        
        // 如果没有找到标题，使用默认标题
        if (!foundTitle) {
            titleElement.textContent = langConfig.products;
        }
    }
    
    // 更新内容
    if (data && data.length > 0) {
        // 按显示顺序排序
        const sortedItems = [...data].sort((a, b) => 
            (a.display_order || 100) - (b.display_order || 100)
        );
        
        const productItems = sortedItems.map(item => {
            // 优先使用item.url或item.link字段作为链接
            let url = item.url || item.link || '#';
            
            // 如果没有直接的url字段，尝试从content中解析
            if (url === '#' && item.content) {
                const content = item.content;
                // 尝试多种可能的链接格式
                const urlMatch = content.match(/链接:\s*(.*?)(?:\n|$)/) || 
                                content.match(/url:\s*(.*?)(?:\n|$)/i) || 
                                content.match(/link:\s*(.*?)(?:\n|$)/i);
                if (urlMatch) {
                    url = urlMatch[1].trim();
                }
            }
            
            // 获取名称
            let name = item.name || '';
            if (!name && item.content) {
                name = item.content.split(/\n|<br\/?>/)[0].replace(/链接:.*/, '').replace(/url:.*/, '').replace(/link:.*/, '').trim();
            }
            
            if (name) {
                return `<li><a href="${url}" ${url.includes('http') ? 'target="_blank"' : ''}>${name}</a></li>`;
            }
            return '';
        }).filter(Boolean);

        container.innerHTML = productItems.length > 0 ? 
            productItems.join('') : 
            langConfig.defaultProducts.map(product => 
                `<li><a href="${product.url}">${product.name}</a></li>`
            ).join('');
    } else {
        container.innerHTML = langConfig.defaultProducts.map(product => 
            `<li><a href="${product.url}">${product.name}</a></li>`
        ).join('');
    }
}

// 判断当前页面是否引用了 download.js
function isDownloadPage() {
    var scripts = document.getElementsByTagName('script');
    for (var i = 0; i < scripts.length; i++) {
        if (scripts[i].src && scripts[i].src.indexOf('download.js') !== -1) {
            return true;
        }
    }
    return false;
}

// 挂载initFooter到window，供外部调用
window.initFooter = initFooter;

// 只在未声明window.FOOTER_MANUAL_INIT，且不是product.html/product_en.html，且非download.js页面自动初始化footer
if (
  !window.FOOTER_MANUAL_INIT &&
  !window.location.pathname.endsWith('product.html') &&
  !window.location.pathname.endsWith('product_en.html') &&
  !isDownloadPage()
) {
    document.addEventListener('DOMContentLoaded', function() {
        const footerElement = document.querySelector('footer');
        if (!footerElement) return;
        if (footerElement.getAttribute('data-initialized') === 'true') return;
        if (typeof jQuery === 'undefined') return;
        initFooter();
    });
}
// 关闭立即执行函数
})(); 