// 工具栏初始化函数
function initFloatingToolbar() {
    // 检测是否在子目录中
    const isInSubdir = window.location.pathname.split('/').length > 2;
    const pathPrefix = isInSubdir ? '../' : '';

    // 创建工具栏SVG对象
    var toolbar = document.createElement('object');
    toolbar.type = "image/svg+xml";
    toolbar.data = `${pathPrefix}images/home/<USER>
    toolbar.id = "floatingToolbar";
    toolbar.className = "floating-toolbar";
    toolbar.width = "119";
    toolbar.height = "371";
    toolbar.innerHTML = "您的浏览器不支持SVG";
    document.body.appendChild(toolbar);

    // 创建二维码弹窗
    var qrcodePopup = document.createElement('div');
    qrcodePopup.className = 'qrcode-popup';
    qrcodePopup.innerHTML = `
        <div class="qrcode-container">
            <div class="qrcode-item">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='104' height='104' viewBox='0 0 104 104'%3E%3Crect width='104' height='104' fill='%23fff'/%3E%3C/svg%3E" alt="企业微信" class="qrcode-image">
            </div>
            <div class="qrcode-item">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='104' height='104' viewBox='0 0 104 104'%3E%3Crect width='104' height='104' fill='%23fff'/%3E%3C/svg%3E" alt="个人微信" class="qrcode-image">
            </div>
        </div>
    `;
    document.body.appendChild(qrcodePopup);

    // 创建电话咨询弹窗
    var phonePopup = document.createElement('div');
    phonePopup.className = 'phone-popup';
    phonePopup.innerHTML = `
        <div class="phone-title">微信客服</div>
        <div class="qrcode-wrapper">
            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='93' height='93' viewBox='0 0 93 93'%3E%3Crect width='93' height='93' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="微信客服" class="qrcode-image">
        </div>
        <div class="work-time">
            <p>工作时间</p>
            <p>周一至周六 9:00-18:00</p>
            <p>服务热线: 4001-511-533</p>
        </div>
    `;
    document.body.appendChild(phonePopup);

    // 获取轮播图元素
    var carousel = document.querySelector('.carousel-bg');
    
    // 如果找不到轮播图，使用页面顶部作为参考点
    var getCarouselBottomPosition = function() {
        if (carousel) {
            return carousel.getBoundingClientRect().bottom + window.scrollY;
        }
        return 0;
    };

    // 更新工具栏可见性
    function updateToolbarVisibility() {
        var scrollPos = window.scrollY;
        var carouselBottomPos = getCarouselBottomPosition();

        if (scrollPos >= carouselBottomPos) {
            toolbar.classList.add('fixed');
        } else {
            toolbar.classList.remove('fixed');
            qrcodePopup.classList.remove('show');
            phonePopup.classList.remove('show');
        }
    }

    // 监听滚动事件
    window.addEventListener('scroll', updateToolbarVisibility);

    // 初始检查位置
    setTimeout(updateToolbarVisibility, 100);

    // 窗口大小改变时重新计算位置
    window.addEventListener('resize', function() {
        updateToolbarVisibility();
    });

    // 监听SVG文档加载完成
    toolbar.addEventListener('load', function() {
        var svgDoc = toolbar.contentDocument;
        if (!svgDoc) {
            console.error('无法获取SVG文档');
            return;
        }

        var svgElement = svgDoc.querySelector('svg');
        if (!svgElement) {
            console.error('无法获取SVG元素');
            return;
        }

        // 获取SVG尺寸
        var svgRect = svgElement.getBoundingClientRect();
        var svgHeight = svgRect.height;
        var iconHeight = svgHeight / 3;

        // SVG点击事件
        svgElement.addEventListener('click', function(e) {
            var rect = svgElement.getBoundingClientRect();
            var clickY = e.clientY - rect.top;

            if (clickY <= iconHeight) {
                // 企业微信
                qrcodePopup.classList.toggle('show');
                phonePopup.classList.remove('show');
            } else if (clickY <= iconHeight * 2) {
                // 电话咨询
                phonePopup.classList.toggle('show');
                qrcodePopup.classList.remove('show');
            } else {
                // 在线客服
                qrcodePopup.classList.remove('show');
                phonePopup.classList.remove('show');
                // 这里可以添加在线客服的处理逻辑
            }
        });

        // 鼠标悬停效果
        svgElement.addEventListener('mousemove', function() {
            svgElement.style.cursor = 'pointer';
        });
    });

    // 点击页面其他地方关闭弹窗
    document.addEventListener('click', function(e) {
        var svgElement = toolbar.contentDocument?.querySelector('svg');
        if (!qrcodePopup.contains(e.target) && 
            !svgElement?.contains(e.target) && 
            qrcodePopup.classList.contains('show')) {
            qrcodePopup.classList.remove('show');
        }
        
        if (!phonePopup.contains(e.target) && 
            !svgElement?.contains(e.target) && 
            phonePopup.classList.contains('show')) {
            phonePopup.classList.remove('show');
        }
    });

    // 处理回到顶部按钮点击事件
    var scrollupBtn = document.querySelector('.scrollup');
    if (scrollupBtn) {
        scrollupBtn.addEventListener('click', function() {
            qrcodePopup.classList.remove('show');
            phonePopup.classList.remove('show');
            
            setTimeout(function() {
                if (!toolbar.classList.contains('fixed')) {
                    qrcodePopup.classList.remove('show');
                    phonePopup.classList.remove('show');
                }
            }, 500);
        });
    }
}

// 当DOM加载完成时初始化工具栏
document.addEventListener('DOMContentLoaded', initFloatingToolbar); 